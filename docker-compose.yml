services:
  db:
    image: mysql:latest
    container_name: mysql_db
    environment:
      MYSQL_ROOT_PASSWORD: pass
      MYSQL_DATABASE: tripleteadb
      MYSQL_USER: user
      MYSQL_PASSWORD: pass
    ports:
      - "6603:3306"
    volumes:
      - dbdata:/var/lib/mysql
    restart: always

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: php-my-admin
    environment:
      PMA_HOST: db
      PMA_PORT: 3306
      PMA_ARBITRARY: 1
    depends_on:
      - db
    ports:
      - "8080:80"
    restart: always

volumes:
  dbdata:
