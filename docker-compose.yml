services:
  db:
    image: mysql:latest
    container_name: mysql_db
    environment:
      MYSQL_ROOT_PASSWORD: pass
      MYSQL_DATABASE: tripleteadb
      MYSQL_USER: user
      MYSQL_PASSWORD: pass
    ports:
      - "6603:3306"
    volumes:
      - dbdata:/var/lib/mysql
    restart: always

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: php-my-admin
    environment:
      PMA_HOST: db
      PMA_PORT: 3306
      PMA_ARBITRARY: 1
    depends_on:
      - db
    ports:
      - "8080:80"
    restart: always

  db-init:
    image: mysql:latest
    depends_on:
      - db
    entrypoint: >
      sh -c "
        echo 'Waiting for <PERSON> to wake up...' &&
        until mysql -h db -u user -ppass -e 'SELECT 1;' tripleteadb; do
          echo 'DB not ready yet...';
          sleep 2;
        done &&
        echo 'DB is ready!'"
    restart: "no"

volumes:
  dbdata:
