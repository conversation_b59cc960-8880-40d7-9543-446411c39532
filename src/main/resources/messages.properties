# Application Name
app.name=TripleTea
app.copyright=© 2025 tripleTea

# Navigation
nav.users=Users
nav.customers=Customers
nav.quotes=Quotes
nav.tasks=Tasks
nav.change.password=Change Password
nav.logout=Logout

# Common Actions
action.add=Add
action.edit=Edit
action.delete=Delete
action.save=Save
action.cancel=Cancel
button.backToCustomerList=Back to Customer List
action.new=New
action.show.all=Show All
action.start=Start
action.finish=Finish
action.login=Login
action.search=Search
action.filter=Filter
action.clear=Clear

# Common Labels
label.title=Title
label.description=Description
label.name=Name
label.email=Email
label.password=Password
label.username=Username
label.created.at=Created At
label.updated.at=Updated At
label.status=Status
label.type=Type
label.subtype=Subtype
label.note=Note
label.actions=Actions

# Home Page
home.welcome=Welcome!
home.no.tasks=There are currently no tasks
home.tasks.title=Title
home.tasks.description=Description
home.tasks.planned.start=Planned Start
home.tasks.expected.finish=Expected Finish
home.tasks.finished.at=Finished

# Work Hours Chart
home.work.hours.chart.title=Work Hours by User (Weekly)
home.work.hours.chart.label=Hours
home.work.hours.chart.x.axis=Users
home.work.hours.chart.y.axis=Hours
home.work.hours.chart.prev.week=Previous Week
home.work.hours.chart.next.week=Next Week
home.work.hours.chart.current.week=Current Week

# Login Page
login.title=Login
login.username=Username
login.password=Password
login.submit=Login

# Customer Management
customer.list.title=Customer List
customer.add=Add Customer
customer.name=Name
customer.type=Customer Type
customer.subtype=Subtype
customer.company.name=Company Name
customer.responsible.person=Responsible Person
customer.company.individual=Company/Individual
customer.ico=ICO
customer.dic=DIC
customer.vat.number=VAT Number
customer.city=City
customer.country=Country
customer.contact.person=Contact Person
customer.contact.email=Contact Email
customer.contact.phone=Contact Phone
customer.tags=Tags
customer.invoice.due.days=Invoice Due Days
customer.number=Customer Number
customer.delete.confirm=Are you sure you want to delete this customer?

# Task Management
task.list.title=Task List
task.add=Add Task
task.title=Title
task.description=Description
task.executor=Executor
task.created.by=Created By
task.planned.start=Planned Start
task.planned.end=Planned End
task.started.at=Started At
task.end.at=Finished At
task.estimated.duration=Estimated Duration (hours)
task.actual.duration=Actual Duration (hours)
task.priority=Priority

# User Management
user.list.title=User List
user.add=Add User
user.first.name=First Name
user.last.name=Last Name
user.email=Email
user.enabled=Enabled
user.roles=Roles



# Business Events
event.list.title=Business Events
event.add=Add Event
event.type=Event Type
event.category=Category
event.direction=Direction
event.customer=Customer
event.user=User
event.scheduled.from=Scheduled From
event.scheduled.to=Scheduled To
event.realization.date=Realization Date

# Business Cases
case.list.title=Business Cases
case.add=Add Case
case.contact.source=Contact Source
case.number=Number
case.total.price=Total Price
case.responsible=Responsible
case.deal.type=Deal Type
case.deal.subtype=Deal Subtype
case.expected.closure=Expected Closure
case.closure.date=Closure Date
case.failure.reason=Failure Reason
case.failure.note=Failure Note

# Contracts
contract.list.title=Contracts
contract.add=Add Contract
contract.number=Number
contract.agreed.price=Agreed Price
contract.trade.margin=Trade Margin
contract.trade.margin.percent=Trade Margin %
contract.invoiced=Invoiced
contract.uninvoiced.payments=Uninvoiced Payments
contract.remaining.to.invoice=Remaining to Invoice
contract.estimated.duration=Estimated Duration
contract.actual.duration=Actual Duration
contract.completion.deadline=Completion Deadline
contract.completion.date=Completion Date
contract.delivery.date=Delivery Date
contract.overdue.days=Days Overdue
contract.business.case=Business Case

# Invoices
invoice.list.title=Invoices
invoice.add=Add Invoice
invoice.number=Number
invoice.issue.date=Issue Date
invoice.delivery.date=Delivery Date
invoice.due.date=Due Date
invoice.payment.date=Payment Date
invoice.partner.name=Partner Name
invoice.partner.number=Partner Number
invoice.total=Total
invoice.total.without.vat=Total without VAT
invoice.currency=Currency
invoice.payment.method=Payment Method

# Products
product.list.title=Price List
product.add=Add Product
product.number=Number
product.unit=Unit
product.selling.price.without.vat=Selling Price (without VAT)
product.selling.price.with.vat=Selling Price (with VAT)
product.buying.price.without.vat=Buying Price (without VAT)
product.buying.price.with.vat=Buying Price (with VAT)
product.vat.rate=VAT Rate
product.trade.margin=Trade Margin
product.manufacturer=Manufacturer
product.category=Category
product.subcategory=Subcategory

# Validation Messages
validation.required=This field is required
validation.email.invalid=Please enter a valid email
validation.password.min.length=Password must be at least 6 characters
validation.number.invalid=Please enter a valid number
validation.date.invalid=Please enter a valid date

# Error Messages
error.general=An error occurred. Please try again.
error.not.found=Requested resource not found.
error.access.denied=Access denied.
error.invalid.credentials=Invalid username or password.

# Success Messages
success.saved=Successfully saved.
success.deleted=Successfully deleted.
success.updated=Successfully updated.
success.created=Successfully created.

# Confirmation Messages
confirm.delete=Are you sure you want to delete this item?
confirm.action=Are you sure you want to perform this action?

# Language
language.english=English
language.slovak=Slovak
language.switch=Switch Language

# --- User Labels ---
user.delete.confirm=Are you sure you want to delete this user?

# --- UserRoles Labels ---
userroles.list.title=User Roles List
userroles.add=Add User Role
userroles.userId=User ID
userroles.roleId=Role ID
userroles.delete.confirm=Are you sure you want to delete this user role?

# General Labels for Address Form
label.street=Street
label.zip=ZIP Code
label.city=City
label.countryCode=Country Code
label.country=Country

# Buttons
button.save=Save
button.cancel=Cancel
button.edit=Edit
button.delete=Delete
button.add=Add

# Page Titles
title.userRolesList=User Roles List
title.userList=User List
title.roleList=Role List
title.taskForm=Task Form
title.editTask=Edit Task
title.createTask=Create Task
title.editRole=Edit Role
title.createRole=Create Role

# Form Labels
label.firstName=First Name
label.lastName=Last Name
label.roles=Roles
label.plannedStart=Planned Start
label.plannedEnd=Planned End
label.estimatedDurationHours=Estimated Duration (hours)
label.priority=Priority
label.executor=Responsible Person
label.id=ID

customerTypes.list.title=Customer Types List
customerTypes.add=Add Customer Type
customerTypes.actions=Actions

customerType.edit.title=Edit Customer Type
customerType.create.title=Create Customer Type

customer.edit.title=Edit Customer
customer.create.title=Create Customer
label.billingName=Billing Name
label.companyOrIndividual=Company or Individual
select.chooseType=Choose type
label.customerType=Customer Type
select.chooseSubtype=Choose subtype
label.customerSubtype=Customer Subtype
label.ico=ICO
label.dic=DIC
label.icDph=IC DPH
label.responsiblePerson=Responsible Person
select.choosePerson=Choose person
label.tags=Tags
label.invoiceDueDays=Invoice Due Days
label.customerNumber=Customer Number
label.billingAddress=Billing Address
label.correspondenceAddress=Correspondence Address
label.deliveryAddress=Delivery Address

contract.id=ID
contract.actualDuration=Actual Duration
contract.agreedPrice=Agreed Price
contract.businessCase=Business Case
contract.completionDate=Completion Date
contract.completionDeadline=Completion Deadline
contract.createdAt=Created At
contract.deliveryDate=Delivery Date
contract.descriptionAndRequirements=Description & Requirements
contract.estimatedDuration=Estimated Duration
contract.name=Name
contract.overdueDays=Overdue Days
contract.remainingToInvoice=Remaining To Invoice
contract.responsible=Responsible
contract.status=Status
contract.subtype=Subtype
contract.tradeMargin=Trade Margin
contract.tradeMarginPercent=Trade Margin (%)
contract.type=Type
contract.uninvoicedPayments=Uninvoiced Payments
contract.customerId=Customer ID
contract.actions=Actions
button.addContract=Add Contract
confirm.deleteContract=Are you sure you want to delete this contract?

contract.editTitle=Edit Contract
contract.createTitle=Create Contract
form.error.fix=Fix errors in form

contact.listTitle=Contact List
contact.add=Add Contact
contact.id=ID
contact.email=Email
contact.firstName=First Name
contact.lastName=Last Name
contact.mainContact=Main Contact
contact.newsletterSubscribed=Newsletter Subscribed
contact.note=Note
contact.otherContact=Other Contact
contact.phone1=Phone 1
contact.phone2=Phone 2
contact.position=Position
contact.suffix=Suffix
contact.title=Title
contact.customerId=Customer ID
actions=Actions

confirm.deleteContact=Are you sure you want to delete this contact?

businesscase.listTitle=BusinessCase List
businesscase.add=Add BusinessCase
businesscase.id=ID
businesscase.closureDate=Closure Date
businesscase.contactSource=Contact Source
businesscase.createdAt=Created At
businesscase.dealSubtype=Deal Subtype
businesscase.dealType=Deal Type
businesscase.description=Description
businesscase.expectedClosureDate=Expected Closure Date
businesscase.failureNote=Failure Note
businesscase.failureReason=Failure Reason
businesscase.name=Name
businesscase.number=Number
businesscase.responsible=Responsible
businesscase.status=Status
businesscase.totalPrice=Total Price
businesscase.customerId=Customer ID

confirm.deleteBusinesscase=Are you sure you want to delete this business case?

businessEvent.editTitle=Edit Business Event
businessEvent.createTitle=Create Business Event
businessEvent.eventType=Event Type
businessEvent.eventCategory=Event Category
businessEvent.direction=Direction
businessEvent.scheduledFrom=Scheduled From
businessEvent.scheduledTo=Scheduled To
businessEvent.realizationDate=Realization Date
businessEvent.dealNumber=Deal Number
businessEvent.department=Department
businessEvent.note=Note
businessEvent.customer=Customer
businessEvent.user=User
businessEvent.businessCase=Business Case

businessEvents.title=Business Events for {0}
businessEvents.heading=Business Events
businessEvents.add=Add Business Event
businessEvents.addForCustomer=Add Business Event for {0}
businessEvents.createdAt=Created At
businessEvents.eventType=Event Type
businessEvents.category=Category
businessEvents.direction=Direction
businessEvents.customer=Customer
businessEvents.user=User
businessEvents.scheduledFrom=Scheduled From
businessEvents.scheduledTo=Scheduled To
businessEvents.realizationDate=Realization Date
businessEvents.note=Note
businessEvents.actions=Actions
businessEvents.noRecords=No business events found
businessEvents.confirmDelete=Are you sure you want to delete this event?
businessEvents.edit=Edit
businessEvents.delete=Delete

# Customer events
customer.events=Customer Events
customer.event.history=Event History
customer.add.event=Add Event

# Tags
tag.list.title=Tag List
tag.add=Add Tag
tag.name=Tag Name
tag.color=Color
tag.customers.count=Customers Count
tag.edit.title=Edit Tag
tag.create.title=Create Tag
tag.delete.confirm=Are you sure you want to delete this tag?
tag.color.help=Hex color code (e.g. #563d7c)

# Tasks
task.user=Responsible User
task.user.select=Choose person

# Invoices
invoice.id=ID
invoice.number=Invoice Number
invoice.number.auto.generated=Invoice number will be automatically generated when saved
invoice.accountNumber=Account Number
invoice.category=Category
invoice.company=Company
invoice.issueDate=Issue Date
invoice.user=Responsible User
invoice.user.select=Choose user
invoice.status=Status
invoice.status.select=Choose status
invoice.realizationDate=Realization Date
invoice.note=Note
invoice.internalNote=Internal Note
invoice.edit.title=Edit Invoice
invoice.create.title=Create Invoice
invoice.customer=Customer
invoice.customer.select=Choose customer

# Customer actions
customer.invoice.create=Create Invoice

# Status translations
status.planned=Planned
status.in_progress=In Progress
status.finished=Finished
status.archived=Archived
status.draft=Draft
status.sent=Sent
status.accepted=Accepted
status.rejected=Rejected
status.expired=Expired
status.cancelled=Cancelled

# Customer actions
customer.quote.create=Create Quote
customer.quote.list=View Quotes

# Quotes
quote.list.title=Quote List
quote.add=Add Quote
quote.id=ID
quote.number=Number
quote.company=Company
quote.issueDate=Issue Date
quote.validUntilDate=Valid Until Date
quote.user=Responsible User
quote.user.select=Choose user
quote.status=Status
quote.status.select=Choose status
quote.realizationDate=Realization Date
quote.note=Note
quote.internalNote=Internal Note
quote.edit.title=Edit Quote
quote.create.title=Create Quote
quote.customer=Customer
quote.customer.select=Choose customer
quote.total=Total
quote.currency=Currency
quote.currency.select=Choose currency
quote.orderDeliveryDate=Order Delivery Date
quote.deliveryDate=Delivery Date
quote.download=Download PDF
quote.preview=Preview Quote
quote.customer.list.title=Quotes for Customer
quote.customer.noRecords=No quotes found for this customer
quote.noRecords=No quotes found
quote.delete.confirm=Are you sure you want to delete this quote?

button.backToCustomer=Back to Customer
form.errorsFix=Please fix the errors below

bankaccount.list.title=BankAccount List
bankaccount.list.heading=BankAccount List
bankaccount.list.add=Add BankAccount

bankaccount.id=ID
bankaccount.bankCode=Bank Code
bankaccount.iban=IBAN
bankaccount.number=Number
bankaccount.prefix=Prefix
bankaccount.swift=SWIFT
bankaccount.customerId=Customer ID
bankaccount.actions=Actions
bankaccount.edit=Edit
bankaccount.delete=Delete
bankaccount.confirmDelete=Are you sure you want to delete this bankaccount?

bankaccount.form.createTitle=Create BankAccount
bankaccount.form.editTitle=Edit BankAccount
bankaccount.form.fixErrors=Fix errors in form
bankaccount.form.save=Save
bankaccount.form.cancel=Cancel

address.list.title=Address List
address.list.heading=Address List
address.list.add=Add Address

address.id=ID
address.city=City
address.country=Country
address.countryCode=Country Code
address.street=Street
address.zip=ZIP
address.actions=Actions
address.edit=Edit
address.delete=Delete
address.confirmDelete=Are you sure you want to delete this address?

address.form.createTitle=Create Address
address.form.editTitle=Edit Address
address.form.fixErrors=Fix errors in form
address.form.save=Save
address.form.cancel=Cancel

user.change.password.title=Change Password
user.current.password=Current Password
user.new.password=New Password
user.confirm.new.password=Confirm New Password
user.change.password.button=Change Password
select.chooseTags=Choose tags

task.create.title=Create Task
task.edit.title=Edit Task
task.executor.select=Select executor
action.tags=Tags
app.version=Version :
action.back=Back
