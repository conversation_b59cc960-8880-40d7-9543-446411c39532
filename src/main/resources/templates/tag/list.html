<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:lang="${#locale.language}"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout}">
<head>
    <title th:text="#{tag.list.title}">Tag List</title>
</head>

<section layout:fragment="content">
    <h2 th:text="#{tag.list.title}">Tag List</h2>
    <div class="mb-3">
        <a th:href="@{/tags/new}" class="btn btn-primary me-2" th:text="#{tag.add}">Add Tag</a>
        <a th:href="@{/customers}" id="backToCustomerListBtn" class="btn btn-outline-secondary" th:text="#{button.backToCustomerList}">Back to Customer List</a>
    </div>

    <table class="table table-bordered table-striped">
        <thead>
        <tr>
            <th th:text="#{label.id}">ID</th>
            <th th:text="#{tag.name}">Name</th>
            <th th:text="#{tag.customers.count}">Customers Count</th>
            <th th:text="#{label.actions}">Actions</th>
        </tr>
        </thead>
        <tbody>
        <tr th:each="tag : ${tags}">
            <td th:text="${tag.id}">id</td>
            <td><span class="badge"
                      th:style="'background-color:' + ${tag.color ?: '#6c757d'}"
                      th:text="${tag.name}"></span>
            </td>
            <td th:text="${tag.customers.size()}">customers count</td>
            <td>
                <a th:href="@{'/tags/edit/' + ${tag.id}}" class="btn btn-sm btn-secondary" th:text="#{button.edit}">Edit</a>
                <form th:action="@{'/tags/delete/' + ${tag.id}}" method="post" style="display:inline;">
                    <input type="hidden" name="_method" value="delete"/>
                    <button type="submit" class="btn btn-sm btn-danger"
                            th:onclick="'return confirm(\'' + #{tag.delete.confirm} + '\');'"
                            th:text="#{button.delete}">Delete
                    </button>
                </form>
            </td>
        </tr>
        </tbody>
    </table>
</section>

<script>
    // Function to get cookie value
    function getCookie(name) {
        const nameEQ = name + '=';
        const ca = document.cookie.split(';');
        for (let i = 0; i < ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) return decodeURIComponent(c.substring(nameEQ.length, c.length));
        }
        return null;
    }

    // Function to build customer list URL with saved filters
    function buildCustomerListUrl() {
        const filters = {
            nameFilter: getCookie('customer_nameFilter') || '',
            icoFilter: getCookie('customer_icoFilter') || '',
            cityFilter: getCookie('customer_cityFilter') || '',
            countryFilter: getCookie('customer_countryFilter') || '',
            typeFilter: getCookie('customer_typeFilter') || '',
            subtypeFilter: getCookie('customer_subtypeFilter') || '',
            responsiblePersonFilter: getCookie('customer_responsiblePersonFilter') || '',
            page: getCookie('customer_page') || '0',
            size: getCookie('customer_size') || '20',
            sortBy: getCookie('customer_sortBy') || 'name',
            sortDir: getCookie('customer_sortDir') || 'asc'
        };

        // Handle tagFilters
        const savedTagFilters = getCookie('customer_tagFilters');
        let tagFilters = [];
        if (savedTagFilters) {
            try {
                tagFilters = JSON.parse(savedTagFilters);
            } catch (e) {
                console.error('Error parsing saved tag filters:', e);
            }
        }

        // Build URL with non-empty filters
        const url = new URL('/customers', window.location.origin);
        Object.keys(filters).forEach(key => {
            if (filters[key] && filters[key] !== '') {
                url.searchParams.set(key, filters[key]);
            }
        });

        // Add tag filters
        if (tagFilters.length > 0) {
            tagFilters.forEach(tagId => {
                url.searchParams.append('tagFilters', tagId);
            });
        }

        return url.toString();
    }

    // Set up back button when page loads
    document.addEventListener('DOMContentLoaded', function() {
        const backBtn = document.getElementById('backToCustomerListBtn');
        if (backBtn) {
            try {
                const urlWithFilters = buildCustomerListUrl();
                backBtn.href = urlWithFilters;
                console.log('Back button URL set to:', urlWithFilters);
            } catch (error) {
                console.error('Error setting up back button:', error);
                // Fallback to basic customer list URL
                backBtn.href = '/customers';
            }
        }
    });
</script>
</html>
