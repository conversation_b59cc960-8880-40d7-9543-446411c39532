<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:lang="${#locale.language}"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout}">
<head>
    <meta charset="UTF-8">
    <title th:text="${tag != null and tag.id != null} ? #{tag.edit.title} : #{tag.create.title}">Tag Form</title>
</head>

<body>
<section layout:fragment="content">
    <div class="container">
        <form th:action="@{/tags/save}" th:object="${tag}" method="post">

            <div th:if="${#fields.hasErrors('*')}" class="alert alert-danger" th:text="#{form.error.fix}">
                Fix errors in form
            </div>

            <h2 th:text="*{id} != null ? #{tag.edit.title} : #{tag.create.title}">Tag Form</h2>

            <input type="hidden" th:field="*{id}" />

            <div class="mb-3">
                <label for="name" class="form-label" th:text="#{tag.name}">Tag Name</label>
                <input type="text" th:field="*{name}" class="form-control" id="name" required />
                <div class="text-danger" th:if="${#fields.hasErrors('name')}" th:errors="*{name}"></div>
            </div>

            <div class="mb-3">
                <label for="color" class="form-label" th:text="#{tag.color}">Color</label>
                <div class="input-group">
                    <input type="color"
                           th:value="${tag.color != null} ? ${tag.color} : '#563d7c'"
                           class="form-control form-control-color"
                           id="colorPicker"
                           oninput="document.getElementById('color').value=this.value">
                    <input type="text"
                           th:field="*{color}"
                           class="form-control"
                           id="color"
                           pattern="^#[0-9A-Fa-f]{6}$"
                           placeholder="#RRGGBB"/>
                </div>
                <small class="form-text text-muted" th:text="#{tag.color.help}">Hex color code (e.g. #563d7c)</small>
                <div class="text-danger" th:if="${#fields.hasErrors('color')}" th:errors="*{color}"></div>
            </div>

            <button type="submit" class="btn btn-success" th:text="#{button.save}">Save</button>
            <a th:href="@{/tags}" class="btn btn-secondary me-2" th:text="#{button.cancel}">Cancel</a>
            <a th:href="@{/customers}" id="backToCustomerListBtn" class="btn btn-outline-secondary" th:text="#{button.backToCustomerList}">Back to Customer List</a>
        </form>

        <!-- Migration Section - Only show when editing existing tag -->
        <div th:if="${tag.id != null and availableTags != null and !availableTags.isEmpty()}" class="mt-5">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span th:text="#{tag.migration.title}">Migrácia menovky</span>
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">
                        <span th:text="#{tag.migration.description}">Táto akcia presunie všetkých zákazníkov z tejto menovky na inú existujúcu menovku a následne túto menovku vymaže.</span>
                        <strong th:text="#{tag.migration.warning}">Táto akcia je nevratná!</strong>
                    </p>

                    <form th:action="@{'/tags/migrate/' + ${tag.id}}" method="post"
                          th:attr="onsubmit='return confirm(\'' + #{tag.migration.confirm} + '\');'">
                        <div class="row align-items-end">
                            <div class="col-md-8">
                                <label for="targetTagId" class="form-label" th:text="#{tag.migration.select.target}">Vyberte cieľovú menovku:</label>
                                <select name="targetTagId" id="targetTagId" class="form-select" required>
                                    <option value="" th:text="#{tag.migration.select.placeholder}">-- Vyberte menovku --</option>
                                    <option th:each="availableTag : ${availableTags}"
                                            th:value="${availableTag.id}"
                                            th:text="${availableTag.name}">
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-warning w-100">
                                    <i class="fas fa-exchange-alt"></i>
                                    <span th:text="#{tag.migration.button}">Migrovať a vymazať</span>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const colorField = document.getElementById('color');
            const colorPicker = document.getElementById('colorPicker');

            if(colorField.value) {
                colorPicker.value = colorField.value;
            }

            colorPicker.addEventListener('input', function() {
                colorField.value = this.value;
            });

            // Update color picker when text field changes
            colorField.addEventListener('input', function() {
                if(this.value.match(/^#[0-9A-Fa-f]{6}$/)) {
                    colorPicker.value = this.value;
                }
            });

            // Set up back button with saved filters
            setupBackButton();
        });

        // Function to get cookie value
        function getCookie(name) {
            const nameEQ = name + '=';
            const ca = document.cookie.split(';');
            for (let i = 0; i < ca.length; i++) {
                let c = ca[i];
                while (c.charAt(0) === ' ') c = c.substring(1, c.length);
                if (c.indexOf(nameEQ) === 0) return decodeURIComponent(c.substring(nameEQ.length, c.length));
            }
            return null;
        }

        // Function to build customer list URL with saved filters
        function buildCustomerListUrl() {
            const filters = {
                nameFilter: getCookie('customer_nameFilter') || '',
                icoFilter: getCookie('customer_icoFilter') || '',
                cityFilter: getCookie('customer_cityFilter') || '',
                countryFilter: getCookie('customer_countryFilter') || '',
                typeFilter: getCookie('customer_typeFilter') || '',
                subtypeFilter: getCookie('customer_subtypeFilter') || '',
                responsiblePersonFilter: getCookie('customer_responsiblePersonFilter') || '',
                page: getCookie('customer_page') || '0',
                size: getCookie('customer_size') || '20',
                sortBy: getCookie('customer_sortBy') || 'name',
                sortDir: getCookie('customer_sortDir') || 'asc'
            };

            // Handle tagFilters
            const savedTagFilters = getCookie('customer_tagFilters');
            let tagFilters = [];
            if (savedTagFilters) {
                try {
                    tagFilters = JSON.parse(savedTagFilters);
                } catch (e) {
                    console.error('Error parsing saved tag filters:', e);
                }
            }

            // Build URL with non-empty filters
            const url = new URL('/customers', window.location.origin);
            Object.keys(filters).forEach(key => {
                if (filters[key] && filters[key] !== '') {
                    url.searchParams.set(key, filters[key]);
                }
            });

            // Add tag filters
            if (tagFilters.length > 0) {
                tagFilters.forEach(tagId => {
                    url.searchParams.append('tagFilters', tagId);
                });
            }

            return url.toString();
        }

        // Set up back button
        function setupBackButton() {
            const backBtn = document.getElementById('backToCustomerListBtn');
            if (backBtn) {
                backBtn.href = buildCustomerListUrl();
            }
        }
    </script>
</section>
</body>
</html>
