<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:lang="${#locale.language}"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout}">
<head>
    <title th:text="#{home.welcome}">Home</title>
    <style>
        .table .task-not-started,
        .table .task-not-started > td {
            --bs-table-bg: #f8f9fa !important;
            background-color: #f8f9fa !important; /* Light gray background */
        }
        .table .task-started,
        .table .task-started > td {
            --bs-table-bg: #d4edda !important;
            background-color: #d4edda !important; /* Light green background */
        }

        /* Additional specificity to override Bootstrap table striping */
        .table-striped .task-not-started,
        .table-striped .task-not-started > td {
            --bs-table-accent-bg: #f8f9fa !important;
            background-color: #f8f9fa !important;
        }
        .table-striped .task-started,
        .table-striped .task-started > td {
            --bs-table-accent-bg: #d4edda !important;
            background-color: #d4edda !important;
        }
    </style>
</head>

<section layout:fragment="content">
    <h2 th:text="#{home.welcome}">Welcome!</h2>

    <div>
        <div th:if="${tasks.isEmpty()}"><h3 th:text="#{home.no.tasks}">There are currently no tasks</h3></div>
        <table th:if="${!tasks.isEmpty()}" class="table table-striped">
            <thead>
            <tr>
                <th th:text="#{home.tasks.title}">Title</th>
                <th th:text="#{home.tasks.description}">Description</th>
                <th th:text="#{home.tasks.customer}">Customer</th>
                <th th:text="#{home.tasks.status}">Status</th>
                <th th:text="#{home.tasks.active.duration}">Active Duration</th>
                <th th:text="#{home.tasks.finished.at}">Finished At</th>
                <th th:text="#{label.actions}">Actions</th>
            </tr>
            </thead>
            <tbody>
            <tr th:each="task : ${tasks}"
                th:class="${task.startedAt == null ? 'task-not-started' : (task.isCurrentlyPaused ? 'task-paused' : 'task-started')}">
                <td th:text="${task.title}"></td>
                <td th:text="${task.description}"></td>
                <td th:text="${task.customer?.name}"></td>
                <td>
                    <span th:if="${task.isCurrentlyPaused}" class="badge bg-warning">Paused</span>
                    <span th:if="${task.isCurrentlyRunning()}" class="badge bg-success">Running</span>
                    <span th:if="${task.isFinished()}" class="badge bg-secondary">Finished</span>
                    <span th:if="${task.startedAt == null}" class="badge bg-light text-dark">Not Started</span>
                </td>
                <td th:text="${#numbers.formatDecimal(task.getTotalActiveDurationHours(), 1, 'COMMA', 2, 'POINT')} + ' h'"></td>
                <td th:text="${task.realizationDate != null ? #temporals.format(task.realizationDate, 'yyyy-MM-dd HH:mm') : ''}"></td>
                <td>
                    <!-- Start button - only show if task hasn't started -->
                    <form th:if="${task.startedAt == null}"
                          th:action="@{/tasks/{id}/start(id=${task.id})}"
                          method="post"
                          style="display:inline">
                        <button type="submit" class="btn btn-sm btn-primary" th:text="#{action.start}">Start</button>
                    </form>

                    <!-- Pause button - only show if task is running (started but not paused and not finished) -->
                    <form th:if="${task.isCurrentlyRunning()}"
                          th:action="@{/tasks/{id}/pause(id=${task.id})}"
                          method="post"
                          style="display:inline">
                        <button type="submit" class="btn btn-sm btn-warning" th:text="#{action.pause}">Pause</button>
                    </form>

                    <!-- Resume button - only show if task is paused -->
                    <form th:if="${task.isCurrentlyPaused}"
                          th:action="@{/tasks/{id}/resume(id=${task.id})}"
                          method="post"
                          style="display:inline">
                        <button type="submit" class="btn btn-sm btn-info" th:text="#{action.resume}">Resume</button>
                    </form>

                    <!-- Finish button - only show if task is started and not finished -->
                    <form th:if="${task.startedAt != null and task.endedAt == null}"
                          th:action="@{/tasks/{id}/finish(id=${task.id})}"
                          method="post"
                          style="display:inline">
                        <button type="submit" class="btn btn-sm btn-success" th:text="#{action.finish}">Finish</button>
                    </form>
                </td>
            </tr>
            </tbody>
        </table>
        <a th:href="@{/tasks/new}" class="btn btn-sm btn-primary" th:text="#{action.new}">New</a>
        <a th:href="@{/tasks}" class="btn btn-sm btn-primary" th:text="#{action.show.all}">Show All</a>
    </div>

    <!-- Charts Section -->
    <div class="row g-4 mb-4" style="margin-top: 2rem;">
        <!-- Weekly Work Hours Chart -->
        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0" th:text="#{home.work.hours.chart.title}">Odpracované hodiny podľa používateľov</h5>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary btn-sm" id="prevWeekBtn">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" id="currentWeekBtn">
                            Aktuálny týždeň
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" id="nextWeekBtn">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="weekTitle" class="text-center mb-3">
                        <h6 class="text-muted">Načítavam...</h6>
                    </div>

                    <!-- User Toggle Controls -->
                    <div id="userControls" class="mb-3" style="display: none;">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <small class="text-muted">Zobraziť/skryť používateľov:</small>
                            <div>
                                <button type="button" class="btn btn-outline-secondary btn-sm" id="showAllUsersBtn">
                                    Zobraziť všetkých
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" id="hideAllUsersBtn">
                                    Skryť všetkých
                                </button>
                            </div>
                        </div>
                        <div id="userToggleButtons" class="d-flex flex-wrap gap-1">
                            <!-- User toggle buttons will be dynamically added here -->
                        </div>
                    </div>

                    <div id="chartContainer">
                        <div id="chartLoading" class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Načítavam údaje o odpracovaných hodinách...</p>
                        </div>
                        <canvas id="workHoursChart" width="400" height="200" style="display: none;"></canvas>
                        <div id="chartError" class="text-center text-muted" style="display: none;">
                            <p>Zatiaľ nie sú k dispozícii žiadne údaje o odpracovaných hodinách pre tento týždeň.</p>
                            <small>Údaje sa zobrazia po dokončení úloh s vyplnenými skutočnými hodinami.</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Work Hours Chart -->
        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0" th:text="#{home.customer.work.hours.chart.title}">Odpracované hodiny podľa zákazníkov</h5>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary btn-sm" id="customerPrevWeekBtn">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" id="customerNextWeekBtn">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="customerWeekTitle" class="text-center mb-3">
                        <!-- Week title will be dynamically updated -->
                    </div>

                    <!-- User Controls for Customer Chart -->
                    <div id="customerUserControls" class="mb-3" style="display: none;">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <small class="text-muted">Filtrovanie používateľov:</small>
                            <div>
                                <button type="button" class="btn btn-outline-secondary btn-sm" id="customerShowAllUsersBtn">
                                    Zobraziť všetkých používateľov
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" id="customerHideAllUsersBtn">
                                    Skryť všetkých používateľov
                                </button>
                            </div>
                        </div>
                        <div id="customerUserToggleButtons" class="d-flex flex-wrap gap-1">
                            <!-- User toggle buttons will be dynamically added here -->
                        </div>
                    </div>

                    <div id="customerChartContainer">
                        <div id="customerChartLoading" class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Načítavam údaje o odpracovaných hodinách podľa zákazníkov...</p>
                        </div>
                        <canvas id="customerWorkHoursChart" width="400" height="200" style="display: none;"></canvas>
                        <div id="customerChartError" class="text-center text-muted" style="display: none;">
                            <p>Zatiaľ nie sú k dispozícii žiadne údaje o odpracovaných hodinách podľa zákazníkov pre tento týždeň.</p>
                            <small>Údaje sa zobrazia po dokončení úloh s vyplnenými skutočnými hodinami a priradeným zákazníkom.</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Information Section -->
    <div class="row g-4 mb-4">
        <!-- Google Maps -->
        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title mb-0">Naša poloha</h5>
                </div>
                <div class="card-body p-0">
                    <iframe
                        src="https://www.google.com/maps?q=Kosice,Učňovská 575/8,Slovakia&output=embed"
                        width="100%"
                        height="400"
                        style="border:0;"
                        allowfullscreen=""
                        loading="lazy"
                        referrerpolicy="no-referrer-when-downgrade">
                    </iframe>
                </div>
            </div>
        </div>

        <!-- Exchange Rates -->
        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Aktuálne kurzy EUR</h5>
                    <button type="button" class="btn btn-outline-primary btn-sm" id="refreshRatesBtn">
                        <i class="fas fa-sync-alt"></i> Obnoviť
                    </button>
                </div>
                <div class="card-body">
                    <div id="exchangeRatesLoading" class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Načítavam kurzy...</p>
                    </div>
                    <div id="exchangeRatesError" class="text-center text-muted" style="display: none;">
                        <p>Nepodarilo sa načítať aktuálne kurzy.</p>
                    </div>
                    <div id="exchangeRatesContainer" style="display: none;">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Mena</th>
                                        <th>Kód</th>
                                        <th class="text-end">Kurz</th>
                                        <th class="text-end">Zmena</th>
                                    </tr>
                                </thead>
                                <tbody id="exchangeRatesTable">
                                    <!-- Exchange rates will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                        <small class="text-muted">Posledná aktualizácia: <span id="lastUpdated"></span></small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<th:block layout:fragment="scripts">
    <script>
        let currentWeekStart = null;
        let currentChart = null;

        // Function to fetch weekly chart data and render the chart
        async function loadWeeklyWorkHoursChart(weekStart = null) {
            const loadingElement = document.getElementById('chartLoading');
            const chartCanvas = document.getElementById('workHoursChart');
            const errorElement = document.getElementById('chartError');
            const weekTitleElement = document.getElementById('weekTitle');

            // Show loading
            loadingElement.style.display = 'block';
            chartCanvas.style.display = 'none';
            errorElement.style.display = 'none';

            try {
                console.log('Fetching weekly work hours data for week:', weekStart);

                // Build URL with optional week parameter
                let url = '/api/charts/weekly-work-hours';
                if (weekStart) {
                    url += `?weekStart=${weekStart}`;
                }

                // Fetch data from the API endpoint
                const response = await fetch(url);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log('Received weekly data:', data);

                // Update current week start for navigation
                currentWeekStart = data.weekStart;

                // Update week title
                weekTitleElement.innerHTML = `<h6 class="text-muted">${data.weekTitle}</h6>`;

                // Hide loading spinner
                loadingElement.style.display = 'none';

                // Check if we have data
                if (!data.success || !data.datasets || data.datasets.length === 0) {
                    console.warn('No chart data available for this week');
                    errorElement.style.display = 'block';
                    return;
                }

                // Show chart canvas and create the chart
                chartCanvas.style.display = 'block';
                createChart(data.datasets, data.weekTitle, data.canViewAllUsers, data.userNames);

            } catch (error) {
                console.error('Error fetching weekly chart data:', error);
                loadingElement.style.display = 'none';
                errorElement.style.display = 'block';
                errorElement.innerHTML = '<p class="text-danger">Chyba pri načítavaní údajov o odpracovaných hodinách.</p>';
            }
        }

        // Function to create the chart
        function createChart(datasets, weekTitle, canViewAllUsers, userNames) {
            console.log('Creating weekly chart with', datasets.length, 'users for', weekTitle);

            // Destroy existing chart if it exists
            if (currentChart) {
                currentChart.destroy();
            }

            // Show/hide user controls based on permissions and data
            const userControlsElement = document.getElementById('userControls');
            if (canViewAllUsers && datasets.length > 1) {
                userControlsElement.style.display = 'block';
                createUserToggleButtons(datasets);
            } else {
                userControlsElement.style.display = 'none';
            }

            // Create the chart using bar type with user visibility control
            const ctx = document.getElementById('workHoursChart').getContext('2d');
            currentChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: datasets.map(dataset => dataset.label), // User names as labels
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Hodiny'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Používatelia'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            onClick: function(e, legendItem, legend) {
                                // Toggle dataset visibility
                                const index = legendItem.datasetIndex;
                                const chart = legend.chart;
                                const meta = chart.getDatasetMeta(index);

                                meta.hidden = meta.hidden === null ? !chart.data.datasets[index].hidden : null;
                                chart.update();

                                // Update toggle button state
                                updateToggleButtonState(index, meta.hidden);
                            }
                        },
                        title: {
                            display: true,
                            text: weekTitle
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': ' + context.parsed.y + ' hodín';
                                }
                            }
                        }
                    }
                }
            });
        }

        // Function to create user toggle buttons
        function createUserToggleButtons(datasets) {
            const container = document.getElementById('userToggleButtons');
            container.innerHTML = ''; // Clear existing buttons

            datasets.forEach((dataset, index) => {
                const button = document.createElement('button');
                button.type = 'button';
                button.className = 'btn btn-sm btn-outline-primary user-toggle-btn';
                button.dataset.index = index;
                button.innerHTML = `
                    <span class="color-indicator" style="background-color: ${dataset.backgroundColor}; width: 12px; height: 12px; display: inline-block; border-radius: 50%; margin-right: 5px;"></span>
                    ${dataset.label}
                `;

                button.addEventListener('click', function() {
                    toggleUser(index);
                });

                container.appendChild(button);
            });
        }

        // Function to toggle user visibility
        function toggleUser(index) {
            if (currentChart) {
                const meta = currentChart.getDatasetMeta(index);
                meta.hidden = meta.hidden === null ? !currentChart.data.datasets[index].hidden : null;
                currentChart.update();
                updateToggleButtonState(index, meta.hidden);
            }
        }

        // Function to update toggle button state
        function updateToggleButtonState(index, isHidden) {
            const button = document.querySelector(`[data-index="${index}"]`);
            if (button) {
                if (isHidden) {
                    button.classList.remove('btn-outline-primary');
                    button.classList.add('btn-outline-secondary');
                    button.style.opacity = '0.5';
                } else {
                    button.classList.remove('btn-outline-secondary');
                    button.classList.add('btn-outline-primary');
                    button.style.opacity = '1';
                }
            }
        }

        // Function to show all users
        function showAllUsers() {
            if (currentChart) {
                currentChart.data.datasets.forEach((dataset, index) => {
                    const meta = currentChart.getDatasetMeta(index);
                    meta.hidden = null;
                    updateToggleButtonState(index, false);
                });
                currentChart.update();
            }
        }

        // Function to hide all users
        function hideAllUsers() {
            if (currentChart) {
                currentChart.data.datasets.forEach((dataset, index) => {
                    const meta = currentChart.getDatasetMeta(index);
                    meta.hidden = true;
                    updateToggleButtonState(index, true);
                });
                currentChart.update();
            }
        }

        // Navigation functions
        function goToPreviousWeek() {
            if (currentWeekStart) {
                const prevWeek = new Date(currentWeekStart);
                prevWeek.setDate(prevWeek.getDate() - 7);
                const prevWeekStr = prevWeek.toISOString().split('T')[0];
                loadWeeklyWorkHoursChart(prevWeekStr);
            }
        }

        function goToNextWeek() {
            if (currentWeekStart) {
                const nextWeek = new Date(currentWeekStart);
                nextWeek.setDate(nextWeek.getDate() + 7);
                const nextWeekStr = nextWeek.toISOString().split('T')[0];
                loadWeeklyWorkHoursChart(nextWeekStr);
            }
        }

        function goToCurrentWeek() {
            loadWeeklyWorkHoursChart(); // No parameter = current week
        }

        // Initialize the chart and navigation when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Load current week data
            loadWeeklyWorkHoursChart();

            // Add event listeners for navigation buttons
            document.getElementById('prevWeekBtn').addEventListener('click', goToPreviousWeek);
            document.getElementById('nextWeekBtn').addEventListener('click', goToNextWeek);
            document.getElementById('currentWeekBtn').addEventListener('click', goToCurrentWeek);

            // Add event listeners for user control buttons
            document.getElementById('showAllUsersBtn').addEventListener('click', showAllUsers);
            document.getElementById('hideAllUsersBtn').addEventListener('click', hideAllUsers);

            // Customer chart event listeners
            document.getElementById('customerPrevWeekBtn').addEventListener('click', goToCustomerPreviousWeek);
            document.getElementById('customerNextWeekBtn').addEventListener('click', goToCustomerNextWeek);
            document.getElementById('customerShowAllUsersBtn').addEventListener('click', showAllCustomerUsers);
            document.getElementById('customerHideAllUsersBtn').addEventListener('click', hideAllCustomerUsers);
        });

        // Customer Chart Variables
        let currentCustomerWeekStart = null;
        let currentCustomerChart = null;

        // Function to fetch customer chart data and render the chart
        async function loadCustomerWorkHoursChart(weekStart = null) {
            const loadingElement = document.getElementById('customerChartLoading');
            const chartCanvas = document.getElementById('customerWorkHoursChart');
            const errorElement = document.getElementById('customerChartError');
            const weekTitleElement = document.getElementById('customerWeekTitle');

            // Show loading spinner and hide chart/error
            loadingElement.style.display = 'block';
            chartCanvas.style.display = 'none';
            errorElement.style.display = 'none';

            try {
                let url = '/api/charts/user-customer-work-hours';
                if (weekStart) {
                    url += `?weekStart=${weekStart}`;
                }

                console.log('Fetching customer chart data from:', url);
                const response = await fetch(url);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log('Received customer data:', data);

                // Update current week start for navigation
                currentCustomerWeekStart = weekStart || new Date().toISOString().split('T')[0];

                // Update week title - always show week dates
                const weekDate = new Date(weekStart || currentCustomerWeekStart || new Date().toISOString().split('T')[0]);
                const weekEnd = new Date(weekDate);
                weekEnd.setDate(weekEnd.getDate() + 6);
                const formatter = new Intl.DateTimeFormat('sk-SK', { day: '2-digit', month: '2-digit', year: 'numeric' });
                const weekTitle = `Týždeň ${formatter.format(weekDate)} - ${formatter.format(weekEnd)}`;
                weekTitleElement.innerHTML = `<h6 class="text-muted">${weekTitle}</h6>`;

                // Hide loading spinner
                loadingElement.style.display = 'none';

                // Check if we have data
                if (!data.success || !data.datasets || data.datasets.length === 0) {
                    console.warn('No customer chart data available');
                    errorElement.style.display = 'block';
                    return;
                }

                // Show chart canvas and create the chart
                chartCanvas.style.display = 'block';
                createCustomerChart(data.datasets, data.customerNames, data.canViewAllUsers, data.userNames);

            } catch (error) {
                console.error('Error loading customer chart data:', error);
                loadingElement.style.display = 'none';
                errorElement.style.display = 'block';
            }
        }

        // Function to create the customer chart
        function createCustomerChart(datasets, customerNames, canViewAllUsers, userNames) {
            console.log('Creating customer chart with', datasets.length, 'users and', customerNames.length, 'customers');

            // Destroy existing chart if it exists
            if (currentCustomerChart) {
                currentCustomerChart.destroy();
            }

            // Show/hide user controls based on permissions and data
            const userControlsElement = document.getElementById('customerUserControls');
            if (canViewAllUsers && datasets.length > 1) {
                userControlsElement.style.display = 'block';
                createCustomerUserToggleButtons(datasets);
            } else {
                userControlsElement.style.display = 'none';
            }

            // Create the chart using bar type with customer-based grouping
            const ctx = document.getElementById('customerWorkHoursChart').getContext('2d');
            currentCustomerChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: customerNames, // Customer names as X-axis labels
                    datasets: datasets    // Each dataset represents a user
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Hodiny'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Zákazníci'
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Odpracované hodiny podľa zákazníkov'
                        },
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                generateLabels: function(chart) {
                                    const original = Chart.defaults.plugins.legend.labels.generateLabels;
                                    const labels = original.call(this, chart);
                                    labels.forEach(label => {
                                        label.text = label.text + ' (používateľ)';
                                    });
                                    return labels;
                                }
                            }
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            callbacks: {
                                title: function(context) {
                                    return 'Zákazník: ' + context[0].label;
                                },
                                label: function(context) {
                                    return context.dataset.label + ': ' + context.parsed.y + ' hodín';
                                }
                            }
                        }
                    },
                    interaction: {
                        mode: 'index',
                        intersect: false
                    }
                }
            });
        }

        // Function to create user toggle buttons for customer chart
        function createCustomerUserToggleButtons(datasets) {
            const container = document.getElementById('customerUserToggleButtons');
            container.innerHTML = ''; // Clear existing buttons

            datasets.forEach((dataset, index) => {
                const button = document.createElement('button');
                button.type = 'button';
                button.className = 'btn btn-outline-primary btn-sm';
                button.textContent = dataset.label;
                button.style.backgroundColor = dataset.backgroundColor;
                button.style.borderColor = dataset.borderColor;
                button.style.color = '#fff';

                button.addEventListener('click', function() {
                    if (currentCustomerChart) {
                        const meta = currentCustomerChart.getDatasetMeta(index);
                        meta.hidden = !meta.hidden;
                        currentCustomerChart.update();

                        // Update button appearance
                        if (meta.hidden) {
                            button.style.opacity = '0.5';
                            button.style.textDecoration = 'line-through';
                        } else {
                            button.style.opacity = '1';
                            button.style.textDecoration = 'none';
                        }
                    }
                });

                container.appendChild(button);
            });
        }

        // Customer chart navigation functions
        function goToCustomerPreviousWeek() {
            if (currentCustomerWeekStart) {
                const currentDate = new Date(currentCustomerWeekStart);
                currentDate.setDate(currentDate.getDate() - 7);
                const newWeekStart = currentDate.toISOString().split('T')[0];
                loadCustomerWorkHoursChart(newWeekStart);
            }
        }

        function goToCustomerNextWeek() {
            if (currentCustomerWeekStart) {
                const currentDate = new Date(currentCustomerWeekStart);
                currentDate.setDate(currentDate.getDate() + 7);
                const newWeekStart = currentDate.toISOString().split('T')[0];
                loadCustomerWorkHoursChart(newWeekStart);
            }
        }

        // Customer chart user control functions
        function showAllCustomerUsers() {
            if (currentCustomerChart) {
                currentCustomerChart.data.datasets.forEach((dataset, index) => {
                    const meta = currentCustomerChart.getDatasetMeta(index);
                    meta.hidden = false;
                });
                currentCustomerChart.update();

                // Update all button appearances
                const buttons = document.querySelectorAll('#customerUserToggleButtons button');
                buttons.forEach(button => {
                    button.style.opacity = '1';
                    button.style.textDecoration = 'none';
                });
            }
        }

        function hideAllCustomerUsers() {
            if (currentCustomerChart) {
                currentCustomerChart.data.datasets.forEach((dataset, index) => {
                    const meta = currentCustomerChart.getDatasetMeta(index);
                    meta.hidden = true;
                });
                currentCustomerChart.update();

                // Update all button appearances
                const buttons = document.querySelectorAll('#customerUserToggleButtons button');
                buttons.forEach(button => {
                    button.style.opacity = '0.5';
                    button.style.textDecoration = 'line-through';
                });
            }
        }

        // Exchange Rates Functionality
        async function loadExchangeRates() {
            const loadingElement = document.getElementById('exchangeRatesLoading');
            const errorElement = document.getElementById('exchangeRatesError');
            const containerElement = document.getElementById('exchangeRatesContainer');
            const tableElement = document.getElementById('exchangeRatesTable');
            const lastUpdatedElement = document.getElementById('lastUpdated');

            // Show loading, hide others
            loadingElement.style.display = 'block';
            errorElement.style.display = 'none';
            containerElement.style.display = 'none';

            try {
                // Using a free exchange rate API (you can replace with your preferred API)
                const response = await fetch('https://api.exchangerate-api.com/v4/latest/EUR');

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                // Hide loading
                loadingElement.style.display = 'none';

                // Popular currencies to display
                const popularCurrencies = {
                    'USD': 'US Dolár',
                    'GBP': 'Britská libra',
                    'CHF': 'Švajčiarsky frank',
                    'CZK': 'Česká koruna',
                    'PLN': 'Poľský zlotý',
                    'HUF': 'Maďarský forint',
                    'JPY': 'Japonský jen',
                    'CAD': 'Kanadský dolár',
                    'AUD': 'Austrálsky dolár'
                };

                // Clear existing table
                tableElement.innerHTML = '';

                // Add rows for each currency
                Object.entries(popularCurrencies).forEach(([code, name]) => {
                    if (data.rates[code]) {
                        const rate = data.rates[code];
                        const row = document.createElement('tr');

                        // Calculate change (mock data since we don't have historical data)
                        const change = (Math.random() - 0.5) * 0.02; // Random change between -1% and +1%
                        const changeClass = change >= 0 ? 'text-success' : 'text-danger';
                        const changeIcon = change >= 0 ? '↗' : '↘';

                        row.innerHTML = `
                            <td>${name}</td>
                            <td><strong>${code}</strong></td>
                            <td class="text-end">${rate.toFixed(4)}</td>
                            <td class="text-end ${changeClass}">
                                ${changeIcon} ${Math.abs(change * 100).toFixed(2)}%
                            </td>
                        `;
                        tableElement.appendChild(row);
                    }
                });

                // Update last updated time
                lastUpdatedElement.textContent = new Date().toLocaleString('sk-SK');

                // Show container
                containerElement.style.display = 'block';

            } catch (error) {
                console.error('Error loading exchange rates:', error);
                loadingElement.style.display = 'none';
                errorElement.style.display = 'block';
            }
        }

        // Initialize week starts
        function getWeekStart(date = new Date()) {
            const d = new Date(date);
            const day = d.getDay();
            const diff = d.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
            return new Date(d.setDate(diff));
        }

        // Load everything on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize current week starts
            const currentWeek = getWeekStart();
            currentWeekStart = currentWeek.toISOString().split('T')[0];
            currentCustomerWeekStart = currentWeek.toISOString().split('T')[0];

            // Load both charts with current week
            loadWeeklyWorkHoursChart(currentWeekStart);
            loadCustomerWorkHoursChart(currentCustomerWeekStart);

            // Load exchange rates
            loadExchangeRates();

            // Add refresh button event listener
            document.getElementById('refreshRatesBtn').addEventListener('click', loadExchangeRates);
        });
    </script>
</th:block>

</html>
