<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:lang="${#locale.language}"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout}">
<head>
    <meta charset="UTF-8">
    <title th:text="#{task.list.title}">Task List</title>
</head>

<section layout:fragment="content">
    <div class="container">
        <h2 th:text="#{task.list.title}">Task List</h2>
        <a th:href="@{/tasks/new}" class="btn btn-primary mb-3" th:text="#{task.add}">Add Task</a>

        <table class="table table-bordered table-striped">
            <thead>
            <tr>
                <th>Title</th>
                <th>Description</th>
                <th>Customer</th>
                <th>Responsible User</th>
                <th>Created By</th>
                <th>Started At</th>
                <th>Ended At</th>
                <th>Status</th>
                <th>Active Duration (hrs)</th>
                <th>Priority</th>
                <th>Created At</th>
                <th>Actions</th>
            </tr>
            </thead>
            <tbody>
            <tr th:each="task : ${tasks}">
                <td th:text="${task.title}">Title</td>
                <td th:text="${task.description}">Description</td>
                <td th:text="${task.customer?.name}">Customer</td>
                <td th:text="${task.user?.firstName}">Responsible User</td>
                <td th:text="${task.createdBy?.firstName}">Created By</td>
                <td th:text="${task.startedAt != null ? #temporals.format(task.startedAt, 'yyyy-MM-dd HH:mm') : ''}">Started At</td>
                <td th:text="${task.endedAt != null ? #temporals.format(task.endedAt, 'yyyy-MM-dd HH:mm') : ''}">Ended At</td>
                <td>
                    <span th:if="${task.isCurrentlyPaused}" class="badge bg-warning">Paused</span>
                    <span th:if="${task.isCurrentlyRunning()}" class="badge bg-success">Running</span>
                    <span th:if="${task.isFinished()}" class="badge bg-secondary">Finished</span>
                    <span th:if="${task.startedAt == null}" class="badge bg-light text-dark">Not Started</span>
                </td>
                <td th:text="${#numbers.formatDecimal(task.getTotalActiveDurationHours(), 1, 'COMMA', 2, 'POINT')}">Active Duration</td>
                <td th:text="${task.priority}">Priority</td>
                <td th:text="${task.createdAt != null ? #temporals.format(task.createdAt, 'yyyy-MM-dd HH:mm') : ''}">Created At</td>
                <td>
                    <a th:href="@{'/tasks/edit/' + ${task.id}}" class="btn btn-sm btn-secondary">Edit</a>
                    <form th:if="${task.archivedAt != null and task.realizationDate != null}"
                          th:action="@{/tasks/{id}/finish(id=${task.id})}"
                          method="post"
                          style="display:inline">
                        <button type="submit" class="btn btn-sm btn-danger">Archive</button>
                    </form>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
</section>
</html>
