<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:lang="${#locale.language}"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout}">
<head>
    <meta charset="UTF-8">
    <title th:text="#{task.list.title}">Task List</title>
    <style>
        .table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .table {
            margin-bottom: 0;
            font-size: 0.9rem;
        }
        .table th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
            padding: 12px 8px;
            white-space: nowrap;
        }
        .table td {
            padding: 10px 8px;
            vertical-align: middle;
            border-bottom: 1px solid #f0f0f0;
        }
        .table tbody tr:hover {
            background-color: #f8f9fa;
        }
        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        .action-buttons {
            white-space: nowrap;
        }
        .action-buttons .btn {
            margin-right: 0.25rem;
        }
        .priority-high {
            background-color: #dc3545 !important;
        }
        .priority-medium {
            background-color: #ffc107 !important;
            color: #000 !important;
        }
        .priority-low {
            background-color: #6c757d !important;
        }
        .task-title {
            font-weight: 500;
            color: #495057;
        }
        .task-description {
            font-size: 0.8rem;
            color: #6c757d;
            font-style: italic;
        }
    </style>
</head>

<section layout:fragment="content">
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 th:text="#{task.list.title}" class="mb-0">Task List</h2>
            <a th:href="@{/tasks/new}" class="btn btn-primary" th:text="#{task.add}">Add Task</a>
        </div>

        <div class="table-container">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th th:text="#{task.title}">Title</th>
                            <th th:text="#{task.customer}">Customer</th>
                            <th th:text="#{task.user}">Responsible User</th>
                            <th th:text="#{task.priority}">Priority</th>
                            <th th:text="#{task.status}">Status</th>
                            <th th:text="#{task.started.at}">Started At</th>
                            <th th:text="#{task.ended.at}">Ended At</th>
                            <th th:text="#{task.active.duration}">Active Duration</th>
                            <th th:text="#{task.created.at}">Created At</th>
                            <th th:text="#{label.actions}">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr th:each="task : ${tasks}" th:if="${tasks != null && !tasks.isEmpty()}">
                            <td>
                                <div class="task-title" th:text="${task.title}">Task Title</div>
                                <div class="task-description" th:if="${task.description != null && !task.description.isEmpty()}"
                                     th:text="${#strings.abbreviate(task.description, 60)}">Description...</div>
                            </td>
                            <td>
                                <span th:if="${task.customer != null}" th:text="${task.customer.name}">Customer Name</span>
                                <span th:unless="${task.customer != null}" class="text-muted">-</span>
                            </td>
                            <td>
                                <span th:if="${task.user != null}" th:text="${task.user.firstName + ' ' + task.user.lastName}">User Name</span>
                                <span th:unless="${task.user != null}" class="text-muted">-</span>
                            </td>
                            <td>
                                <span th:if="${task.priority != null}"
                                      th:class="'badge ' + (${task.priority <= 1} ? 'priority-high' : (${task.priority <= 3} ? 'priority-medium' : 'priority-low'))"
                                      th:text="${task.priority}">Priority</span>
                                <span th:unless="${task.priority != null}" class="text-muted">-</span>
                            </td>
                            <td>
                                <span th:if="${task.isCurrentlyPaused != null && task.isCurrentlyPaused}"
                                      class="badge bg-warning status-badge" th:text="#{task.status.paused}">Paused</span>
                                <span th:if="${task.isCurrentlyRunning()}"
                                      class="badge bg-success status-badge" th:text="#{task.status.running}">Running</span>
                                <span th:if="${task.isFinished()}"
                                      class="badge bg-secondary status-badge" th:text="#{task.status.finished}">Finished</span>
                                <span th:if="${task.startedAt == null}"
                                      class="badge bg-light text-dark status-badge" th:text="#{task.status.not.started}">Not Started</span>
                            </td>
                            <td th:text="${task.startedAt != null ? #temporals.format(task.startedAt, 'dd.MM.yyyy HH:mm') : '-'}">Started At</td>
                            <td th:text="${task.endedAt != null ? #temporals.format(task.endedAt, 'dd.MM.yyyy HH:mm') : '-'}">Ended At</td>
                            <td>
                                <span th:if="${task.getTotalActiveDurationHours() > 0}"
                                      th:text="${#numbers.formatDecimal(task.getTotalActiveDurationHours(), 1, 2)} + ' h'">0.00 h</span>
                                <span th:unless="${task.getTotalActiveDurationHours() > 0}" class="text-muted">-</span>
                            </td>
                            <td th:text="${task.createdAt != null ? #temporals.format(task.createdAt, 'dd.MM.yyyy HH:mm') : '-'}">Created At</td>
                            <td>
                                <div class="action-buttons">
                                    <a th:href="@{'/tasks/edit/' + ${task.id}}"
                                       class="btn btn-outline-secondary btn-sm"
                                       th:title="#{button.edit}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form th:action="@{'/tasks/delete/' + ${task.id}}"
                                          method="get"
                                          style="display:inline;"
                                          th:attr="onsubmit='return confirm(\'' + #{task.delete.confirm} + '\');'">
                                        <button type="submit"
                                                class="btn btn-outline-danger btn-sm"
                                                th:title="#{button.delete}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        <tr th:if="${tasks == null || tasks.isEmpty()}">
                            <td colspan="10" class="text-center text-muted py-4" th:text="#{task.list.empty}">No tasks found</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>
</html>
        </table>
    </div>
</section>
</html>
