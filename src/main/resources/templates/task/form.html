<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:lang="${#locale.language}"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout}">
<head>
    <meta charset="UTF-8">
    <title th:text="${task.id != null} ? #{task.edit.title} : #{task.create.title}">Task Form</title>
</head>

<body>
<section layout:fragment="content">
    <div class="container">
        <form th:action="@{/tasks/save}" th:object="${task}" method="post">
            <h2 th:text="${task.id != null} ? #{task.edit.title} : #{task.create.title}">Task Form</h2>

            <input type="hidden" th:field="*{id}" />

            <div class="mb-3">
                <label for="title" class="form-label" th:text="#{task.title}">Title</label>
                <input type="text" th:field="*{title}" class="form-control" id="title" />
                <div class="text-danger" th:errors="*{title}"></div>
            </div>

            <div class="mb-3">
                <label for="description" class="form-label" th:text="#{task.description}">Description</label>
                <textarea th:field="*{description}" class="form-control" id="description"></textarea>
                <div class="text-danger" th:errors="*{description}"></div>
            </div>

            <div class="mb-3">
                <label for="customer" class="form-label" th:text="#{task.customer}">Customer</label>
                <select th:field="*{customer}" class="form-select" id="customer">
                    <option value="" th:text="#{task.customer.select}">Choose customer</option>
                    <option th:each="customer : ${customers}" th:value="${customer.id}"
                            th:text="${customer.name}"></option>
                </select>
                <div class="text-danger" th:if="${#fields.hasErrors('customer')}"
                     th:errors="*{customer}"></div>
            </div>

            <div class="mb-3">
                <label for="priority" class="form-label" th:text="#{task.priority}">Priority</label>
                <input type="number" step="1" th:field="*{priority}" class="form-control" id="priority" />
                <div class="text-danger" th:errors="*{priority}"></div>
            </div>

            <div class="mb-3">
                <label for="user" class="form-label" th:text="#{task.user}">Responsible Person</label>
                <select th:field="*{user}" class="form-select" id="user" required>
                    <option value="" disabled th:text="#{task.user.select}">Choose person</option>
                    <option th:each="user : ${users}" th:value="${user.id}"
                            th:text="${user.firstName} + ' ' + ${user.lastName}"></option>
                </select>
                <div class="text-danger" th:if="${#fields.hasErrors('user')}"
                     th:errors="*{user}"></div>
            </div>

            <button type="submit" class="btn btn-primary" th:text="#{action.save}">Save</button>
            <a th:href="@{/tasks}" class="btn btn-secondary" th:text="#{action.cancel}">Cancel</a>
        </form>
    </div>
</section>

</body>
</html>
