<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:lang="${#locale.language}"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout">

<head>
    <meta charset="UTF-8">
    <title>TtT</title>
    <link href="https://fonts.googleapis.com/css2?family=Mulish:wght@400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css"
          integrity="sha512-Avb2QiuDEEvB4bZJYdft2mNjVShBftLdPG8FJ0V7irTLQ8Uo0qcPxh4Plq7G5tGm0rU+1SPhVotteLpBERwTkw=="
          crossorigin="anonymous" referrerpolicy="no-referrer"/>
    <link rel="stylesheet" href="/css/styles.css">
</head>

<body>
<nav class="navbar navbar-light bg-light mb-4">
    <div class="container d-flex justify-content-between align-items-center">
        <div class="col-md-4">
            <a style="text-decoration: none;" th:href="@{/home}">
                <span class="navbar-brand fw-bold fs-4 mb-0" th:text="#{app.name}">TripleTea</span>
            </a>
        </div>

        <div class="col-md-4 offset-md-1" th:if="${currentUser != null}">
            <div class="row">
                <div class="col-md-6 d-flex align-items-center justify-content-center" style="color: white; min-width: 0;">
                    <span th:text="${currentUser.email}" style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap; max-width: 250px;" th:title="${currentUser.email}"></span>
                </div>
                <div class="col-md-3 d-flex align-items-center justify-content-center">
                    <!-- Language Switcher -->
                    <div class="dropdown me-2">
                        <button class="btn btn-sm btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-globe"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" th:href="@{''(lang=en)}" th:text="#{language.english}">English</a></li>
                            <li><a class="dropdown-item" th:href="@{''(lang=sk)}" th:text="#{language.slovak}">Slovak</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-3">
                    <button class="btn" type="button" data-bs-toggle="collapse" data-bs-target="#menuCollapse" aria-expanded="false"
                            aria-controls="menuCollapse">
                        <i class="fas fa-bars fs-4" style="color: white;"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="collapse" id="menuCollapse">
            <div class="p-3 border-top" th:if="${currentUser != null}">
                <a th:href="@{'/users'}" class="d-block text-white mb-2 text-decoration-none" th:text="#{nav.users}">Users</a>
                <a th:href="@{'/customers'}" class="d-block text-white mb-2 text-decoration-none" th:text="#{nav.customers}">Customers</a>
                <a th:href="@{'/quotes'}" class="d-block text-white mb-2 text-decoration-none" th:text="#{nav.quotes}">Quotes</a>
                <a th:href="@{'/tasks'}" class="d-block text-white mb-2 text-decoration-none" th:text="#{nav.tasks}">Tasks</a>
                <a th:href="@{'/tags'}" class="d-block text-white mb-2 text-decoration-none" th:text="#{nav.tags}">Tags</a>
                <a th:href="@{'/users/change-password/' + ${currentUser.id}}" class="d-block text-white mb-2 text-decoration-none" th:text="#{nav.change.password}">Change Password</a>
                <a th:href="@{'/logout'}" class="d-block text-decoration-none text-danger" th:text="#{nav.logout}">Logout</a>
            </div>
        </div>
    </div>
</nav>

<div class="container">
    <section layout:fragment="content">
    </section>
</div>

<footer class="bg-light text-center py-3 mt-5">
    <div class="container">
        <div class="row">
            <div class="col-md-6 text-start">
                <span class="text-muted" th:text="#{app.copyright}">© 2025 TripleTea</span>
            </div>
            <div class="col-md-6 text-end">
                <span class="text-muted">
                    <span th:text="#{app.version}">Version: </span><span th:text="${projectVersion}">SNAPSHOT</span>
                </span>
            </div>
        </div>
    </div>
</footer>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<th:block layout:fragment="scripts"></th:block>
</body>
</html>
