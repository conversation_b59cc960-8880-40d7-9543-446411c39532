<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:lang="${#locale.language}"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout}">
<head>
    <title th:text="#{address.list.title}">Address List</title>
</head>

<body>
<section layout:fragment="content">
    <h2 th:text="#{address.list.heading}">Address List</h2>
    <a th:href="@{/addresses/new}" class="btn btn-primary mb-3" th:text="#{address.list.add}">Add Address</a>

    <table class="table table-bordered table-striped">
        <thead>
        <tr>
            <th th:text="#{address.id}">id</th>
            <th th:text="#{address.city}">city</th>
            <th th:text="#{address.country}">country</th>
            <th th:text="#{address.countryCode}">countryCode</th>
            <th th:text="#{address.street}">street</th>
            <th th:text="#{address.zip}">zip</th>
            <th th:text="#{address.actions}">Actions</th>
        </tr>
        </thead>
        <tbody>
        <tr th:each="address : ${addresses}">
            <td th:text="${address.id}">id</td>
            <td th:text="${address.city}">city</td>
            <td th:text="${address.country}">country</td>
            <td th:text="${address.countryCode}">countryCode</td>
            <td th:text="${address.street}">street</td>
            <td th:text="${address.zip}">zip</td>
            <td>
                <a th:href="@{'/addresses/edit/' + ${address.id}}" class="btn btn-sm btn-secondary" th:text="#{address.edit}">Edit</a>
                <form th:action="@{'/addresses/delete/' + ${address.id}}" method="post" style="display:inline;">
                    <input type="hidden" name="_method" value="delete"/>
                    <button type="submit" class="btn btn-sm btn-danger"
                            onclick="return confirm(/*[[#{address.confirmDelete}]]*/'Are you sure you want to delete this address?');"
                            th:text="#{address.delete}">Delete</button>
                </form>
            </td>
        </tr>
        </tbody>
    </table>
</section>
</body>
</html>
