<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      th:lang="${#locale.language}"
      layout:decorate="~{layout}">
<head>
    <meta charset="UTF-8" />
    <title th:text="${address != null and address.id != null} ? #{address.form.editTitle} : #{address.form.createTitle}">Address Form</title>
</head>

<body>
<section layout:fragment="content">
    <div class="container">
        <form th:action="@{/addresses/save}" th:object="${address}" method="post">

            <div th:if="${#fields.hasErrors('*')}" class="alert alert-danger" th:text="#{address.form.fixErrors}">
                Fix errors in form
            </div>

            <h2 th:text="${address != null and address.id != null} ? #{address.form.editTitle} : #{address.form.createTitle}">
                Address Form
            </h2>

            <input type="hidden" th:field="*{id}" />

            <div class="mb-3">
                <label for="city" class="form-label" th:text="#{address.city}">City</label>
                <input type="text" th:field="*{city}" class="form-control" id="city" />
                <div class="text-danger" th:if="${#fields.hasErrors('city')}" th:errors="*{city}"></div>
            </div>

            <div class="mb-3">
                <label for="country" class="form-label" th:text="#{address.country}">Country</label>
                <input type="text" th:field="*{country}" class="form-control" id="country" />
                <div class="text-danger" th:if="${#fields.hasErrors('country')}" th:errors="*{country}"></div>
            </div>

            <div class="mb-3">
                <label for="countryCode" class="form-label" th:text="#{address.countryCode}">Country Code</label>
                <input type="text" th:field="*{countryCode}" class="form-control" id="countryCode" />
                <div class="text-danger" th:if="${#fields.hasErrors('countryCode')}" th:errors="*{countryCode}"></div>
            </div>

            <div class="mb-3">
                <label for="street" class="form-label" th:text="#{address.street}">Street</label>
                <input type="text" th:field="*{street}" class="form-control" id="street" />
                <div class="text-danger" th:if="${#fields.hasErrors('street')}" th:errors="*{street}"></div>
            </div>

            <div class="mb-3">
                <label for="zip" class="form-label" th:text="#{address.zip}">ZIP</label>
                <input type="text" th:field="*{zip}" class="form-control" id="zip" />
                <div class="text-danger" th:if="${#fields.hasErrors('zip')}" th:errors="*{zip}"></div>
            </div>

            <button type="submit" class="btn btn-success" th:text="#{address.form.save}">Save</button>
            <a th:href="@{/addresses}" class="btn btn-secondary" th:text="#{address.form.cancel}">Cancel</a>
        </form>
    </div>
</section>
</body>
</html>
