<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      th:lang="${#locale.language}"
      layout:decorate="~{layout}">
<head>
    <title th:text="#{invoice.list.title}">Invoice List</title>
</head>
<body>
<section layout:fragment="content">
    <h2 th:text="#{invoice.list.title}">Invoice List</h2>
    <a th:href="@{/invoices/create}" class="btn btn-primary mb-3" th:text="#{invoice.list.add}">Add Invoice</a>

    <table class="table table-bordered table-striped">
        <thead>
        <tr>
            <th th:text="#{invoice.id}">ID</th>
            <th th:text="#{invoice.accountNumber}">Account Number</th>
            <th th:text="#{invoice.category}">Category</th>
            <th th:text="#{invoice.company}">Company</th>
            <th th:text="#{invoice.constantSymbol}">Constant Symbol</th>
            <th th:text="#{invoice.createdAt}">Created At</th>
            <th th:text="#{invoice.currency}">Currency</th>
            <th th:text="#{invoice.deliveryDate}">Delivery Date</th>
            <th th:text="#{invoice.department}">Department</th>
            <th th:text="#{invoice.dic}">DIC</th>
            <th th:text="#{invoice.dueDate}">Due Date</th>
            <th th:text="#{invoice.iban}">IBAN</th>
            <th th:text="#{invoice.icDph}">IC DPH</th>
            <th th:text="#{invoice.ico}">ICO</th>
            <th th:text="#{invoice.internalNote}">Internal Note</th>
            <th th:text="#{invoice.introText}">Intro Text</th>
            <th th:text="#{invoice.issueDate}">Issue Date</th>
            <th th:text="#{invoice.issuedBy}">Issued By</th>
            <th th:text="#{invoice.number}">Number</th>
            <th th:text="#{invoice.customer}">Customer</th>
            <th th:text="#{invoice.user}">Responsible User</th>
            <th th:text="#{invoice.status}">Status</th>
            <th th:text="#{invoice.realizationDate}">Realization Date</th>
            <th th:text="#{invoice.note}">Note</th>
            <th th:text="#{invoice.orderDeliveryDate}">Order Delivery Date</th>
            <th th:text="#{invoice.orderNumber}">Order Number</th>
            <th th:text="#{invoice.paidAdvance}">Paid Advance</th>
            <th th:text="#{invoice.paidAdvanceWithoutVat}">Paid Advance Without VAT</th>
            <th th:text="#{invoice.partnerName}">Partner Name</th>
            <th th:text="#{invoice.partnerNumber}">Partner Number</th>
            <th th:text="#{invoice.paymentDate}">Payment Date</th>
            <th th:text="#{invoice.paymentMethod}">Payment Method</th>
            <th th:text="#{invoice.projectNumber}">Project Number</th>
            <th th:text="#{invoice.remainingToPay}">Remaining To Pay</th>
            <th th:text="#{invoice.specificSymbol}">Specific Symbol</th>
            <th th:text="#{invoice.subcategory}">Subcategory</th>
            <th th:text="#{invoice.swift}">SWIFT</th>
            <th th:text="#{invoice.taxDocNumberForAdvance}">Tax Doc # for Advance</th>
            <th th:text="#{invoice.total}">Total</th>
            <th th:text="#{invoice.totalExcludingAdvance}">Total Excl. Advance</th>
            <th th:text="#{invoice.totalExcludingAdvanceWithoutVat}">Total Excl. Advance Without VAT</th>
            <th th:text="#{invoice.totalWithoutVat}">Total Without VAT</th>
            <th th:text="#{invoice.type}">Type</th>
            <th th:text="#{invoice.variableSymbol}">Variable Symbol</th>
            <th th:text="#{invoice.vatAmountHigh}">VAT Amount High</th>
            <th th:text="#{invoice.vatBaseHigh}">VAT Base High</th>
            <th th:text="#{invoice.vatBaseNone}">VAT Base None</th>
            <th th:text="#{invoice.vatRateHigh}">VAT Rate High</th>
            <th th:text="#{invoice.addressId}">Address ID</th>
            <th th:text="#{actions}">Actions</th>
        </tr>
        </thead>
        <tbody>
        <tr th:each="invoice : ${invoices}">
            <td th:text="${invoice.id}">ID</td>
            <td th:text="${invoice.accountNumber}">Account Number</td>
            <td th:text="${invoice.category}">Category</td>
            <td th:text="${invoice.company}">Company</td>
            <td th:text="${invoice.constantSymbol}">Constant Symbol</td>
            <td th:text="${invoice.createdAt != null ? #temporals.format(invoice.createdAt, 'yyyy-MM-dd HH:mm') : ''}">Created At</td>
            <td th:text="${invoice.currency}">Currency</td>
            <td th:text="${invoice.deliveryDate != null ? #temporals.format(invoice.deliveryDate, 'yyyy-MM-dd') : ''}">Delivery Date</td>
            <td th:text="${invoice.department}">Department</td>
            <td th:text="${invoice.dic}">DIC</td>
            <td th:text="${invoice.dueDate != null ? #temporals.format(invoice.dueDate, 'yyyy-MM-dd') : ''}">Due Date</td>
            <td th:text="${invoice.iban}">IBAN</td>
            <td th:text="${invoice.icDph}">IC DPH</td>
            <td th:text="${invoice.ico}">ICO</td>
            <td th:text="${invoice.internalNote}">Internal Note</td>
            <td th:text="${invoice.introText}">Intro Text</td>
            <td th:text="${invoice.issueDate != null ? #temporals.format(invoice.issueDate, 'yyyy-MM-dd') : ''}">Issue Date</td>
            <td th:text="${invoice.issuedBy}">Issued By</td>
            <td th:text="${invoice.number}">Number</td>
            <td th:text="${invoice.customer != null ? invoice.customer.name : ''}">Customer</td>
            <td th:text="${invoice.user != null ? invoice.user.firstName + ' ' + invoice.user.lastName : ''}">Responsible User</td>
            <td th:text="${invoice.status != null ? #{${'status.' + invoice.status.name().toLowerCase()}} : ''}">Status</td>
            <td th:text="${invoice.realizationDate != null ? #temporals.format(invoice.realizationDate, 'yyyy-MM-dd HH:mm') : ''}">Realization Date</td>
            <td th:text="${invoice.note}">Note</td>
            <td th:text="${invoice.orderDeliveryDate != null ? #temporals.format(invoice.orderDeliveryDate, 'yyyy-MM-dd') : ''}">Order Delivery Date</td>
            <td th:text="${invoice.orderNumber}">Order Number</td>
            <td th:text="${invoice.paidAdvance}">Paid Advance</td>
            <td th:text="${invoice.paidAdvanceWithoutVat}">Paid Advance Without VAT</td>
            <td th:text="${invoice.partnerName}">Partner Name</td>
            <td th:text="${invoice.partnerNumber}">Partner Number</td>
            <td th:text="${invoice.paymentDate}">Payment Date</td>
            <td th:text="${invoice.paymentMethod}">Payment Method</td>
            <td th:text="${invoice.projectNumber}">Project Number</td>
            <td th:text="${invoice.remainingToPay}">Remaining To Pay</td>
            <td th:text="${invoice.specificSymbol}">Specific Symbol</td>
            <td th:text="${invoice.subcategory}">Subcategory</td>
            <td th:text="${invoice.swift}">SWIFT</td>
            <td th:text="${invoice.taxDocNumberForAdvance}">Tax Doc # for Advance</td>
            <td th:text="${invoice.total}">Total</td>
            <td th:text="${invoice.totalExcludingAdvance}">Total Excl. Advance</td>
            <td th:text="${invoice.totalExcludingAdvanceWithoutVat}">Total Excl. Advance Without VAT</td>
            <td th:text="${invoice.totalWithoutVat}">Total Without VAT</td>
            <td th:text="${invoice.type}">Type</td>
            <td th:text="${invoice.variableSymbol}">Variable Symbol</td>
            <td th:text="${invoice.vatAmountHigh}">VAT Amount High</td>
            <td th:text="${invoice.vatBaseHigh}">VAT Base High</td>
            <td th:text="${invoice.vatBaseNone}">VAT Base None</td>
            <td th:text="${invoice.vatRateHigh}">VAT Rate High</td>
            <td th:text="${invoice.addressId}">Address ID</td>
            <td>
                <a th:href="@{/invoices/edit/{id}(id=${invoice.id})}" class="btn btn-sm btn-secondary" th:text="#{action.edit}">Edit</a>
                <a th:href="@{/invoices/delete/{id}(id=${invoice.id})}" class="btn btn-sm btn-danger"
                   th:text="#{action.delete}"
                   onclick="return confirm([[#{invoice.delete.confirm}]])">Delete</a>
            </td>
        </tr>
        </tbody>
    </table>
</section>
</body>
</html>
