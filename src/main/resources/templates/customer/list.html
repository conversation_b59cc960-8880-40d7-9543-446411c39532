<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:lang="${#locale.language}"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout}">
<head>
    <meta charset="UTF-8"/>
    <title th:text="#{customer.list.title}">Customer List</title>
    <style>
        .table-container {
            overflow-x: auto;
            width: 100%;
            max-width: 100vw;
        }
        .customer-table {
            font-size: 0.85rem;
            width: 100%;
            min-width: 1400px; /* Minimum width to ensure all columns are readable */
            table-layout: fixed; /* Fixed layout prevents overlapping */
        }
        .customer-table th,
        .customer-table td {
            padding: 0.4rem;
            vertical-align: middle;
            min-width: 120px; /* Minimum column width */
            word-wrap: break-word;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .customer-table th:first-child,
        .customer-table td:first-child {
            min-width: 200px; /* Wider for name column */
        }
        .customer-table .col-tags {
            min-width: 150px;
            max-width: 180px;
            white-space: nowrap;
            overflow: hidden;
        }
        .customer-table .col-name {
            min-width: 250px; /* Extra wide for customer name */
            max-width: 300px;
        }
        .customer-table .col-ico {
            min-width: 100px;
            max-width: 120px;
        }
        .customer-table .col-city,
        .customer-table .col-country {
            min-width: 120px;
            max-width: 150px;
        }
        .customer-table .col-type,
        .customer-table .col-subtype {
            min-width: 130px;
            max-width: 160px;
        }
        .customer-table .col-responsible,
        .customer-table .col-contact-person {
            min-width: 150px;
            max-width: 200px;
        }
        .customer-table .col-contact-email {
            min-width: 180px;
            max-width: 220px;
        }
        .customer-table .col-contact-phone {
            min-width: 130px;
            max-width: 150px;
        }
        /* Prevent text wrapping in key columns */
        .customer-table .col-name,
        .customer-table .col-ico,
        .customer-table .col-contact-email,
        .customer-table .col-contact-phone {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        /* Actions column styling */
        .customer-table .actions-column {
            min-width: 120px;
            max-width: 120px;
            text-align: center;
            white-space: nowrap;
        }
        .action-btn {
            display: inline-block;
            padding: 0.25rem;
            margin: 0 0.1rem;
            border-radius: 0.25rem;
            text-decoration: none;
            transition: all 0.2s ease;
        }
        .action-btn:hover {
            transform: scale(1.1);
            text-decoration: none;
        }
        .action-btn-edit {
            color: #6c757d;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
        }
        .action-btn-edit:hover {
            color: #495057;
            background-color: #e9ecef;
        }
        .action-btn-event {
            color: #0d6efd;
            background-color: #e7f1ff;
            border: 1px solid #b6d7ff;
        }
        .action-btn-event:hover {
            color: #0a58ca;
            background-color: #cfe2ff;
        }
        .action-btn-quote {
            color: #198754;
            background-color: #d1e7dd;
            border: 1px solid #a3cfbb;
        }
        .action-btn-quote:hover {
            color: #146c43;
            background-color: #b8dcc8;
        }
        .action-btn-delete {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
        }
        .action-btn-delete:hover {
            color: #721c24;
            background-color: #f1aeb5;
        }
        .tag-badge {
            display: inline-block;
            padding: 0.15em 0.25em;
            font-size: 0.65rem;
            font-weight: 600;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.15rem;
            margin-right: 0.15rem;
            margin-bottom: 0;
            color: #4a4a4a !important;
            border: 1px solid #d1c4e9;
        }
        .tags-container {
            display: flex;
            flex-wrap: wrap;
            gap: 0.15rem;
            max-width: 150px;
            overflow: hidden;
        }
        .container-fluid {
            max-width: 100%;
            padding: 0.5rem;
        }
        .table-responsive-custom {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }
        .table-responsive-custom::-webkit-scrollbar {
            height: 8px;
        }
        .table-responsive-custom::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }
        .table-responsive-custom::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        .table-responsive-custom::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        .column-toggle {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .column-toggle h6 {
            margin-bottom: 0.5rem;
            font-weight: 600;
        }
        .form-check {
            display: inline-block;
            margin-right: 1rem;
            margin-bottom: 0.5rem;
        }
        .filters-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .pagination-info {
            font-size: 0.9rem;
            color: #6c757d;
        }
        .sortable {
            cursor: pointer;
            user-select: none;
        }
        .sortable:hover {
            background-color: #f8f9fa;
        }
        .sort-icon {
            margin-left: 0.5rem;
            font-size: 0.8rem;
        }

        .container, .container-lg, .container-md, .container-sm, .container-xl, .container-xxl{
            max-width: 1700px;
        }

        /* Multi-select dropdown styling */
        .tag-filter-select {
            min-height: 38px;
            max-height: 120px;
            overflow-y: auto;
        }

        .tag-filter-select option {
            padding: 0.25rem 0.5rem;
        }

        .tag-filter-select option:checked {
            background-color: #0d6efd;
            color: white;
        }
    </style>
</head>
<body>
<section layout:fragment="content">
    <div class="container-fluid">
        <h2 th:text="#{customer.list.title}">Customer List</h2>
        <a th:href="@{/customers/new}" class="btn btn-primary mb-3" th:text="#{customer.add}">Add Customer</a>

        <!-- Column Toggle Section -->
        <div class="column-toggle">
            <h6>Zobraziť/skryť stĺpce:</h6>
            <div class="form-check">
                <input class="form-check-input column-toggle-cb" type="checkbox" id="col-name" checked>
                <label class="form-check-label" for="col-name" th:text="#{customer.name}">Názov / meno</label>
            </div>
            <div class="form-check">
                <input class="form-check-input column-toggle-cb" type="checkbox" id="col-ico" checked>
                <label class="form-check-label" for="col-ico" th:text="#{customer.ico}">IČO</label>
            </div>
            <div class="form-check">
                <input class="form-check-input column-toggle-cb" type="checkbox" id="col-city" checked>
                <label class="form-check-label" for="col-city" th:text="#{customer.city}">Mesto</label>
            </div>
            <div class="form-check">
                <input class="form-check-input column-toggle-cb" type="checkbox" id="col-country" checked>
                <label class="form-check-label" for="col-country" th:text="#{customer.country}">Štát</label>
            </div>
            <div class="form-check">
                <input class="form-check-input column-toggle-cb" type="checkbox" id="col-type" checked>
                <label class="form-check-label" for="col-type" th:text="#{customer.type}">Typ</label>
            </div>
            <div class="form-check">
                <input class="form-check-input column-toggle-cb" type="checkbox" id="col-subtype" checked>
                <label class="form-check-label" for="col-subtype" th:text="#{customer.subtype}">Podtyp</label>
            </div>
            <div class="form-check">
                <input class="form-check-input column-toggle-cb" type="checkbox" id="col-responsible" checked>
                <label class="form-check-label" for="col-responsible" th:text="#{customer.responsible.person}">Zodpovedný</label>
            </div>
            <div class="form-check">
                <input class="form-check-input column-toggle-cb" type="checkbox" id="col-contact-person" checked>
                <label class="form-check-label" for="col-contact-person" th:text="#{customer.contact.person}">Kontaktná osoba</label>
            </div>
            <div class="form-check">
                <input class="form-check-input column-toggle-cb" type="checkbox" id="col-contact-email" checked>
                <label class="form-check-label" for="col-contact-email" th:text="#{customer.contact.email}">Kontaktný email</label>
            </div>
            <div class="form-check">
                <input class="form-check-input column-toggle-cb" type="checkbox" id="col-contact-phone" checked>
                <label class="form-check-label" for="col-contact-phone" th:text="#{customer.contact.phone}">Kontaktný telefón</label>
            </div>
            <div class="form-check">
                <input class="form-check-input column-toggle-cb" type="checkbox" id="col-tags" checked>
                <label class="form-check-label" for="col-tags" th:text="#{customer.tags}">Menovky</label>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="filters-section">
            <h6>Filtre:</h6>
            <form method="get" th:action="@{/customers}">
                <div class="row">
                    <div class="col-md-2">
                        <input type="text" class="form-control form-control-sm" name="nameFilter"
                               th:value="${nameFilter}" th:placeholder="#{customer.name}">
                    </div>
                    <div class="col-md-2">
                        <input type="text" class="form-control form-control-sm" name="icoFilter"
                               th:value="${icoFilter}" th:placeholder="#{customer.ico}">
                    </div>
                    <div class="col-md-2">
                        <input type="text" class="form-control form-control-sm" name="cityFilter"
                               th:value="${cityFilter}" th:placeholder="#{customer.city}">
                    </div>
                    <div class="col-md-2">
                        <input type="text" class="form-control form-control-sm" name="countryFilter"
                               th:value="${countryFilter}" th:placeholder="#{customer.country}">
                    </div>
                    <div class="col-md-2">
                        <input type="text" class="form-control form-control-sm" name="typeFilter"
                               th:value="${typeFilter}" th:placeholder="#{customer.type}">
                    </div>
                    <div class="col-md-2">
                        <input type="text" class="form-control form-control-sm" name="subtypeFilter"
                               th:value="${subtypeFilter}" th:placeholder="#{customer.subtype}">
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-2">
                        <select class="form-select form-select-sm" name="tagFilters" multiple>
                            <option th:each="tag : ${tags}"
                                    th:value="${tag.id}"
                                    th:text="${tag.name}"
                                    th:selected="${tagFilters != null && tagFilters.contains(tag.id)}">
                            </option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select form-select-sm" name="responsiblePersonFilter">
                            <option value="" th:text="#{customer.responsible.person}">Zodpovedná osoba</option>
                            <option th:each="user : ${users}"
                                    th:value="${user.id}"
                                    th:text="${user.fullName}"
                                    th:selected="${responsiblePersonFilter != null && responsiblePersonFilter == user.id}">
                            </option>
                        </select>
                    </div>
                    <div class="col-md-8">
                        <button type="submit" class="btn btn-sm btn-primary" th:text="#{action.filter}">Filtrovať</button>
                        <a th:href="@{/customers}" class="btn btn-sm btn-secondary" th:text="#{action.clear}">Vyčistiť</a>
                    </div>
                </div>
                <!-- Hidden fields to preserve pagination -->
                <input type="hidden" name="page" th:value="${currentPage}">
                <input type="hidden" name="size" th:value="${size}">
                <input type="hidden" name="sortBy" th:value="${sortBy}">
                <input type="hidden" name="sortDir" th:value="${sortDir}">
            </form>
        </div>

        <!-- Scrollable Table Container -->
        <div class="table-responsive-custom">
            <table class="table table-bordered table-striped customer-table">
            <thead>
            <tr>
                <th class="col-name sortable" data-sort="name">
                    <span th:text="#{customer.name}">Názov / meno</span>
                    <i class="fas fa-sort sort-icon" th:classappend="${sortBy == 'name' ? (sortDir == 'asc' ? 'fa-sort-up' : 'fa-sort-down') : ''}"></i>
                </th>
                <th class="col-ico sortable" data-sort="ico">
                    <span th:text="#{customer.ico}">IČO</span>
                    <i class="fas fa-sort sort-icon" th:classappend="${sortBy == 'ico' ? (sortDir == 'asc' ? 'fa-sort-up' : 'fa-sort-down') : ''}"></i>
                </th>
                <th class="col-city sortable" data-sort="billingAddress.city">
                    <span th:text="#{customer.city}">Mesto</span>
                    <i class="fas fa-sort sort-icon" th:classappend="${sortBy == 'billingAddress.city' ? (sortDir == 'asc' ? 'fa-sort-up' : 'fa-sort-down') : ''}"></i>
                </th>
                <th class="col-country sortable" data-sort="billingAddress.country">
                    <span th:text="#{customer.country}">Štát</span>
                    <i class="fas fa-sort sort-icon" th:classappend="${sortBy == 'billingAddress.country' ? (sortDir == 'asc' ? 'fa-sort-up' : 'fa-sort-down') : ''}"></i>
                </th>
                <th class="col-type sortable" data-sort="customerType.name">
                    <span th:text="#{customer.type}">Typ</span>
                    <i class="fas fa-sort sort-icon" th:classappend="${sortBy == 'customerType.name' ? (sortDir == 'asc' ? 'fa-sort-up' : 'fa-sort-down') : ''}"></i>
                </th>
                <th class="col-subtype sortable" data-sort="customerSubtype.name">
                    <span th:text="#{customer.subtype}">Podtyp</span>
                    <i class="fas fa-sort sort-icon" th:classappend="${sortBy == 'customerSubtype.name' ? (sortDir == 'asc' ? 'fa-sort-up' : 'fa-sort-down') : ''}"></i>
                </th>
                <th class="col-responsible sortable" data-sort="responsiblePerson.firstName">
                    <span th:text="#{customer.responsible.person}">Zodpovedný</span>
                    <i class="fas fa-sort sort-icon" th:classappend="${sortBy == 'responsiblePerson.firstName' ? (sortDir == 'asc' ? 'fa-sort-up' : 'fa-sort-down') : ''}"></i>
                </th>
                <th class="col-contact-person">
                    <span th:text="#{customer.contact.person}">Kontaktná osoba</span>
                </th>
                <th class="col-contact-email">
                    <span th:text="#{customer.contact.email}">Kontaktný email</span>
                </th>
                <th class="col-contact-phone">
                    <span th:text="#{customer.contact.phone}">Kontaktný telefón</span>
                </th>
                <th class="col-tags">
                    <span th:text="#{customer.tags}">Menovky</span>
                </th>
                <th class="actions-column" th:text="#{label.actions}">Akcie</th>
            </tr>
            </thead>
            <tbody>
            <tr th:each="customer : ${customers.content}">
                <td class="col-name">
                    <a th:href="@{'/customers/edit/' + ${customer.id}}" th:text="${customer.name}"></a>
                </td>
                <td class="col-ico" th:text="${customer.ico}">ICO</td>
                <td class="col-city" th:text="${customer.billingAddress?.city}">City</td>
                <td class="col-country" th:text="${customer.billingAddress?.country}">Country</td>
                <td class="col-type" th:text="${customer.customerType?.name}">Customer Type</td>
                <td class="col-subtype" th:text="${customer.customerSubtype?.name}">Customer Subtype</td>
                <td class="col-responsible" th:text="${customer.responsiblePerson != null ? customer.responsiblePerson.fullName : ''}">Responsible Person</td>
                <td class="col-contact-person" th:text="${!customer.contacts.isEmpty() ? customer.contacts[0].firstName + ' ' + customer.contacts[0].lastName : ''}">Contact Person</td>
                <td class="col-contact-email" th:text="${!customer.contacts.isEmpty() ? customer.contacts[0].email : ''}">Contact Email</td>
                <td class="col-contact-phone" th:text="${!customer.contacts.isEmpty() ? customer.contacts[0].phone1 : ''}">Contact Phone</td>
                <td class="col-tags">
                    <div class="tags-container">
                        <span th:each="tag : ${customer.tags}" class="tag-badge"
                              th:style="'background-color:' + (${tag.color} != null ? ${tag.color} : '#e6e6fa')"
                              th:text="${tag.name}">
                        </span>
                    </div>
                </td>
                <td class="actions-column">
                    <a th:href="@{'/customers/edit/' + ${customer.id}}" class="action-btn action-btn-edit"
                       th:title="#{action.edit}">
                        <i class="fas fa-edit"></i>
                    </a>
                    <a th:href="@{'/business-events/customer/' + ${customer.id}}" class="action-btn action-btn-event"
                       th:title="#{customer.event.history}">
                        <i class="fas fa-calendar-plus"></i>
                    </a>
                    <a th:href="@{'/quotes/customer/' + ${customer.id}}" class="action-btn action-btn-quote"
                       th:title="#{customer.quote.list}">
                        <i class="fas fa-file-contract"></i>
                    </a>
                    <a th:href="@{'/customers/' + ${customer.id} + '/files'}" class="action-btn action-btn-edit"
                       th:title="#{customer.files.manage}">
                        <i class="fas fa-folder"></i>
                    </a>
                    <a th:href="@{'/tags'}" class="action-btn action-btn-edit"
                       th:title="#{action.tags}">
                        <i class="fa fa-tag" aria-hidden="true"></i>
                    </a>
                    <form th:action="@{'/customers/delete/' + ${customer.id}}" method="post"
                          th:attr="onsubmit='return confirm(\'' + #{customer.delete.confirm} + '\');'" style="display:inline;">
                        <input type="hidden" name="_method" value="DELETE"/>
                        <button type="submit" class="action-btn action-btn-delete"
                                th:title="#{action.delete}" style="border: none; background: none; padding: 0;">
                            <span class="action-btn action-btn-delete">
                                <i class="fas fa-trash"></i>
                            </span>
                        </button>
                    </form>
                </td>
            </tr>
            </tbody>
        </table>
        </div> <!-- End table-responsive-custom -->

        <!-- Pagination Info -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div class="pagination-info">
                Zobrazuje sa <span th:text="${customers.numberOfElements}">0</span> z <span th:text="${customers.totalElements}">0</span> záznamov
                (strana <span th:text="${currentPage + 1}">1</span> z <span th:text="${customers.totalPages}">1</span>)
            </div>
            <div>
                <select class="form-select form-select-sm" id="pageSizeSelect" onchange="changePageSize()">
                    <option value="10" th:selected="${size == 10}">10 na stránku</option>
                    <option value="20" th:selected="${size == 20}">20 na stránku</option>
                    <option value="50" th:selected="${size == 50}">50 na stránku</option>
                    <option value="100" th:selected="${size == 100}">100 na stránku</option>
                </select>
            </div>
        </div>

        <!-- Pagination -->
        <nav aria-label="Customer pagination" th:if="${customers.totalPages > 1}">
            <ul class="pagination justify-content-center">
                <!-- First page -->
                <li class="page-item" th:classappend="${currentPage == 0} ? 'disabled'">
                    <a class="page-link" th:href="@{/customers(page=0, size=${size}, sortBy=${sortBy}, sortDir=${sortDir}, nameFilter=${nameFilter}, icoFilter=${icoFilter}, cityFilter=${cityFilter}, countryFilter=${countryFilter}, typeFilter=${typeFilter}, subtypeFilter=${subtypeFilter}, tagFilters=${tagFilters}, responsiblePersonFilter=${responsiblePersonFilter})}">
                        <i class="fas fa-angle-double-left"></i>
                    </a>
                </li>

                <!-- Previous page -->
                <li class="page-item" th:classappend="${currentPage == 0} ? 'disabled'">
                    <a class="page-link" th:href="@{/customers(page=${currentPage - 1}, size=${size}, sortBy=${sortBy}, sortDir=${sortDir}, nameFilter=${nameFilter}, icoFilter=${icoFilter}, cityFilter=${cityFilter}, countryFilter=${countryFilter}, typeFilter=${typeFilter}, subtypeFilter=${subtypeFilter}, tagFilters=${tagFilters}, responsiblePersonFilter=${responsiblePersonFilter})}">
                        <i class="fas fa-angle-left"></i>
                    </a>
                </li>

                <!-- Page numbers -->
                <li class="page-item" th:each="pageNum : ${#numbers.sequence(T(java.lang.Math).max(0, currentPage - 2), T(java.lang.Math).min(customers.totalPages - 1, currentPage + 2))}"
                    th:classappend="${pageNum == currentPage} ? 'active'">
                    <a class="page-link" th:href="@{/customers(page=${pageNum}, size=${size}, sortBy=${sortBy}, sortDir=${sortDir}, nameFilter=${nameFilter}, icoFilter=${icoFilter}, cityFilter=${cityFilter}, countryFilter=${countryFilter}, typeFilter=${typeFilter}, subtypeFilter=${subtypeFilter}, tagFilters=${tagFilters}, responsiblePersonFilter=${responsiblePersonFilter})}"
                       th:text="${pageNum + 1}">1</a>
                </li>

                <!-- Next page -->
                <li class="page-item" th:classappend="${currentPage == customers.totalPages - 1} ? 'disabled'">
                    <a class="page-link" th:href="@{/customers(page=${currentPage + 1}, size=${size}, sortBy=${sortBy}, sortDir=${sortDir}, nameFilter=${nameFilter}, icoFilter=${icoFilter}, cityFilter=${cityFilter}, countryFilter=${countryFilter}, typeFilter=${typeFilter}, subtypeFilter=${subtypeFilter}, tagFilters=${tagFilters}, responsiblePersonFilter=${responsiblePersonFilter})}">
                        <i class="fas fa-angle-right"></i>
                    </a>
                </li>

                <!-- Last page -->
                <li class="page-item" th:classappend="${currentPage == customers.totalPages - 1} ? 'disabled'">
                    <a class="page-link" th:href="@{/customers(page=${customers.totalPages - 1}, size=${size}, sortBy=${sortBy}, sortDir=${sortDir}, nameFilter=${nameFilter}, icoFilter=${icoFilter}, cityFilter=${cityFilter}, countryFilter=${countryFilter}, typeFilter=${typeFilter}, subtypeFilter=${subtypeFilter}, tagFilters=${tagFilters}, responsiblePersonFilter=${responsiblePersonFilter})}">
                        <i class="fas fa-angle-double-right"></i>
                    </a>
                </li>
            </ul>
        </nav>
    </div>

    <!-- JavaScript for dynamic functionality -->
    <script>
        // Cookie functions
        function setCookie(name, value, days) {
            const expires = new Date();
            expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
            document.cookie = name + '=' + encodeURIComponent(value) + ';expires=' + expires.toUTCString() + ';path=/';
        }

        function getCookie(name) {
            const nameEQ = name + '=';
            const ca = document.cookie.split(';');
            for (let i = 0; i < ca.length; i++) {
                let c = ca[i];
                while (c.charAt(0) === ' ') c = c.substring(1, c.length);
                if (c.indexOf(nameEQ) === 0) return decodeURIComponent(c.substring(nameEQ.length, c.length));
            }
            return null;
        }

        // Filter persistence functions
        function saveFilters() {
            const filters = {
                nameFilter: document.querySelector('input[name="nameFilter"]')?.value || '',
                icoFilter: document.querySelector('input[name="icoFilter"]')?.value || '',
                cityFilter: document.querySelector('input[name="cityFilter"]')?.value || '',
                countryFilter: document.querySelector('input[name="countryFilter"]')?.value || '',
                typeFilter: document.querySelector('input[name="typeFilter"]')?.value || '',
                subtypeFilter: document.querySelector('input[name="subtypeFilter"]')?.value || '',
                responsiblePersonFilter: document.querySelector('select[name="responsiblePersonFilter"]')?.value || '',
                tagFilters: Array.from(document.querySelector('select[name="tagFilters"]')?.selectedOptions || []).map(option => option.value),
                // Also save pagination and sorting
                page: document.querySelector('input[name="page"]')?.value || '0',
                size: document.querySelector('input[name="size"]')?.value || '20',
                sortBy: document.querySelector('input[name="sortBy"]')?.value || 'name',
                sortDir: document.querySelector('input[name="sortDir"]')?.value || 'asc'
            };

            // Save each filter to cookies (expires in 30 days)
            Object.keys(filters).forEach(key => {
                if (key === 'tagFilters') {
                    setCookie('customer_' + key, JSON.stringify(filters[key]), 30);
                } else {
                    setCookie('customer_' + key, filters[key], 30);
                }
            });
        }

        // Function to build customer list URL with current filters
        function buildCustomerListUrl() {
            const filters = {
                nameFilter: getCookie('customer_nameFilter') || '',
                icoFilter: getCookie('customer_icoFilter') || '',
                cityFilter: getCookie('customer_cityFilter') || '',
                countryFilter: getCookie('customer_countryFilter') || '',
                typeFilter: getCookie('customer_typeFilter') || '',
                subtypeFilter: getCookie('customer_subtypeFilter') || '',
                responsiblePersonFilter: getCookie('customer_responsiblePersonFilter') || '',
                page: getCookie('customer_page') || '0',
                size: getCookie('customer_size') || '20',
                sortBy: getCookie('customer_sortBy') || 'name',
                sortDir: getCookie('customer_sortDir') || 'asc'
            };

            // Handle tagFilters
            const savedTagFilters = getCookie('customer_tagFilters');
            let tagFilters = [];
            if (savedTagFilters) {
                try {
                    tagFilters = JSON.parse(savedTagFilters);
                } catch (e) {
                    console.error('Error parsing saved tag filters:', e);
                }
            }

            // Build URL with non-empty filters
            const url = new URL('/customers', window.location.origin);
            Object.keys(filters).forEach(key => {
                if (filters[key] && filters[key] !== '') {
                    url.searchParams.set(key, filters[key]);
                }
            });

            // Add tag filters
            if (tagFilters.length > 0) {
                tagFilters.forEach(tagId => {
                    url.searchParams.append('tagFilters', tagId);
                });
            }

            return url.toString();
        }

        // Make buildCustomerListUrl available globally for other pages
        window.buildCustomerListUrl = buildCustomerListUrl;

        function restoreFilters() {
            // Only restore if no server-side filters are already applied
            const hasServerFilters = /*[[${nameFilter != null || icoFilter != null || cityFilter != null || countryFilter != null || typeFilter != null || subtypeFilter != null || responsiblePersonFilter != null || (tagFilters != null && !tagFilters.isEmpty())}]]*/ false;

            if (hasServerFilters) {
                return; // Don't restore from cookies if server already has filters
            }

            const filterNames = ['nameFilter', 'icoFilter', 'cityFilter', 'countryFilter', 'typeFilter', 'subtypeFilter', 'responsiblePersonFilter'];

            filterNames.forEach(filterName => {
                const savedValue = getCookie('customer_' + filterName);
                if (savedValue) {
                    const element = document.querySelector(`[name="${filterName}"]`);
                    if (element) {
                        element.value = savedValue;
                    }
                }
            });

            // Handle tagFilters (multi-select)
            const savedTagFilters = getCookie('customer_tagFilters');
            if (savedTagFilters) {
                try {
                    const tagValues = JSON.parse(savedTagFilters);
                    const tagSelect = document.querySelector('select[name="tagFilters"]');
                    if (tagSelect && tagValues.length > 0) {
                        Array.from(tagSelect.options).forEach(option => {
                            option.selected = tagValues.includes(option.value);
                        });
                    }
                } catch (e) {
                    console.error('Error parsing saved tag filters:', e);
                }
            }

            // Restore pagination and sorting
            const savedPage = getCookie('customer_page');
            const savedSize = getCookie('customer_size');
            const savedSortBy = getCookie('customer_sortBy');
            const savedSortDir = getCookie('customer_sortDir');

            if (savedSize) {
                const pageSizeSelect = document.getElementById('pageSizeSelect');
                if (pageSizeSelect) {
                    pageSizeSelect.value = savedSize;
                }
            }
        }

        function clearFilterCookies() {
            const filterNames = ['nameFilter', 'icoFilter', 'cityFilter', 'countryFilter', 'typeFilter', 'subtypeFilter', 'responsiblePersonFilter', 'tagFilters', 'page', 'size', 'sortBy', 'sortDir'];
            filterNames.forEach(filterName => {
                setCookie('customer_' + filterName, '', -1); // Set expiry to past date to delete
            });
        }

        // Column visibility management
        function saveColumnVisibility() {
            const visibility = {};
            document.querySelectorAll('.column-toggle-cb').forEach(cb => {
                visibility[cb.id] = cb.checked;
            });
            setCookie('customerTableColumns', JSON.stringify(visibility), 365);
        }

        function loadColumnVisibility() {
            const saved = getCookie('customerTableColumns');
            if (saved) {
                const visibility = JSON.parse(saved);
                Object.keys(visibility).forEach(colId => {
                    const checkbox = document.getElementById(colId);
                    if (checkbox) {
                        checkbox.checked = visibility[colId];
                        toggleColumn(colId, visibility[colId]);
                    }
                });
            }
        }

        function toggleColumn(columnId, show) {
            const className = '.' + columnId;
            document.querySelectorAll(className).forEach(el => {
                el.style.display = show ? '' : 'none';
            });
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Load saved column visibility
            loadColumnVisibility();

            // Restore saved filters
            restoreFilters();

            // Column toggle event listeners
            document.querySelectorAll('.column-toggle-cb').forEach(cb => {
                cb.addEventListener('change', function() {
                    toggleColumn(this.id, this.checked);
                    saveColumnVisibility();
                });
            });

            // Save filters when form is submitted
            const filterForm = document.querySelector('form');
            if (filterForm) {
                filterForm.addEventListener('submit', function() {
                    saveFilters();
                });
            }

            // Save filters when any filter input changes
            const filterInputs = document.querySelectorAll('input[name*="Filter"], select[name*="Filter"]');
            filterInputs.forEach(input => {
                input.addEventListener('change', function() {
                    // Delay saving to allow for multiple rapid changes
                    setTimeout(saveFilters, 100);
                });
            });

            // Clear filters when clear button is clicked
            const clearButton = document.querySelector('a[href="/customers"]');
            if (clearButton) {
                clearButton.addEventListener('click', function() {
                    clearFilterCookies();
                });
            }

            // Sorting event listeners
            document.querySelectorAll('.sortable').forEach(th => {
                th.addEventListener('click', function() {
                    const sortBy = this.dataset.sort;
                    const currentSortBy = '[[${sortBy}]]';
                    const currentSortDir = '[[${sortDir}]]';

                    let newSortDir = 'asc';
                    if (sortBy === currentSortBy && currentSortDir === 'asc') {
                        newSortDir = 'desc';
                    }

                    // Save sort state to cookies
                    setCookie('customer_sortBy', sortBy, 30);
                    setCookie('customer_sortDir', newSortDir, 30);
                    setCookie('customer_page', '0', 30); // Reset to first page

                    // Build URL with current filters
                    const url = new URL(window.location.href);
                    url.searchParams.set('sortBy', sortBy);
                    url.searchParams.set('sortDir', newSortDir);
                    url.searchParams.set('page', '0'); // Reset to first page

                    window.location.href = url.toString();
                });
            });
        });

        // Add click handlers to pagination links to save page state
        document.querySelectorAll('.pagination .page-link').forEach(link => {
            link.addEventListener('click', function(e) {
                const url = new URL(this.href);
                const page = url.searchParams.get('page') || '0';
                setCookie('customer_page', page, 30);
            });
        });

        // Page size change function
        function changePageSize() {
            const newSize = document.getElementById('pageSizeSelect').value;

            // Save page size to cookies
            setCookie('customer_size', newSize, 30);
            setCookie('customer_page', '0', 30); // Reset to first page

            const url = new URL(window.location.href);
            url.searchParams.set('size', newSize);
            url.searchParams.set('page', '0'); // Reset to first page
            window.location.href = url.toString();
        }
    </script>
</section>
</body>
</html>
