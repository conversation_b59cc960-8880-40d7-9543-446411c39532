<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:lang="${#locale.language}"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout}">
<head>
    <meta charset="UTF-8">
    <title th:text="${customer.id != null} ? #{customer.edit.title} : #{customer.create.title}">Customer Form</title>
</head>

<body>
<section layout:fragment="content">
    <div class="container">
        <form th:action="@{/customers/save}" th:object="${customer}" method="post">

            <div th:if="${#fields.hasErrors('*')}" class="alert alert-danger" th:text="#{form.error.fix}">
                Fix errors in form
            </div>

            <h2 th:text="${#fields.hasErrors('*')} ? #{form.error.fix} : (customer.id != null ? #{customer.edit.title} : #{customer.create.title})">
                Customer Form
            </h2>

            <input type="hidden" th:field="*{id}"/>

            <!-- Two-column layout section -->
            <div class="row">
                <!-- Left Column -->
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="companyOrIndividual" class="form-label" th:text="#{label.companyOrIndividual}">Company or Individual</label>
                        <select th:field="*{companyOrIndividual}" class="form-select" id="companyOrIndividual" required>
                            <option value="" disabled th:text="#{select.chooseType}">Choose type</option>
                            <option th:each="type : ${companyOrIndividuals}" th:value="${type}" th:text="${type}"></option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="customerType" class="form-label" th:text="#{label.customerType}">Customer Type</label>
                        <select th:field="*{customerType.id}" class="form-select" id="customerType" required>
                            <option value="" disabled th:text="#{select.chooseType}">Choose type</option>
                            <option th:each="type : ${customerTypes}" th:value="${type.id}" th:text="${type.name}"></option>
                        </select>
                        <div class="text-danger" th:if="${#fields.hasErrors('customerType')}" th:errors="*{customerType}"></div>
                    </div>

                    <div class="mb-3">
                        <label for="ico" class="form-label" th:text="#{label.ico}">ICO</label>
                        <input type="text" th:field="*{ico}" class="form-control" id="ico"/>
                    </div>

                    <div class="mb-3">
                        <label for="icDph" class="form-label" th:text="#{label.icDph}">IC DPH</label>
                        <input type="text" th:field="*{icDph}" class="form-control" id="icDph"/>
                    </div>

                    <div class="mb-3">
                        <label for="invoiceDueDays" class="form-label" th:text="#{label.invoiceDueDays}">Invoice Due Days</label>
                        <input type="number" th:field="*{invoiceDueDays}" class="form-control" id="invoiceDueDays"/>
                    </div>

                    <div class="mb-3">
                        <label for="customerNumber" class="form-label" th:text="#{label.customerNumber}">Customer Number</label>
                        <input type="text" th:field="*{customerNumber}" class="form-control" id="customerNumber"/>
                    </div>
                </div>

                <!-- Right Column -->
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="name" class="form-label" th:text="#{label.name}">Name</label>
                        <input type="text" th:field="*{name}" class="form-control" id="name" required/>
                        <div class="text-danger" th:if="${#fields.hasErrors('name')}" th:errors="*{name}"></div>
                    </div>

                    <div class="mb-3">
                        <label for="customerSubtype" class="form-label" th:text="#{label.customerSubtype}">Customer Subtype</label>
                        <select th:field="*{customerSubtype.id}" class="form-select" id="customerSubtype" required>
                            <option value="" disabled th:text="#{select.chooseSubtype}">Choose subtype</option>
                            <option th:each="subtype : ${customerSubtypes}" th:value="${subtype.id}" th:text="${subtype.name}"></option>
                        </select>
                        <div class="text-danger" th:if="${#fields.hasErrors('customerSubtype')}" th:errors="*{customerSubtype}"></div>
                    </div>

                    <div class="mb-3">
                        <label for="dic" class="form-label" th:text="#{label.dic}">DIC</label>
                        <input type="text" th:field="*{dic}" class="form-control" id="dic"/>
                    </div>

                    <div class="mb-3">
                        <label for="note" class="form-label" th:text="#{label.note}">Note</label>
                        <textarea th:field="*{note}" class="form-control" id="note" rows="3"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="responsiblePerson" class="form-label" th:text="#{label.responsiblePerson}">Responsible Person</label>
                        <select th:field="*{responsiblePerson}" class="form-select" id="responsiblePerson" required>
                            <option value="" disabled th:text="#{select.choosePerson}">Choose person</option>
                            <option th:each="user : ${users}" th:value="${user.id}"
                                    th:text="${user.firstName} + ' ' + ${user.lastName}"></option>
                        </select>
                        <div class="text-danger" th:if="${#fields.hasErrors('responsiblePerson')}" th:errors="*{responsiblePerson}"></div>
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label class="form-label" th:text="#{label.tags}">Tags</label>

                <select th:field="*{tags}" class="d-none" id="tags" multiple>
                    <option th:each="tag : ${tags}"
                            th:value="${tag.id}"
                            th:attr="data-tag-id=${tag.id}"></option>
                </select>

                <select class="form-select" id="tagsDropdown">
                    <option value="" disabled selected th:text="#{select.chooseTags}">Choose tags</option>
                    <option th:each="tag : ${tags}"
                            th:value="${tag.id}"
                            th:attr="data-color=${tag.color ?: '#6c757d'}"
                            th:text="${tag.name}"></option>
                </select>

                <div class="selected-tags-container mt-2 p-2 border rounded" id="selectedTags">
                    <span class="badge me-2 mb-2"
                          th:each="tag : ${customer.tags}"
                          th:style="'background-color:' + ${tag.color ?: '#6c757d'} + '; color: white;'"
                          th:attr="data-tag-id=${tag.id}">
                        <span th:text="${tag.name}"></span>
                        <button type="button" class="ms-2 btn-close btn-close-white"
                                onclick="removeTag(this)"></button>
                    </span>
                </div>
            </div>

            <!-- Contact Information Section -->
            <div th:replace="~{fragments/contact-fields :: contact-fields}"></div>

            <div th:replace="~{fragments/address-fields :: address-fields('billingAddress', #{label.billingAddress})}"></div>
            <div th:replace="~{fragments/address-fields :: address-fields('correspondenceAddress', #{label.correspondenceAddress})}"></div>
            <div th:replace="~{fragments/address-fields :: address-fields('deliveryAddress', #{label.deliveryAddress})}"></div>

            <button type="submit" class="btn btn-success" th:text="#{button.save}">Save</button>
            <a th:href="@{/customers}" id="backToCustomerListBtn" class="btn btn-secondary" th:text="#{button.cancel}">Cancel</a>
        </form>
    </div>
</section>

<th:block layout:fragment="scripts">
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Set up back button with saved filters
            setupBackButton();

            const tagSelect = document.getElementById('tags');
            const tagsDropdown = document.getElementById('tagsDropdown');
            const selectedTagsContainer = document.getElementById('selectedTags');

            // Initialize with existing tags
            updateHiddenSelect();

            // Handle tag selection from dropdown
            tagsDropdown.addEventListener('change', function() {
                if (this.value) {
                    const selectedOption = this.options[this.selectedIndex];
                    const tagId = this.value;
                    const tagName = selectedOption.text;
                    const tagColor = selectedOption.dataset.color;

                    // Add to display
                    addTagToDisplay(tagId, tagName, tagColor);

                    // Add to hidden select
                    addTagToHiddenSelect(tagId);

                    // Reset dropdown
                    this.value = '';
                }
            });

            // Style dropdown options
            Array.from(tagsDropdown.options).forEach(option => {
                if (option.value) {
                    const color = option.dataset.color || '#6c757d';
                    option.style.backgroundColor = color;
                    option.style.color = getContrastColor(color);
                }
            });
        });

        function addTagToDisplay(tagId, tagName, tagColor) {
            const selectedTagsContainer = document.getElementById('selectedTags');

            // Check if tag already exists
            if (document.querySelector(`#selectedTags [data-tag-id="${tagId}"]`)) {
                return;
            }

            const tagElement = document.createElement('span');
            tagElement.className = 'badge me-2 mb-2';
            tagElement.style.backgroundColor = tagColor;
            tagElement.style.color = getContrastColor(tagColor);
            tagElement.setAttribute('data-tag-id', tagId);

            tagElement.innerHTML = `
                ${tagName}
                <button type="button" class="ms-2 btn-close btn-close-white" onclick="removeTag(this)"></button>
            `;

            selectedTagsContainer.appendChild(tagElement);
        }

        function addTagToHiddenSelect(tagId) {
            const tagSelect = document.getElementById('tags');

            // Check if already selected
            if (Array.from(tagSelect.options).some(opt => opt.value === tagId)) {
                return;
            }

            const option = document.createElement('option');
            option.value = tagId;
            option.selected = true;
            option.setAttribute('data-tag-id', tagId);
            tagSelect.appendChild(option);
        }

        function removeTag(button) {
            const tagElement = button.parentElement;
            const tagId = tagElement.getAttribute('data-tag-id');

            // Remove from display
            tagElement.remove();

            // Remove from hidden select
            const tagSelect = document.getElementById('tags');
            const optionToRemove = Array.from(tagSelect.options)
                .find(opt => opt.value === tagId);
            if (optionToRemove) {
                tagSelect.removeChild(optionToRemove);
            }
        }

        function updateHiddenSelect() {
            const tagSelect = document.getElementById('tags');
            const selectedTags = document.querySelectorAll('#selectedTags [data-tag-id]');

            // Clear existing options except the ones that match displayed tags
            Array.from(tagSelect.options).forEach(option => {
                const isDisplayed = Array.from(selectedTags).some(
                    tag => tag.getAttribute('data-tag-id') === option.value
                );
                if (!isDisplayed) {
                    tagSelect.removeChild(option);
                }
            });

            // Add any displayed tags that aren't in the hidden select
            selectedTags.forEach(tag => {
                const tagId = tag.getAttribute('data-tag-id');
                if (!Array.from(tagSelect.options).some(opt => opt.value === tagId)) {
                    addTagToHiddenSelect(tagId);
                }
            });
        }

        function getContrastColor(hexColor) {
            hexColor = hexColor.replace('#', '');
            const r = parseInt(hexColor.substring(0, 2), 16);
            const g = parseInt(hexColor.substring(2, 4), 16);
            const b = parseInt(hexColor.substring(4, 6), 16);
            const luminance = (0.2126 * r + 0.7152 * g + 0.0722 * b) / 255;
            return luminance > 0.5 ? 'black' : 'white';
        }

        // Function to get cookie value
        function getCookie(name) {
            const nameEQ = name + '=';
            const ca = document.cookie.split(';');
            for (let i = 0; i < ca.length; i++) {
                let c = ca[i];
                while (c.charAt(0) === ' ') c = c.substring(1, c.length);
                if (c.indexOf(nameEQ) === 0) return decodeURIComponent(c.substring(nameEQ.length, c.length));
            }
            return null;
        }

        // Function to build customer list URL with saved filters
        function buildCustomerListUrl() {
            const filters = {
                nameFilter: getCookie('customer_nameFilter') || '',
                icoFilter: getCookie('customer_icoFilter') || '',
                cityFilter: getCookie('customer_cityFilter') || '',
                countryFilter: getCookie('customer_countryFilter') || '',
                typeFilter: getCookie('customer_typeFilter') || '',
                subtypeFilter: getCookie('customer_subtypeFilter') || '',
                responsiblePersonFilter: getCookie('customer_responsiblePersonFilter') || '',
                page: getCookie('customer_page') || '0',
                size: getCookie('customer_size') || '20',
                sortBy: getCookie('customer_sortBy') || 'name',
                sortDir: getCookie('customer_sortDir') || 'asc'
            };

            // Handle tagFilters
            const savedTagFilters = getCookie('customer_tagFilters');
            let tagFilters = [];
            if (savedTagFilters) {
                try {
                    tagFilters = JSON.parse(savedTagFilters);
                } catch (e) {
                    console.error('Error parsing saved tag filters:', e);
                }
            }

            // Build URL with non-empty filters
            const url = new URL('/customers', window.location.origin);
            Object.keys(filters).forEach(key => {
                if (filters[key] && filters[key] !== '') {
                    url.searchParams.set(key, filters[key]);
                }
            });

            // Add tag filters
            if (tagFilters.length > 0) {
                tagFilters.forEach(tagId => {
                    url.searchParams.append('tagFilters', tagId);
                });
            }

            return url.toString();
        }

        // Set up back button
        function setupBackButton() {
            const backBtn = document.getElementById('backToCustomerListBtn');
            if (backBtn) {
                try {
                    const urlWithFilters = buildCustomerListUrl();
                    backBtn.href = urlWithFilters;
                    console.log('Back button URL set to:', urlWithFilters);
                } catch (error) {
                    console.error('Error setting up back button:', error);
                    // Fallback to basic customer list URL
                    backBtn.href = '/customers';
                }
            }
        }
    </script>
    <style>
        .selected-tags-container {
            min-height: 60px;
            background-color: #f8f9fa;
        }

        .badge {
            font-size: 0.9rem;
            padding: 0.5em 0.75em;
            display: inline-flex;
            align-items: center;
        }

        .btn-close {
            font-size: 0.75em;
            opacity: 0.8;
        }

        .btn-close:hover {
            opacity: 1;
        }

        .accordion-button:not(.collapsed) {
            background-color: #f8f9fa;
            box-shadow: inset 0 -1px 0 rgba(0, 0, 0, .125);
        }

        .address-section {
            margin-bottom: 1.5rem;
            border: 1px solid #dee2e6;
            border-radius: 0.25rem;
        }

        .contact-section {
            margin-bottom: 1.5rem;
            border: 1px solid #dee2e6;
            border-radius: 0.25rem;
        }

        .contact-item {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef !important;
        }
    </style>

    <script>
        let contactIndex = 0;

        // Initialize contact index based on existing contacts
        document.addEventListener('DOMContentLoaded', function() {
            const existingContacts = document.querySelectorAll('.contact-item');
            contactIndex = existingContacts.length;
            updateContactBadge();
        });

        function addNewContact() {
            const container = document.getElementById('contactsContainer');
            const newContactHtml = `
                <div id="contact-${contactIndex}" class="contact-item border rounded p-3 mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">Kontakt ${contactIndex + 1}</h6>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeContact(${contactIndex})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>

                    <input type="hidden" name="contacts[${contactIndex}].id" value="" />

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="contactTitle${contactIndex}" class="form-label">Titul</label>
                            <input type="text" name="contacts[${contactIndex}].title" id="contactTitle${contactIndex}" class="form-control" />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="contactTitleAfterName${contactIndex}" class="form-label">Titul za menom</label>
                            <input type="text" name="contacts[${contactIndex}].titleAfterName" id="contactTitleAfterName${contactIndex}" class="form-control" />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="contactFirstName${contactIndex}" class="form-label">Meno</label>
                            <input type="text" name="contacts[${contactIndex}].firstName" id="contactFirstName${contactIndex}" class="form-control" />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="contactLastName${contactIndex}" class="form-label">Priezvisko</label>
                            <input type="text" name="contacts[${contactIndex}].lastName" id="contactLastName${contactIndex}" class="form-control" />
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="contactPosition${contactIndex}" class="form-label">Pozícia</label>
                        <input type="text" name="contacts[${contactIndex}].position" id="contactPosition${contactIndex}" class="form-control" />
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="contactEmail${contactIndex}" class="form-label">Email</label>
                            <input type="email" name="contacts[${contactIndex}].email" id="contactEmail${contactIndex}" class="form-control" />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="contactPhone1${contactIndex}" class="form-label">Telefón 1</label>
                            <input type="tel" name="contacts[${contactIndex}].phone1" id="contactPhone1${contactIndex}" class="form-control" />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="contactPhone2${contactIndex}" class="form-label">Telefón 2</label>
                            <input type="tel" name="contacts[${contactIndex}].phone2" id="contactPhone2${contactIndex}" class="form-control" />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="contactOtherContact${contactIndex}" class="form-label">Iný kontakt</label>
                            <input type="text" name="contacts[${contactIndex}].otherContact" id="contactOtherContact${contactIndex}" class="form-control" />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                <input type="checkbox" name="contacts[${contactIndex}].newsletterSubscribed" id="contactNewsletter${contactIndex}" class="form-check-input" value="true" />
                                <input type="hidden" name="contacts[${contactIndex}].newsletterSubscribed" value="false" />
                                <label for="contactNewsletter${contactIndex}" class="form-check-label">Odber newslettera</label>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                <input type="checkbox" name="contacts[${contactIndex}].mainContact" id="contactMain${contactIndex}" class="form-check-input" value="true" />
                                <input type="hidden" name="contacts[${contactIndex}].mainContact" value="false" />
                                <label for="contactMain${contactIndex}" class="form-check-label">Hlavný kontakt</label>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="contactNote${contactIndex}" class="form-label">Poznámka</label>
                        <textarea name="contacts[${contactIndex}].note" id="contactNote${contactIndex}" class="form-control" rows="2"></textarea>
                    </div>
                </div>
            `;

            container.insertAdjacentHTML('beforeend', newContactHtml);
            contactIndex++;
            updateContactBadge();
        }

        function removeContact(index) {
            const contactElement = document.getElementById('contact-' + index);
            if (contactElement) {
                contactElement.remove();
                updateContactBadge();
            }
        }

        function updateContactBadge() {
            const badge = document.querySelector('#contactsCollapse').previousElementSibling.querySelector('.badge');
            const contactCount = document.querySelectorAll('.contact-item').length;
            if (badge) {
                badge.textContent = contactCount;
            }
        }
    </script>
</th:block>

</body>
</html>
