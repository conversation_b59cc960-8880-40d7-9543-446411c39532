<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:lang="${#locale.language}"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout}">
<head>
    <meta charset="UTF-8">
    <title th:text="#{customer.files.title} + ' - ' + ${customer.name}">Customer Files</title>
    <style>
        .file-upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 0.375rem;
            padding: 2rem;
            text-align: center;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
        }
        .file-upload-area:hover {
            border-color: #0d6efd;
            background-color: #e7f1ff;
        }
        .file-upload-area.dragover {
            border-color: #0d6efd;
            background-color: #cfe2ff;
        }
        .file-item {
            transition: all 0.2s ease;
        }
        .file-item:hover {
            background-color: #f8f9fa;
        }
        .file-icon {
            font-size: 1.5rem;
            margin-right: 0.5rem;
        }
        .file-actions {
            opacity: 0.7;
            transition: opacity 0.2s ease;
        }
        .file-item:hover .file-actions {
            opacity: 1;
        }
    </style>
</head>

<body>
<section layout:fragment="content">
    <div class="container-fluid">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 th:text="#{customer.files.title}">Súbory zákazníka</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a th:href="@{/customers}" th:text="#{customer.list.title}">Zákazníci</a>
                        </li>
                        <li class="breadcrumb-item">
                            <a th:href="@{'/customers/edit/' + ${customer.id}}" th:text="${customer.name}">Customer Name</a>
                        </li>
                        <li class="breadcrumb-item active" th:text="#{customer.files.title}">Súbory</li>
                    </ol>
                </nav>
            </div>
            <div>
                <a th:href="@{/customers}" id="backToCustomerListBtn" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-list"></i> <span th:text="#{button.backToCustomerList}">Späť na zoznam zákazníkov</span>
                </a>
                <a th:href="@{'/customers/edit/' + ${customer.id}}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> <span th:text="#{action.back}">Späť</span>
                </a>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i> <span th:text="${success}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i> <span th:text="${error}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- File Upload Section -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cloud-upload-alt"></i> <span th:text="#{customer.files.upload}">Nahrať súbor</span>
                </h5>
            </div>
            <div class="card-body">
                <form th:action="@{'/customers/' + ${customer.id} + '/files/upload'}" 
                      method="post" 
                      enctype="multipart/form-data" 
                      id="uploadForm">
                    <div class="file-upload-area" id="fileUploadArea">
                        <div class="mb-3">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                            <h6 th:text="#{customer.files.upload.drag}">Pretiahnite súbory sem alebo kliknite na výber</h6>
                            <input type="file" 
                                   name="file" 
                                   id="fileInput" 
                                   class="form-control" 
                                   style="display: none;" 
                                   multiple 
                                   accept="*/*">
                            <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('fileInput').click()">
                                <i class="fas fa-folder-open"></i> <span th:text="#{customer.files.select}">Vybrať súbory</span>
                            </button>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label" th:text="#{label.description}">Popis (voliteľný)</label>
                            <input type="text" name="description" id="description" class="form-control" 
                                   th:placeholder="#{customer.files.description.placeholder}">
                        </div>
                        <div id="selectedFiles" class="mt-3" style="display: none;">
                            <h6 th:text="#{customer.files.selected}">Vybrané súbory:</h6>
                            <div id="fileList"></div>
                            <button type="submit" class="btn btn-success mt-2">
                                <i class="fas fa-upload"></i> <span th:text="#{customer.files.upload.button}">Nahrať</span>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Files List -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-folder"></i> <span th:text="#{customer.files.list}">Zoznam súborov</span>
                    <span class="badge bg-secondary ms-2" th:text="${#lists.size(files)}">0</span>
                </h5>
                <div>
                    <small class="text-muted" th:text="#{customer.files.total} + ': ' + ${#lists.size(files)}">Celkom súborov: 0</small>
                </div>
            </div>
            <div class="card-body">
                <div th:if="${#lists.isEmpty(files)}" class="text-center text-muted py-4">
                    <i class="fas fa-folder-open fa-3x mb-3"></i>
                    <p th:text="#{customer.files.empty}">Žiadne súbory neboli nájdené.</p>
                </div>
                
                <div th:if="${!#lists.isEmpty(files)}">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th th:text="#{customer.files.filename}">Názov súboru</th>
                                    <th th:text="#{customer.files.size}">Veľkosť</th>
                                    <th th:text="#{customer.files.modified}">Upravené</th>
                                    <th th:text="#{label.actions}">Akcie</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr th:each="file : ${files}" class="file-item">
                                    <td>
                                        <i class="fas fa-file file-icon text-muted"></i>
                                        <span th:text="${file.originalFilename}">filename.pdf</span>
                                    </td>
                                    <td th:text="${file.formattedSize}">1.2 MB</td>
                                    <td th:text="${#temporals.format(file.lastModified, 'yyyy-MM-dd HH:mm')}">2023-12-01 10:30</td>
                                    <td class="file-actions">
                                        <a th:href="@{'/customers/' + ${customer.id} + '/files/download/' + ${file.filename}}" 
                                           class="btn btn-sm btn-outline-primary me-1"
                                           th:title="#{action.download}">
                                            <i class="fas fa-download"></i>
                                        </a>
                                        <form th:action="@{'/customers/' + ${customer.id} + '/files/delete/' + ${file.filename}}" 
                                              method="post" 
                                              style="display: inline;"
                                              th:attr="onsubmit='return confirm(\'' + #{customer.files.delete.confirm} + '\');'">
                                            <button type="submit" 
                                                    class="btn btn-sm btn-outline-danger"
                                                    th:title="#{action.delete}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // File upload functionality
        const fileUploadArea = document.getElementById('fileUploadArea');
        const fileInput = document.getElementById('fileInput');
        const selectedFiles = document.getElementById('selectedFiles');
        const fileList = document.getElementById('fileList');

        // Click to select files
        fileUploadArea.addEventListener('click', function(e) {
            if (e.target === fileUploadArea || e.target.closest('.file-upload-area')) {
                fileInput.click();
            }
        });

        // Drag and drop functionality
        fileUploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            fileUploadArea.classList.add('dragover');
        });

        fileUploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            fileUploadArea.classList.remove('dragover');
        });

        fileUploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            fileUploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            fileInput.files = files;
            displaySelectedFiles(files);
        });

        // File selection change
        fileInput.addEventListener('change', function() {
            displaySelectedFiles(this.files);
        });

        function displaySelectedFiles(files) {
            if (files.length > 0) {
                fileList.innerHTML = '';
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    const fileItem = document.createElement('div');
                    fileItem.className = 'alert alert-info py-2 mb-1';
                    fileItem.innerHTML = `
                        <i class="fas fa-file"></i> 
                        <strong>${file.name}</strong> 
                        <small class="text-muted">(${formatFileSize(file.size)})</small>
                    `;
                    fileList.appendChild(fileItem);
                }
                selectedFiles.style.display = 'block';
            } else {
                selectedFiles.style.display = 'none';
            }
        }

        function formatFileSize(bytes) {
            if (bytes < 1024) return bytes + ' B';
            if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
            if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
            return (bytes / (1024 * 1024 * 1024)).toFixed(1) + ' GB';
        }

        // Set up back button to preserve customer list filters
        function setupBackButton() {
            const backBtn = document.getElementById('backToCustomerListBtn');
            if (backBtn) {
                try {
                    const urlWithFilters = buildCustomerListUrl();
                    backBtn.href = urlWithFilters;
                    console.log('Back button URL set to:', urlWithFilters);
                } catch (error) {
                    console.error('Error setting up back button:', error);
                    // Fallback to basic customer list URL
                    backBtn.href = '/customers';
                }
            }
        }

        function buildCustomerListUrl() {
            const url = new URL('/customers', window.location.origin);

            // Get filters from localStorage
            const filters = JSON.parse(localStorage.getItem('customerFilters') || '{}');

            // Add filters to URL
            Object.keys(filters).forEach(key => {
                if (filters[key] !== null && filters[key] !== undefined && filters[key] !== '') {
                    if (Array.isArray(filters[key])) {
                        filters[key].forEach(value => url.searchParams.append(key, value));
                    } else {
                        url.searchParams.set(key, filters[key]);
                    }
                }
            });

            return url.toString();
        }

        // Initialize back button when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setupBackButton();
        });
    </script>
</section>
</body>
</html>
