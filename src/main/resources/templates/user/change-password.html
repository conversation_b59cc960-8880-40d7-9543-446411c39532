<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:lang="${#locale.language}"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout}">
<head>
    <meta charset="UTF-8" />
    <title th:text="#{user.change.password.title}">Change Password</title>
</head>

<body>
<section layout:fragment="content">
    <div class="container">
        <h2 th:text="#{user.change.password.title}">Change Password</h2>

        <div th:if="${error}" class="alert alert-danger" th:text="${error}"></div>

        <form th:action="@{/users/change-password}" method="post" th:object="${passwordChangeForm}">
            <input type="hidden" th:field="*{userId}" />

            <div class="mb-3">
                <label for="currentPassword" th:text="#{user.current.password}">Current Password</label>
                <input type="password" th:field="*{currentPassword}" class="form-control" id="currentPassword" required />
            </div>

            <div class="mb-3">
                <label for="newPassword" th:text="#{user.new.password}">New Password</label>
                <input type="password" th:field="*{newPassword}" class="form-control" id="newPassword" required />
            </div>

            <div class="mb-3">
                <label for="confirmPassword" th:text="#{user.confirm.new.password}">Confirm New Password</label>
                <input type="password" th:field="*{confirmPassword}" class="form-control" id="confirmPassword" required />
            </div>

            <button type="submit" class="btn btn-primary" th:text="#{user.change.password.button}">Change Password</button>
        </form>
    </div>
</section>
</body>
</html>
