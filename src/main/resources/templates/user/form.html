<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:lang="${#locale.language}"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout}">
<head>
    <meta charset="UTF-8" />
    <title th:text="${user.id == null} ? #{user.create.title} : #{user.edit.title}">Create User</title>
</head>

<body>
<section layout:fragment="content">
    <div class="container">
        <h2 th:text="${user.id == null} ? #{user.create.title} : #{user.edit.title}">Create User</h2>

        <form th:action="@{/users/save}" method="post" th:object="${user}">
            <input type="hidden" th:field="*{id}" />

            <div class="mb-3">
                <label for="firstName" th:text="#{user.first.name}">First Name</label>
                <input type="text" th:field="*{firstName}" class="form-control" id="firstName" />
            </div>

            <div class="mb-3">
                <label for="lastName" th:text="#{user.last.name}">Last Name</label>
                <input type="text" th:field="*{lastName}" class="form-control" id="lastName" />
            </div>

            <div class="mb-3">
                <label for="email" th:text="#{user.email}">Email</label>
                <input type="text" th:field="*{email}" class="form-control" id="email" />
            </div>

            <button type="submit" class="btn btn-primary" th:text="#{action.save}">Save</button>
            <a th:href="@{/users}" class="btn btn-secondary" th:text="#{action.cancel}">Cancel</a>
        </form>
    </div>
</section>
</body>
</html>
