<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:lang="${#locale.language}"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout}">
<head>
    <meta charset="UTF-8" />
    <title th:text="#{user.list.title}">Users</title>
    <style>
        .user-enabled {
            background-color: #d4edda !important; /* Light green background for enabled users */
        }
        .user-disabled {
            background-color: #f8d7da !important; /* Light red background for disabled users */
        }
        .action-btn {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            margin: 0 0.1rem;
            border-radius: 0.25rem;
            text-decoration: none;
            transition: all 0.2s ease;
            border: none;
            font-size: 0.875rem;
        }
        .action-btn:hover {
            transform: scale(1.05);
            text-decoration: none;
        }
        .action-btn-edit {
            color: #6c757d;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
        }
        .action-btn-edit:hover {
            color: #495057;
            background-color: #e9ecef;
        }
        .action-btn-enable {
            color: #198754;
            background-color: #d1e7dd;
            border: 1px solid #badbcc;
        }
        .action-btn-enable:hover {
            color: #146c43;
            background-color: #a3cfbb;
        }
        .action-btn-disable {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
        }
        .action-btn-disable:hover {
            color: #721c24;
            background-color: #f1aeb5;
        }
    </style>
</head>

<body>
<section layout:fragment="content">
    <div class="container">
        <h2 th:text="#{user.list.title}">User List</h2>

        <a th:href="@{/users/new}" class="btn btn-primary mb-3" th:text="#{user.add}">Add User</a>

        <table class="table table-bordered table-striped">
            <thead>
            <tr>
                <th th:text="#{user.first.name}">First Name</th>
                <th th:text="#{user.last.name}">Last Name</th>
                <th th:text="#{user.email}">Email</th>
                <th th:text="#{user.enabled}">Status</th>
                <th th:text="#{label.actions}">Actions</th>
            </tr>
            </thead>
            <tbody>
            <tr th:each="user : ${users}"
                th:class="${user.enabled} ? 'user-enabled' : 'user-disabled'">
                <td th:text="${user.firstName}">First Name</td>
                <td th:text="${user.lastName}">Last Name</td>
                <td th:text="${user.email}">Email</td>
                <td>
                    <span th:if="${user.enabled}" class="badge bg-success" th:text="#{user.status.enabled}">Aktívny</span>
                    <span th:unless="${user.enabled}" class="badge bg-danger" th:text="#{user.status.disabled}">Deaktivovaný</span>
                </td>
                <td>
                    <!-- Edit Button -->
                    <a th:href="@{'/users/edit/' + ${user.id}}" class="action-btn action-btn-edit"
                       th:title="#{action.edit}">
                        <i class="fas fa-edit"></i>
                    </a>

                    <!-- Enable/Disable Button -->
                    <form th:if="${user.enabled}" th:action="@{'/users/disable/' + ${user.id}}" method="post"
                          style="display:inline;"
                          th:attr="onsubmit='return confirm(\'' + #{user.disable.confirm} + '\');'">
                        <button type="submit" class="action-btn action-btn-disable"
                                th:title="#{user.disable}">
                            <i class="fas fa-user-slash"></i>
                        </button>
                    </form>

                    <form th:unless="${user.enabled}" th:action="@{'/users/enable/' + ${user.id}}" method="post"
                          style="display:inline;"
                          th:attr="onsubmit='return confirm(\'' + #{user.enable.confirm} + '\');'">
                        <button type="submit" class="action-btn action-btn-enable"
                                th:title="#{user.enable}">
                            <i class="fas fa-user-check"></i>
                        </button>
                    </form>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
</section>
</body>
</html>
