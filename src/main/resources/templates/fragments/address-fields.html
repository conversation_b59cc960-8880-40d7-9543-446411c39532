<div th:fragment="address-fields(addressPrefix, addressLabel)" class="address-section">
    <div class="accordion">
        <div class="accordion-item">
            <h2 class="accordion-header">
                <button class="accordion-button collapsed" type="button"
                        data-bs-toggle="collapse"
                        th:data-bs-target="'#' + ${addressPrefix} + 'Collapse'"
                        th:aria-controls="${addressPrefix + 'Collapse'}"
                        aria-expanded="false">
                    <span th:text="${addressLabel}">Address</span>
                </button>
            </h2>
            <div th:id="${addressPrefix + 'Collapse'}" class="accordion-collapse collapse"
                 th:aria-labelledby="${addressPrefix + 'Heading'}">
                <div class="accordion-body">
                    <!-- Street Field -->
                    <div class="mb-3">
                        <label th:for="${addressPrefix + 'Street'}" class="form-label" th:text="#{label.street}">Street</label>
                        <input type="text"
                               th:name="${addressPrefix + '.street'}"
                               th:id="${addressPrefix + 'Street'}"
                               th:value="${customer.__${addressPrefix}__?.street}"
                               class="form-control" />
                        <div class="text-danger" th:if="${#fields.hasErrors(addressPrefix + '.street')}"
                             th:errors="*{__${addressPrefix}.street__}"></div>
                    </div>

                    <!-- ZIP and City Fields -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label th:for="${addressPrefix + 'Zip'}" class="form-label" th:text="#{label.zip}">ZIP Code</label>
                            <input type="text"
                                   th:name="${addressPrefix + '.zip'}"
                                   th:id="${addressPrefix + 'Zip'}"
                                   th:value="${customer.__${addressPrefix}__?.zip}"
                                   class="form-control" />
                            <div class="text-danger" th:if="${#fields.hasErrors(addressPrefix + '.zip')}"
                                 th:errors="*{__${addressPrefix}.zip__}"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label th:for="${addressPrefix + 'City'}" class="form-label" th:text="#{label.city}">City</label>
                            <input type="text"
                                   th:name="${addressPrefix + '.city'}"
                                   th:id="${addressPrefix + 'City'}"
                                   th:value="${customer.__${addressPrefix}__?.city}"
                                   class="form-control" />
                            <div class="text-danger" th:if="${#fields.hasErrors(addressPrefix + '.city')}"
                                 th:errors="*{__${addressPrefix}.city__}"></div>
                        </div>
                    </div>

                    <!-- Country Code and Country Fields -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label th:for="${addressPrefix + 'CountryCode'}" class="form-label" th:text="#{label.countryCode}">Country Code</label>
                            <input type="text"
                                   th:name="${addressPrefix + '.countryCode'}"
                                   th:id="${addressPrefix + 'CountryCode'}"
                                   th:value="${customer.__${addressPrefix}__?.countryCode}"
                                   class="form-control" />
                            <div class="text-danger" th:if="${#fields.hasErrors(addressPrefix + '.countryCode')}"
                                 th:errors="*{__${addressPrefix}.countryCode__}"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label th:for="${addressPrefix + 'Country'}" class="form-label" th:text="#{label.country}">Country</label>
                            <input type="text"
                                   th:name="${addressPrefix + '.country'}"
                                   th:id="${addressPrefix + 'Country'}"
                                   th:value="${customer.__${addressPrefix}__?.country}"
                                   class="form-control" />
                            <div class="text-danger" th:if="${#fields.hasErrors(addressPrefix + '.country')}"
                                 th:errors="*{__${addressPrefix}.country__}"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
