<div th:fragment="contact-fields" class="contact-section">
    <div class="accordion">
        <div class="accordion-item">
            <h2 class="accordion-header">
                <button class="accordion-button collapsed" type="button"
                        data-bs-toggle="collapse"
                        data-bs-target="#contactsCollapse"
                        aria-controls="contactsCollapse"
                        aria-expanded="false">
                    <span th:text="#{customer.contacts}">Kontaktné osoby</span>
                    <span class="badge bg-secondary ms-2" th:text="${#lists.size(customer.contacts)}">0</span>
                </button>
            </h2>
            <div id="contactsCollapse" class="accordion-collapse collapse"
                 aria-labelledby="contactsHeading">
                <div class="accordion-body">
                    <div id="contactsContainer">
                        <!-- Existing contacts -->
                        <div th:each="contact, contactStat : ${customer.contacts}" 
                             th:id="'contact-' + ${contactStat.index}" 
                             class="contact-item border rounded p-3 mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="mb-0" th:text="#{customer.contact} + ' ' + (${contactStat.index + 1})">Kontakt 1</h6>
                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                        th:onclick="'removeContact(' + ${contactStat.index} + ')'"
                                        th:if="${contactStat.index > 0}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                            
                            <input type="hidden" th:name="'contacts[' + ${contactStat.index} + '].id'" 
                                   th:value="${contact.id}"/>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label th:for="'contactTitle' + ${contactStat.index}" class="form-label" th:text="#{label.title}">Titul</label>
                                    <input type="text" 
                                           th:name="'contacts[' + ${contactStat.index} + '].title'"
                                           th:id="'contactTitle' + ${contactStat.index}"
                                           th:value="${contact.title}"
                                           class="form-control" />
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label th:for="'contactTitleAfterName' + ${contactStat.index}" class="form-label" th:text="#{label.titleAfterName}">Titul za menom</label>
                                    <input type="text" 
                                           th:name="'contacts[' + ${contactStat.index} + '].titleAfterName'"
                                           th:id="'contactTitleAfterName' + ${contactStat.index}"
                                           th:value="${contact.titleAfterName}"
                                           class="form-control" />
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label th:for="'contactFirstName' + ${contactStat.index}" class="form-label" th:text="#{label.firstName}">Meno</label>
                                    <input type="text" 
                                           th:name="'contacts[' + ${contactStat.index} + '].firstName'"
                                           th:id="'contactFirstName' + ${contactStat.index}"
                                           th:value="${contact.firstName}"
                                           class="form-control" />
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label th:for="'contactLastName' + ${contactStat.index}" class="form-label" th:text="#{label.lastName}">Priezvisko</label>
                                    <input type="text" 
                                           th:name="'contacts[' + ${contactStat.index} + '].lastName'"
                                           th:id="'contactLastName' + ${contactStat.index}"
                                           th:value="${contact.lastName}"
                                           class="form-control" />
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label th:for="'contactPosition' + ${contactStat.index}" class="form-label" th:text="#{label.position}">Pozícia</label>
                                <input type="text" 
                                       th:name="'contacts[' + ${contactStat.index} + '].position'"
                                       th:id="'contactPosition' + ${contactStat.index}"
                                       th:value="${contact.position}"
                                       class="form-control" />
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label th:for="'contactEmail' + ${contactStat.index}" class="form-label" th:text="#{label.email}">Email</label>
                                    <input type="email" 
                                           th:name="'contacts[' + ${contactStat.index} + '].email'"
                                           th:id="'contactEmail' + ${contactStat.index}"
                                           th:value="${contact.email}"
                                           class="form-control" />
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label th:for="'contactPhone1' + ${contactStat.index}" class="form-label" th:text="#{label.phone1}">Telefón 1</label>
                                    <input type="tel" 
                                           th:name="'contacts[' + ${contactStat.index} + '].phone1'"
                                           th:id="'contactPhone1' + ${contactStat.index}"
                                           th:value="${contact.phone1}"
                                           class="form-control" />
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label th:for="'contactPhone2' + ${contactStat.index}" class="form-label" th:text="#{label.phone2}">Telefón 2</label>
                                    <input type="tel" 
                                           th:name="'contacts[' + ${contactStat.index} + '].phone2'"
                                           th:id="'contactPhone2' + ${contactStat.index}"
                                           th:value="${contact.phone2}"
                                           class="form-control" />
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label th:for="'contactOtherContact' + ${contactStat.index}" class="form-label" th:text="#{label.otherContact}">Iný kontakt</label>
                                    <input type="text" 
                                           th:name="'contacts[' + ${contactStat.index} + '].otherContact'"
                                           th:id="'contactOtherContact' + ${contactStat.index}"
                                           th:value="${contact.otherContact}"
                                           class="form-control" />
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="form-check">
                                        <input type="checkbox" 
                                               th:name="'contacts[' + ${contactStat.index} + '].newsletterSubscribed'"
                                               th:id="'contactNewsletter' + ${contactStat.index}"
                                               th:checked="${contact.newsletterSubscribed}"
                                               class="form-check-input" 
                                               value="true" />
                                        <input type="hidden" 
                                               th:name="'contacts[' + ${contactStat.index} + '].newsletterSubscribed'" 
                                               value="false" />
                                        <label th:for="'contactNewsletter' + ${contactStat.index}" class="form-check-label" th:text="#{label.newsletterSubscribed}">
                                            Odber newslettera
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="form-check">
                                        <input type="checkbox" 
                                               th:name="'contacts[' + ${contactStat.index} + '].mainContact'"
                                               th:id="'contactMain' + ${contactStat.index}"
                                               th:checked="${contact.mainContact}"
                                               class="form-check-input" 
                                               value="true" />
                                        <input type="hidden" 
                                               th:name="'contacts[' + ${contactStat.index} + '].mainContact'" 
                                               value="false" />
                                        <label th:for="'contactMain' + ${contactStat.index}" class="form-check-label" th:text="#{label.mainContact}">
                                            Hlavný kontakt
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label th:for="'contactNote' + ${contactStat.index}" class="form-label" th:text="#{label.note}">Poznámka</label>
                                <textarea th:name="'contacts[' + ${contactStat.index} + '].note'"
                                          th:id="'contactNote' + ${contactStat.index}"
                                          th:text="${contact.note}"
                                          class="form-control" 
                                          rows="2"></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Add new contact button -->
                    <div class="text-center mt-3">
                        <button type="button" class="btn btn-outline-primary" onclick="addNewContact()">
                            <i class="fas fa-plus"></i> <span th:text="#{customer.contact.add}">Pridať kontakt</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
