<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      th:lang="${#locale.language}"
      layout:decorate="~{layout}">
<head>
    <meta charset="UTF-8"/>
    <title th:text="${quote.id != null} ? #{quote.edit.title} : #{quote.create.title}">Quote Form</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
</head>

<body>
<section layout:fragment="content">
    <div class="container">
        <form th:action="@{/quotes/save}" th:object="${quote}" method="post">
            <h2 th:text="${quote.id != null} ? #{quote.edit.title} : #{quote.create.title}">Quote Form</h2>

            <div class="mb-3">
                <label for="number" class="form-label" th:text="#{quote.number}">Number</label>
                <input type="text" th:field="*{number}" class="form-control" id="number" />
                <div class="text-danger" th:if="${#fields.hasErrors('number')}" th:errors="*{number}"></div>
            </div>

            <div class="mb-3">
                <label for="issueDate" class="form-label" th:text="#{quote.issueDate}">Issue Date</label>
                <input type="text" th:field="*{issueDate}" class="form-control" id="issueDate" />
                <div class="text-danger" th:if="${#fields.hasErrors('issueDate')}" th:errors="*{issueDate}"></div>
            </div>

            <div class="mb-3">
                <label for="validUntilDate" class="form-label" th:text="#{quote.validUntilDate}">Valid Until Date</label>
                <input type="text" th:field="*{validUntilDate}" class="form-control" id="validUntilDate" />
                <div class="text-danger" th:if="${#fields.hasErrors('validUntilDate')}" th:errors="*{validUntilDate}"></div>
            </div>

            <!-- Customer field -->
            <div class="mb-3">
                <label for="customer" class="form-label" th:text="#{quote.customer}">Customer</label>
                <select th:field="*{customer}" class="form-select" id="customer" required>
                    <option value="" th:text="#{quote.customer.select}">Choose customer</option>
                    <option th:each="customer : ${customers}" th:value="${customer.id}"
                            th:text="${customer.name}" th:selected="${currentCustomer != null && currentCustomer.id == customer.id}"></option>
                </select>
                <div class="text-danger" th:if="${#fields.hasErrors('customer')}" th:errors="*{customer}"></div>
            </div>

            <!-- HistoryOverview fields -->
            <div class="mb-3">
                <label for="user" class="form-label" th:text="#{quote.user}">Responsible User</label>
                <select th:field="*{user}" class="form-select" id="user">
                    <option value="" th:text="#{quote.user.select}">Choose user</option>
                    <option th:each="user : ${users}" th:value="${user.id}"
                            th:text="${user.firstName} + ' ' + ${user.lastName}"></option>
                </select>
                <div class="text-danger" th:if="${#fields.hasErrors('user')}" th:errors="*{user}"></div>
            </div>

            <div class="mb-3">
                <label for="status" class="form-label" th:text="#{quote.status}">Status</label>
                <select th:field="*{status}" class="form-select" id="status">
                    <option value="" th:text="#{quote.status.select}">Choose status</option>
                    <option th:each="status : ${statuses}" th:value="${status}" th:text="#{${'status.' + status.name().toLowerCase()}}"></option>
                </select>
                <div class="text-danger" th:if="${#fields.hasErrors('status')}" th:errors="*{status}"></div>
            </div>

            <div class="mb-3">
                <label for="total" class="form-label" th:text="#{quote.total}">Total</label>
                <input type="number" step="0.01" th:field="*{total}" class="form-control" id="total" />
                <div class="text-danger" th:if="${#fields.hasErrors('total')}" th:errors="*{total}"></div>
            </div>

            <div class="mb-3">
                <label for="currency" class="form-label" th:text="#{quote.currency}">Currency</label>
                <select th:field="*{currency}" class="form-select" id="currency">
                    <option value="" th:text="#{quote.currency.select}">Choose currency</option>
                    <option th:each="currency : ${currencies}" th:value="${currency}" th:text="${currency}"></option>
                </select>
                <div class="text-danger" th:if="${#fields.hasErrors('currency')}" th:errors="*{currency}"></div>
            </div>

            <div class="mb-3">
                <label for="note" class="form-label" th:text="#{quote.note}">Note</label>
                <textarea th:field="*{note}" class="form-control" id="note" rows="3"></textarea>
                <div class="text-danger" th:if="${#fields.hasErrors('note')}" th:errors="*{note}"></div>
            </div>

            <button type="submit" class="btn btn-success" th:text="#{action.save}">Save</button>
            <a th:href="@{/quotes}" class="btn btn-secondary me-2" th:text="#{action.cancel}">Cancel</a>
            <a th:href="@{/customers}" id="backToCustomerListBtn" class="btn btn-outline-secondary" th:text="#{button.backToCustomerList}">Back to Customer List</a>
        </form>
    </div>
</section>

<th:block layout:fragment="scripts">
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Initialize date pickers
            flatpickr("#issueDate", {
                dateFormat: "Y-m-d"
            });

            flatpickr("#validUntilDate", {
                dateFormat: "Y-m-d"
            });

            // Set up back button with saved filters
            setupBackButton();
        });

        // Function to get cookie value
        function getCookie(name) {
            const nameEQ = name + '=';
            const ca = document.cookie.split(';');
            for (let i = 0; i < ca.length; i++) {
                let c = ca[i];
                while (c.charAt(0) === ' ') c = c.substring(1, c.length);
                if (c.indexOf(nameEQ) === 0) return decodeURIComponent(c.substring(nameEQ.length, c.length));
            }
            return null;
        }

        // Function to build customer list URL with saved filters
        function buildCustomerListUrl() {
            const filters = {
                nameFilter: getCookie('customer_nameFilter') || '',
                icoFilter: getCookie('customer_icoFilter') || '',
                cityFilter: getCookie('customer_cityFilter') || '',
                countryFilter: getCookie('customer_countryFilter') || '',
                typeFilter: getCookie('customer_typeFilter') || '',
                subtypeFilter: getCookie('customer_subtypeFilter') || '',
                responsiblePersonFilter: getCookie('customer_responsiblePersonFilter') || '',
                page: getCookie('customer_page') || '0',
                size: getCookie('customer_size') || '20',
                sortBy: getCookie('customer_sortBy') || 'name',
                sortDir: getCookie('customer_sortDir') || 'asc'
            };

            // Handle tagFilters
            const savedTagFilters = getCookie('customer_tagFilters');
            let tagFilters = [];
            if (savedTagFilters) {
                try {
                    tagFilters = JSON.parse(savedTagFilters);
                } catch (e) {
                    console.error('Error parsing saved tag filters:', e);
                }
            }

            // Build URL with non-empty filters
            const url = new URL('/customers', window.location.origin);
            Object.keys(filters).forEach(key => {
                if (filters[key] && filters[key] !== '') {
                    url.searchParams.set(key, filters[key]);
                }
            });

            // Add tag filters
            if (tagFilters.length > 0) {
                tagFilters.forEach(tagId => {
                    url.searchParams.append('tagFilters', tagId);
                });
            }

            return url.toString();
        }

        // Set up back button
        function setupBackButton() {
            const backBtn = document.getElementById('backToCustomerListBtn');
            if (backBtn) {
                try {
                    const urlWithFilters = buildCustomerListUrl();
                    backBtn.href = urlWithFilters;
                    console.log('Back button URL set to:', urlWithFilters);
                } catch (error) {
                    console.error('Error setting up back button:', error);
                    // Fallback to basic customer list URL
                    backBtn.href = '/customers';
                }
            }
        }
    </script>
</th:block>
</body>
</html>
