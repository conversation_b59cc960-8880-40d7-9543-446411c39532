<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      th:lang="${#locale.language}"
      layout:decorate="~{layout}">
<head>
    <meta charset="UTF-8"/>
    <title th:text="#{quote.customer.list.title} + ' - ' + ${customer.name}">Customer Quotes</title>
    <style>
        .table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .table {
            margin-bottom: 0;
            font-size: 0.9rem;
        }
        .table th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
            padding: 12px 8px;
            white-space: nowrap;
        }
        .table td {
            padding: 10px 8px;
            vertical-align: middle;
            border-bottom: 1px solid #f0f0f0;
        }
        .table tbody tr:hover {
            background-color: #f8f9fa;
        }
        .action-buttons {
            display: flex;
            gap: 4px;
            flex-wrap: nowrap;
        }
        .action-buttons .btn {
            padding: 4px 8px;
            font-size: 0.75rem;
            min-width: auto;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        .status-draft { background-color: #e3f2fd; color: #1976d2; }
        .status-sent { background-color: #f3e5f5; color: #7b1fa2; }
        .status-accepted { background-color: #e8f5e8; color: #388e3c; }
        .status-rejected { background-color: #ffebee; color: #d32f2f; }
        .status-expired { background-color: #fff3e0; color: #f57c00; }
        .status-cancelled { background-color: #fafafa; color: #616161; }
    </style>
</head>

<body>
<section layout:fragment="content">
    <div class="container-fluid">
        <h2 th:text="#{quote.customer.list.title} + ' - ' + ${customer.name}">Quotes for Customer</h2>
        
        <div class="mb-3">
            <a th:href="@{'/quotes/new/customer/' + ${customer.id}}" class="btn btn-primary me-2" th:text="#{quote.add}">Add Quote</a>
            <a th:href="@{/customers}" class="btn btn-outline-secondary me-2" th:text="#{button.backToCustomerList}">Back to Customer List</a>
            <a th:href="@{'/customers/edit/' + ${customer.id}}" class="btn btn-outline-primary" th:text="#{button.backToCustomer}">Back to Customer</a>
        </div>

        <div class="table-container">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th th:text="#{quote.number}">Number</th>
                            <th th:text="#{quote.status}">Status</th>
                            <th th:text="#{quote.issueDate}">Issue Date</th>
                            <th th:text="#{quote.validUntilDate}">Valid Until</th>
                            <th th:text="#{quote.total}">Total</th>
                            <th th:text="#{label.actions}">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr th:each="quote : ${quotes}" th:if="${quotes != null && !quotes.isEmpty()}">
                            <td>
                                <strong th:text="${quote.number ?: 'Q-' + quote.id}">Number</strong>
                            </td>
                            <td>
                                <span th:if="${quote.status != null}"
                                      th:text="#{${'status.' + quote.status.name().toLowerCase()}}"
                                      th:class="'status-badge status-' + ${quote.status.name().toLowerCase()}">Status</span>
                                <span th:unless="${quote.status != null}" class="text-muted">-</span>
                            </td>
                            <td th:text="${quote.issueDate != null ? #temporals.format(quote.issueDate, 'dd.MM.yyyy') : '-'}">Issue Date</td>
                            <td th:text="${quote.validUntilDate != null ? #temporals.format(quote.validUntilDate, 'dd.MM.yyyy') : '-'}">Valid Until</td>
                            <td>
                                <div th:if="${quote.total != null}">
                                    <strong th:text="${#numbers.formatDecimal(quote.total, 1, 2)}">0.00</strong>
                                    <span th:text="${quote.currency ?: 'EUR'}" class="text-muted ms-1">EUR</span>
                                </div>
                                <span th:unless="${quote.total != null}" class="text-muted">-</span>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a th:href="@{'/quotes/edit/' + ${quote.id}}"
                                       class="btn btn-outline-secondary btn-sm"
                                       th:title="#{button.edit}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a th:href="@{'/quotes/preview/' + ${quote.id}}"
                                       class="btn btn-outline-success btn-sm"
                                       th:title="#{quote.preview}"
                                       target="_blank">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <form th:action="@{'/quotes/delete/' + ${quote.id}}" method="post" style="display:inline;">
                                        <input type="hidden" name="_method" value="delete"/>
                                        <button type="submit" class="btn btn-outline-danger btn-sm"
                                                th:onclick="'return confirm(\'' + #{quote.delete.confirm} + '\');'"
                                                th:title="#{button.delete}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        <tr th:if="${quotes == null || quotes.isEmpty()}">
                            <td colspan="6" class="text-center text-muted py-4" th:text="#{quote.customer.noRecords}">No quotes found for this customer</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>
</body>
</html>
