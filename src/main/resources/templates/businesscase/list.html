<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      th:lang="${#locale.language}"
      layout:decorate="~{layout}">
<head>
    <title th:text="#{businesscase.listTitle}">BusinessCase List</title>
</head>
<body>
<section layout:fragment="content">
    <h2 th:text="#{businesscase.listTitle}">BusinessCase List</h2>
    <a th:href="@{/businesscases/create}" class="btn btn-primary mb-3" th:text="#{businesscase.add}">Add BusinessCase</a>

    <table class="table table-bordered table-striped">
        <thead>
        <tr>
            <th th:text="#{businesscase.id}">ID</th>
            <th th:text="#{businesscase.closureDate}">Closure Date</th>
            <th th:text="#{businesscase.contactSource}">Contact Source</th>
            <th th:text="#{businesscase.createdAt}">Created At</th>
            <th th:text="#{businesscase.dealSubtype}">Deal Subtype</th>
            <th th:text="#{businesscase.dealType}">Deal Type</th>
            <th th:text="#{businesscase.description}">Description</th>
            <th th:text="#{businesscase.expectedClosureDate}">Expected Closure Date</th>
            <th th:text="#{businesscase.failureNote}">Failure Note</th>
            <th th:text="#{businesscase.failureReason}">Failure Reason</th>
            <th th:text="#{businesscase.name}">Name</th>
            <th th:text="#{businesscase.number}">Number</th>
            <th th:text="#{businesscase.responsible}">Responsible</th>
            <th th:text="#{businesscase.status}">Status</th>
            <th th:text="#{businesscase.totalPrice}">Total Price</th>
            <th th:text="#{businesscase.customerId}">Customer ID</th>
            <th th:text="#{actions}">Actions</th>
        </tr>
        </thead>
        <tbody>
        <tr th:each="businesscase : ${businesscases}">
            <td th:text="${businesscase.id}">ID</td>
            <td th:text="${businesscase.closureDate != null ? #temporals.format(businesscase.closureDate, 'yyyy-MM-dd') : ''}">Closure Date</td>
            <td th:text="${businesscase.contactSource}">Contact Source</td>
            <td th:text="${businesscase.createdAt != null ? #temporals.format(businesscase.createdAt, 'yyyy-MM-dd HH:mm') : ''}">Created At</td>
            <td th:text="${businesscase.dealSubtype}">Deal Subtype</td>
            <td th:text="${businesscase.dealType}">Deal Type</td>
            <td th:text="${businesscase.description}">Description</td>
            <td th:text="${businesscase.expectedClosureDate != null ? #temporals.format(businesscase.expectedClosureDate, 'yyyy-MM-dd') : ''}">Expected Closure Date</td>
            <td th:text="${businesscase.failureNote}">Failure Note</td>
            <td th:text="${businesscase.failureReason}">Failure Reason</td>
            <td th:text="${businesscase.name}">Name</td>
            <td th:text="${businesscase.number}">Number</td>
            <td th:text="${businesscase.responsible}">Responsible</td>
            <td th:text="${businesscase.status}">Status</td>
            <td th:text="${businesscase.totalPrice}">Total Price</td>
            <td th:text="${businesscase.customerId}">Customer ID</td>
            <td>
                <a th:href="@{/businesscases/edit/{id}(id=${businesscase.id})}" class="btn btn-sm btn-secondary" th:text="#{button.edit}">Edit</a>
                <a th:href="@{/businesscases/delete/{id}(id=${businesscase.id})}" class="btn btn-sm btn-danger"
                   th:onclick="'return confirm(\'' + #{confirm.deleteBusinesscase} + '\');'" th:text="#{button.delete}">Delete</a>
            </td>
        </tr>
        </tbody>
    </table>
</section>
</body>
</html>
