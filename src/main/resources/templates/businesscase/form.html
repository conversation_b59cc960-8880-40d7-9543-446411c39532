<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      th:lang="${#locale.language}"
      layout:decorate="~{layout}">
<head>
    <meta charset="UTF-8">
    <title th:text="${businesscase.id != null} ? #{businesscase.editTitle} : #{businesscase.createTitle}">BusinessCase Form</title>
</head>

<body>
<section layout:fragment="content">
    <div class="container">
        <form th:action="@{/businesscases/save}" th:object="${businesscase}" method="post">
            <h2 th:text="${#fields.hasErrors('*')} ? #{form.errorsFix} : (businesscase.id != null ? #{businesscase.editTitle} : #{businesscase.createTitle})">BusinessCase Form</h2>

            <div class="mb-3">
                <label for="id" class="form-label" th:text="#{businesscase.id}">ID</label>
                <input type="text" th:field="*{id}" class="form-control" id="id"/>
                <div class="text-danger" th:if="${#fields.hasErrors('id')}" th:errors="*{id}"></div>
            </div>

            <div class="mb-3">
                <label for="closureDate" class="form-label" th:text="#{businesscase.closureDate}">Closure Date</label>
                <input type="date" th:field="*{closureDate}" class="form-control" id="closureDate"/>
                <div class="text-danger" th:if="${#fields.hasErrors('closureDate')}" th:errors="*{closureDate}"></div>
            </div>

            <div class="mb-3">
                <label for="contactSource" class="form-label" th:text="#{businesscase.contactSource}">Contact Source</label>
                <input type="text" th:field="*{contactSource}" class="form-control" id="contactSource"/>
                <div class="text-danger" th:if="${#fields.hasErrors('contactSource')}" th:errors="*{contactSource}"></div>
            </div>

            <div class="mb-3">
                <label for="createdAt" class="form-label" th:text="#{businesscase.createdAt}">Created At</label>
                <input type="text" th:field="*{createdAt}" class="form-control" id="createdAt"/>
                <div class="text-danger" th:if="${#fields.hasErrors('createdAt')}" th:errors="*{createdAt}"></div>
            </div>

            <div class="mb-3">
                <label for="dealSubtype" class="form-label" th:text="#{businesscase.dealSubtype}">Deal Subtype</label>
                <input type="text" th:field="*{dealSubtype}" class="form-control" id="dealSubtype"/>
                <div class="text-danger" th:if="${#fields.hasErrors('dealSubtype')}" th:errors="*{dealSubtype}"></div>
            </div>

            <div class="mb-3">
                <label for="dealType" class="form-label" th:text="#{businesscase.dealType}">Deal Type</label>
                <input type="text" th:field="*{dealType}" class="form-control" id="dealType"/>
                <div class="text-danger" th:if="${#fields.hasErrors('dealType')}" th:errors="*{dealType}"></div>
            </div>

            <div class="mb-3">
                <label for="description" class="form-label" th:text="#{businesscase.description}">Description</label>
                <input type="text" th:field="*{description}" class="form-control" id="description"/>
                <div class="text-danger" th:if="${#fields.hasErrors('description')}" th:errors="*{description}"></div>
            </div>

            <div class="mb-3">
                <label for="expectedClosureDate" class="form-label" th:text="#{businesscase.expectedClosureDate}">Expected Closure Date</label>
                <input type="date" th:field="*{expectedClosureDate}" class="form-control" id="expectedClosureDate"/>
                <div class="text-danger" th:if="${#fields.hasErrors('expectedClosureDate')}" th:errors="*{expectedClosureDate}"></div>
            </div>

            <div class="mb-3">
                <label for="failureNote" class="form-label" th:text="#{businesscase.failureNote}">Failure Note</label>
                <input type="text" th:field="*{failureNote}" class="form-control" id="failureNote"/>
                <div class="text-danger" th:if="${#fields.hasErrors('failureNote')}" th:errors="*{failureNote}"></div>
            </div>

            <div class="mb-3">
                <label for="failureReason" class="form-label" th:text="#{businesscase.failureReason}">Failure Reason</label>
                <input type="text" th:field="*{failureReason}" class="form-control" id="failureReason"/>
                <div class="text-danger" th:if="${#fields.hasErrors('failureReason')}" th:errors="*{failureReason}"></div>
            </div>

            <div class="mb-3">
                <label for="name" class="form-label" th:text="#{businesscase.name}">Name</label>
                <input type="text" th:field="*{name}" class="form-control" id="name"/>
                <div class="text-danger" th:if="${#fields.hasErrors('name')}" th:errors="*{name}"></div>
            </div>

            <div class="mb-3">
                <label for="number" class="form-label" th:text="#{businesscase.number}">Number</label>
                <input type="text" th:field="*{number}" class="form-control" id="number"/>
                <div class="text-danger" th:if="${#fields.hasErrors('number')}" th:errors="*{number}"></div>
            </div>

            <div class="mb-3">
                <label for="responsible" class="form-label" th:text="#{businesscase.responsible}">Responsible</label>
                <input type="text" th:field="*{responsible}" class="form-control" id="responsible"/>
                <div class="text-danger" th:if="${#fields.hasErrors('responsible')}" th:errors="*{responsible}"></div>
            </div>

            <div class="mb-3">
                <label for="status" class="form-label" th:text="#{businesscase.status}">Status</label>
                <input type="text" th:field="*{status}" class="form-control" id="status"/>
                <div class="text-danger" th:if="${#fields.hasErrors('status')}" th:errors="*{status}"></div>
            </div>

            <div class="mb-3">
                <label for="totalPrice" class="form-label" th:text="#{businesscase.totalPrice}">Total Price</label>
                <input type="text" th:field="*{totalPrice}" class="form-control" id="totalPrice"/>
                <div class="text-danger" th:if="${#fields.hasErrors('totalPrice')}" th:errors="*{totalPrice}"></div>
            </div>

            <div class="mb-3">
                <label for="customerId" class="form-label" th:text="#{businesscase.customerId}">Customer ID</label>
                <input type="text" th:field="*{customerId}" class="form-control" id="customerId"/>
                <div class="text-danger" th:if="${#fields.hasErrors('customerId')}" th:errors="*{customerId}"></div>
            </div>

            <button type="submit" class="btn btn-success" th:text="#{button.save}">Save</button>
            <a th:href="@{/businesscases}" class="btn btn-secondary" th:text="#{button.cancel}">Cancel</a>
        </form>
    </div>
</section>
</body>
</html>
