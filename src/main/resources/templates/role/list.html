<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:lang="${#locale.language}"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout}">
<head>
    <title th:text="#{role.list.title}">Role List</title>
</head>

<section layout:fragment="content">
    <h2 th:text="#{role.list.title}">Role List</h2>
    <a th:href="@{/roles/create}" class="btn btn-primary mb-3" th:text="#{role.add}">Add Role</a>

    <table class="table table-bordered table-striped">
        <thead>
        <tr>
            <th th:text="#{role.id}">id</th>
            <th th:text="#{role.name}">name</th>
            <th th:text="#{label.actions}">Actions</th>
        </tr>
        </thead>
        <tbody>
        <tr th:each="role : ${roles}">
            <td th:text="${role.id}">id</td>
            <td th:text="${role.name}">name</td>
            <td>
                <a th:href="@{/roles/edit/{id}(id=${role.id})}" class="btn btn-sm btn-secondary" th:text="#{action.edit}">Edit</a>
                <a th:href="@{/roles/delete/{id}(id=${role.id})}" class="btn btn-sm btn-danger"
                   th:attr="onclick='return confirm(' + '\'' + #{role.delete.confirm} + '\'' + ');'"
                   th:text="#{action.delete}">Delete</a>
            </td>
        </tr>
        </tbody>
    </table>
</section>
</html>
