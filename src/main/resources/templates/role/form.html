<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      th:lang="${#locale.language}"
      layout:decorate="~{layout}">
<head>
    <meta charset="UTF-8">
    <title th:text="${role.id != null} ? #{role.edit.title} : #{role.create.title}">Role Form</title>
</head>

<body>
<section layout:fragment="content">
    <div class="container">
        <form th:action="@{/roles/save}" th:object="${role}" method="post">

            <h2 th:text="${#fields.hasErrors('*')} ? #{form.errors.fix} : (role.id != null ? #{role.edit.title} : #{role.create.title})">Role Form</h2>

            <div class="mb-3">
                <label for="id" class="form-label" th:text="#{role.id}">ID</label>
                <input type="text" th:field="*{id}" class="form-control" id="id" />
                <div class="text-danger" th:if="${#fields.hasErrors('id')}" th:errors="*{id}"></div>
            </div>

            <div class="mb-3">
                <label for="name" class="form-label" th:text="#{role.name}">Name</label>
                <input type="text" th:field="*{name}" class="form-control" id="name" />
                <div class="text-danger" th:if="${#fields.hasErrors('name')}" th:errors="*{name}"></div>
            </div>

            <button type="submit" class="btn btn-success" th:text="#{action.save}">Save</button>
            <a th:href="@{/roles}" class="btn btn-secondary" th:text="#{action.cancel}">Cancel</a>
        </form>
    </div>
</section>
</body>
</html>
