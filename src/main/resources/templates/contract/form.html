<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:lang="${#locale.language}"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout}">
<head>
    <meta charset="UTF-8" />
    <title th:text="${contract.id != null} ? #{contract.editTitle} : #{contract.createTitle}">Contract Form</title>
</head>

<body>
<section layout:fragment="content">
    <div class="container">
        <form th:action="@{/contracts/save}" th:object="${contract}" method="post">

            <div th:if="${#fields.hasErrors('*')}" class="alert alert-danger" th:text="#{form.errorsFix}">
                Please fix errors in form
            </div>

            <h2 th:text="${contract.id != null} ? #{contract.editTitle} : #{contract.createTitle}">Contract Form</h2>

            <input type="hidden" th:field="*{id}" />

            <div class="mb-3">
                <label for="name" class="form-label" th:text="#{contract.name}">Name</label>
                <input type="text" th:field="*{name}" class="form-control" id="name" />
                <div class="text-danger" th:if="${#fields.hasErrors('name')}" th:errors="*{name}"></div>
            </div>

            <div class="mb-3">
                <label for="number" class="form-label" th:text="#{contract.number}">Number</label>
                <input type="text" th:field="*{number}" class="form-control" id="number" />
                <div class="text-danger" th:if="${#fields.hasErrors('number')}" th:errors="*{number}"></div>
            </div>

            <div class="mb-3">
                <label for="agreedPrice" class="form-label" th:text="#{contract.agreedPrice}">Agreed Price</label>
                <input type="number" step="0.01" min="0" th:field="*{agreedPrice}" class="form-control" id="agreedPrice" />
                <div class="text-danger" th:if="${#fields.hasErrors('agreedPrice')}" th:errors="*{agreedPrice}"></div>
            </div>

            <div class="mb-3">
                <label for="businessCase" class="form-label" th:text="#{contract.businessCase}">Business Case</label>
                <input type="text" th:field="*{businessCase}" class="form-control" id="businessCase" />
                <div class="text-danger" th:if="${#fields.hasErrors('businessCase')}" th:errors="*{businessCase}"></div>
            </div>

            <div class="mb-3">
                <label for="completionDate" class="form-label" th:text="#{contract.completionDate}">Completion Date</label>
                <input type="date" th:field="*{completionDate}" class="form-control" id="completionDate" />
                <div class="text-danger" th:if="${#fields.hasErrors('completionDate')}" th:errors="*{completionDate}"></div>
            </div>

            <div class="mb-3">
                <label for="completionDeadline" class="form-label" th:text="#{contract.completionDeadline}">Completion Deadline</label>
                <input type="date" th:field="*{completionDeadline}" class="form-control" id="completionDeadline" />
                <div class="text-danger" th:if="${#fields.hasErrors('completionDeadline')}" th:errors="*{completionDeadline}"></div>
            </div>

            <div class="mb-3">
                <label for="createdAt" class="form-label" th:text="#{contract.createdAt}">Created At</label>
                <input type="datetime-local" th:field="*{createdAt}" class="form-control" id="createdAt" />
                <div class="text-danger" th:if="${#fields.hasErrors('createdAt')}" th:errors="*{createdAt}"></div>
            </div>

            <div class="mb-3">
                <label for="deliveryDate" class="form-label" th:text="#{contract.deliveryDate}">Delivery Date</label>
                <input type="date" th:field="*{deliveryDate}" class="form-control" id="deliveryDate" />
                <div class="text-danger" th:if="${#fields.hasErrors('deliveryDate')}" th:errors="*{deliveryDate}"></div>
            </div>

            <div class="mb-3">
                <label for="descriptionAndRequirements" class="form-label" th:text="#{contract.descriptionAndRequirements}">Description and Requirements</label>
                <textarea th:field="*{descriptionAndRequirements}" class="form-control" id="descriptionAndRequirements"></textarea>
                <div class="text-danger" th:if="${#fields.hasErrors('descriptionAndRequirements')}" th:errors="*{descriptionAndRequirements}"></div>
            </div>

            <div class="mb-3">
                <label for="estimatedDuration" class="form-label" th:text="#{contract.estimatedDuration}">Estimated Duration</label>
                <input type="number" step="1" min="0" th:field="*{estimatedDuration}" class="form-control" id="estimatedDuration" />
                <div class="text-danger" th:if="${#fields.hasErrors('estimatedDuration')}" th:errors="*{estimatedDuration}"></div>
            </div>

            <div class="mb-3">
                <label for="invoiced" class="form-label" th:text="#{contract.invoiced}">Invoiced</label>
                <input type="checkbox" th:field="*{invoiced}" class="form-check-input" id="invoiced" />
                <div class="text-danger" th:if="${#fields.hasErrors('invoiced')}" th:errors="*{invoiced}"></div>
            </div>

            <div class="mb-3">
                <label for="overdueDays" class="form-label" th:text="#{contract.overdueDays}">Overdue Days</label>
                <input type="number" step="1" min="0" th:field="*{overdueDays}" class="form-control" id="overdueDays" />
                <div class="text-danger" th:if="${#fields.hasErrors('overdueDays')}" th:errors="*{overdueDays}"></div>
            </div>

            <div class="mb-3">
                <label for="remainingToInvoice" class="form-label" th:text="#{contract.remainingToInvoice}">Remaining To Invoice</label>
                <input type="number" step="0.01" min="0" th:field="*{remainingToInvoice}" class="form-control" id="remainingToInvoice" />
                <div class="text-danger" th:if="${#fields.hasErrors('remainingToInvoice')}" th:errors="*{remainingToInvoice}"></div>
            </div>

            <div class="mb-3">
                <label for="responsible" class="form-label" th:text="#{contract.responsible}">Responsible</label>
                <input type="text" th:field="*{responsible}" class="form-control" id="responsible" />
                <div class="text-danger" th:if="${#fields.hasErrors('responsible')}" th:errors="*{responsible}"></div>
            </div>

            <div class="mb-3">
                <label for="status" class="form-label" th:text="#{contract.status}">Status</label>
                <input type="text" th:field="*{status}" class="form-control" id="status" />
                <div class="text-danger" th:if="${#fields.hasErrors('status')}" th:errors="*{status}"></div>
            </div>

            <div class="mb-3">
                <label for="subtype" class="form-label" th:text="#{contract.subtype}">Subtype</label>
                <input type="text" th:field="*{subtype}" class="form-control" id="subtype" />
                <div class="text-danger" th:if="${#fields.hasErrors('subtype')}" th:errors="*{subtype}"></div>
            </div>

            <div class="mb-3">
                <label for="tradeMargin" class="form-label" th:text="#{contract.tradeMargin}">Trade Margin</label>
                <input type="number" step="0.01" th:field="*{tradeMargin}" class="form-control" id="tradeMargin" />
                <div class="text-danger" th:if="${#fields.hasErrors('tradeMargin')}" th:errors="*{tradeMargin}"></div>
            </div>

            <div class="mb-3">
                <label for="tradeMarginPercent" class="form-label" th:text="#{contract.tradeMarginPercent}">Trade Margin (%)</label>
                <input type="number" step="0.01" th:field="*{tradeMarginPercent}" class="form-control" id="tradeMarginPercent" />
                <div class="text-danger" th:if="${#fields.hasErrors('tradeMarginPercent')}" th:errors="*{tradeMarginPercent}"></div>
            </div>

            <div class="mb-3">
                <label for="type" class="form-label" th:text="#{contract.type}">Type</label>
                <input type="text" th:field="*{type}" class="form-control" id="type" />
                <div class="text-danger" th:if="${#fields.hasErrors('type')}" th:errors="*{type}"></div>
            </div>

            <div class="mb-3">
                <label for="uninvoicedPayments" class="form-label" th:text="#{contract.uninvoicedPayments}">Uninvoiced Payments</label>
                <input type="number" step="0.01" th:field="*{uninvoicedPayments}" class="form-control" id="uninvoicedPayments" />
                <div class="text-danger" th:if="${#fields.hasErrors('uninvoicedPayments')}" th:errors="*{uninvoicedPayments}"></div>
            </div>

            <div class="mb-3">
                <label for="customerId" class="form-label" th:text="#{contract.customerId}">Customer ID</label>
                <input type="text" th:field="*{customerId}" class="form-control" id="customerId" />
                <div class="text-danger" th:if="${#fields.hasErrors('customerId')}" th:errors="*{customerId}"></div>
            </div>

            <button type="submit" class="btn btn-primary" th:text="#{button.save}">Save</button>
            <a th:href="@{/contracts}" class="btn btn-secondary" th:text="#{button.cancel}">Cancel</a>

        </form>
    </div>
</section>
</body>
</html>
