<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:lang="${#locale.language}"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout}">
<head>
    <title th:text="#{contract.list.title}">Contract List</title>
</head>

<section layout:fragment="content">
    <h2 th:text="#{contract.list.title}">Contract List</h2>
    <a th:href="@{/contracts/create}" class="btn btn-primary mb-3" th:text="#{button.addContract}">Add Contract</a>

    <table class="table table-bordered table-striped">
        <thead>
        <tr>
            <th th:text="#{contract.id}">id</th>
            <th th:text="#{contract.actualDuration}">actualDuration</th>
            <th th:text="#{contract.agreedPrice}">agreedPrice</th>
            <th th:text="#{contract.businessCase}">businessCase</th>
            <th th:text="#{contract.completionDate}">completionDate</th>
            <th th:text="#{contract.completionDeadline}">completionDeadline</th>
            <th th:text="#{contract.createdAt}">createdAt</th>
            <th th:text="#{contract.deliveryDate}">deliveryDate</th>
            <th th:text="#{contract.descriptionAndRequirements}">descriptionAndRequirements</th>
            <th th:text="#{contract.estimatedDuration}">estimatedDuration</th>
            <th th:text="#{contract.invoiced}">invoiced</th>
            <th th:text="#{contract.name}">name</th>
            <th th:text="#{contract.number}">number</th>
            <th th:text="#{contract.overdueDays}">overdueDays</th>
            <th th:text="#{contract.remainingToInvoice}">remainingToInvoice</th>
            <th th:text="#{contract.responsible}">responsible</th>
            <th th:text="#{contract.status}">status</th>
            <th th:text="#{contract.subtype}">subtype</th>
            <th th:text="#{contract.tradeMargin}">tradeMargin</th>
            <th th:text="#{contract.tradeMarginPercent}">tradeMarginPercent</th>
            <th th:text="#{contract.type}">type</th>
            <th th:text="#{contract.uninvoicedPayments}">uninvoicedPayments</th>
            <th th:text="#{contract.customerId}">customerId</th>
            <th th:text="#{contract.actions}">Actions</th>
        </tr>
        </thead>
        <tbody>
        <tr th:each="contract : ${contracts}">
            <td th:text="${contract.id}">id</td>
            <td th:text="${contract.actualDuration}">actualDuration</td>
            <td th:text="${#numbers.formatDecimal(contract.agreedPrice, 1, 'COMMA', 2, 'POINT')}">agreedPrice</td>
            <td th:text="${contract.businessCase}">businessCase</td>
            <td th:text="${contract.completionDate != null ? #temporals.format(contract.completionDate, 'yyyy-MM-dd') : ''}">completionDate</td>
            <td th:text="${contract.completionDeadline != null ? #temporals.format(contract.completionDeadline, 'yyyy-MM-dd') : ''}">completionDeadline</td>
            <td th:text="${contract.createdAt != null ? #temporals.format(contract.createdAt, 'yyyy-MM-dd HH:mm') : ''}">createdAt</td>
            <td th:text="${contract.deliveryDate != null ? #temporals.format(contract.deliveryDate, 'yyyy-MM-dd') : ''}">deliveryDate</td>
            <td th:text="${contract.descriptionAndRequirements}">descriptionAndRequirements</td>
            <td th:text="${contract.estimatedDuration}">estimatedDuration</td>
            <td th:text="${contract.invoiced}">invoiced</td>
            <td th:text="${contract.name}">name</td>
            <td th:text="${contract.number}">number</td>
            <td th:text="${contract.overdueDays}">overdueDays</td>
            <td th:text="${#numbers.formatDecimal(contract.remainingToInvoice, 1, 'COMMA', 2, 'POINT')}">remainingToInvoice</td>
            <td th:text="${contract.responsible}">responsible</td>
            <td th:text="${contract.status}">status</td>
            <td th:text="${contract.subtype}">subtype</td>
            <td th:text="${#numbers.formatDecimal(contract.tradeMargin, 1, 'COMMA', 2, 'POINT')}">tradeMargin</td>
            <td th:text="${#numbers.formatDecimal(contract.tradeMarginPercent, 1, 'COMMA', 2, 'POINT')}">tradeMarginPercent</td>
            <td th:text="${contract.type}">type</td>
            <td th:text="${#numbers.formatDecimal(contract.uninvoicedPayments, 1, 'COMMA', 2, 'POINT')}">uninvoicedPayments</td>
            <td th:text="${contract.customerId}">customerId</td>
            <td>
                <a th:href="@{/contracts/edit/{id}(id=${contract.id})}" class="btn btn-sm btn-secondary" th:text="#{button.edit}">Edit</a>
                <a th:href="@{/contracts/delete/{id}(id=${contract.id})}" class="btn btn-sm btn-danger"
                   th:onclick="return confirm(#{confirm.deleteContract});" th:text="#{button.delete}">Delete</a>
            </td>
        </tr>
        </tbody>
    </table>
</section>
</html>
