<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:lang="${#locale.language}"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout}">
<head>
    <title th:text="#{customerTypes.list.title}">Customer Types List</title>
</head>
<body>
<section layout:fragment="content">
    <h2 th:text="#{customerTypes.list.title}">Customer Types List</h2>
    <a th:href="@{/customer-types/new}" class="btn btn-primary mb-3" th:text="#{customerTypes.add}">Add Customer Type</a>

    <table class="table table-bordered table-striped">
        <thead>
        <tr>
            <th>ID</th>
            <th th:text="#{label.name}">Name</th>
            <th th:text="#{customerTypes.actions}">Actions</th>
        </tr>
        </thead>
        <tbody>
        <tr th:each="customerType : ${customerTypes}">
            <td th:text="${customerType.id}">ID</td>
            <td th:text="${customerType.name}">Name</td>
            <td>
                <a th:href="@{'/customer-types/edit/' + ${customerType.id}}" class="btn btn-sm btn-secondary"
                   th:text="#{button.edit}">Edit</a>
                <form th:action="@{'/customer-types/delete/' + ${customerType.id}}" method="post" style="display:inline;">
                    <input type="hidden" name="_method" value="delete"/>
                    <button type="submit" class="btn btn-sm btn-danger"
                            th:text="#{button.delete}"
                            th:onclick="|return confirm('${#{confirm.delete}}');|">
                        Delete
                    </button>
                </form>
            </td>
        </tr>
        </tbody>
    </table>
</section>
</body>
</html>
