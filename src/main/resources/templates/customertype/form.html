<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:lang="${#locale.language}"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout}">
<head>
    <meta charset="UTF-8">
    <title th:text="${customerType != null and customerType.id != null} ? #{customerType.edit.title} : #{customerType.create.title}">
        Customer Type Form
    </title>
</head>

<body>
<section layout:fragment="content">
    <div class="container">
        <form th:action="@{/customer-types/save}" th:object="${customerType}" method="post">

            <div th:if="${#fields.hasErrors('*')}" class="alert alert-danger" th:text="#{form.error.fix}">
                Fix errors in form
            </div>

            <h2 th:text="*{id} != null ? #{customerType.edit.title} : #{customerType.create.title}">
                Customer Type Form
            </h2>

            <input type="hidden" th:field="*{id}" />

            <div class="mb-3">
                <label for="name" class="form-label" th:text="#{label.name}">Name</label>
                <input type="text" th:field="*{name}" class="form-control" id="name" />
                <div class="text-danger" th:if="${#fields.hasErrors('name')}" th:errors="*{name}"></div>
            </div>

            <button type="submit" class="btn btn-success" th:text="#{button.save}">Save</button>
            <a th:href="@{/customer-types}" class="btn btn-secondary" th:text="#{button.cancel}">Cancel</a>
        </form>
    </div>
</section>
</body>
</html>
