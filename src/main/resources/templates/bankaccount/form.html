<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      th:lang="${#locale.language}"
      layout:decorate="~{layout}">
<head>
    <meta charset="UTF-8">
    <title th:text="${bankaccount.id != null} ? #{bankaccount.form.editTitle} : #{bankaccount.form.createTitle}">BankAccount Form</title>
</head>

<body>
<section layout:fragment="content">
    <div class="container">
        <form th:action="@{/bankaccounts/save}" th:object="${bankaccount}" method="post">

            <h2 th:text="${#fields.hasErrors('*')} ? #{bankaccount.form.fixErrors} : (bankaccount.id != null ? #{bankaccount.form.editTitle} : #{bankaccount.form.createTitle})">
                BankAccount Form
            </h2>

            <div class="mb-3">
                <label for="id" class="form-label" th:text="#{bankaccount.id}">ID</label>
                <input type="text" th:field="*{id}" class="form-control" id="id" />
                <div class="text-danger" th:if="${#fields.hasErrors('id')}" th:errors="*{id}"></div>
            </div>

            <div class="mb-3">
                <label for="bankCode" class="form-label" th:text="#{bankaccount.bankCode}">Bank Code</label>
                <input type="text" th:field="*{bankCode}" class="form-control" id="bankCode" />
                <div class="text-danger" th:if="${#fields.hasErrors('bankCode')}" th:errors="*{bankCode}"></div>
            </div>

            <div class="mb-3">
                <label for="iban" class="form-label" th:text="#{bankaccount.iban}">IBAN</label>
                <input type="text" th:field="*{iban}" class="form-control" id="iban" />
                <div class="text-danger" th:if="${#fields.hasErrors('iban')}" th:errors="*{iban}"></div>
            </div>

            <div class="mb-3">
                <label for="number" class="form-label" th:text="#{bankaccount.number}">Number</label>
                <input type="text" th:field="*{number}" class="form-control" id="number" />
                <div class="text-danger" th:if="${#fields.hasErrors('number')}" th:errors="*{number}"></div>
            </div>

            <div class="mb-3">
                <label for="prefix" class="form-label" th:text="#{bankaccount.prefix}">Prefix</label>
                <input type="text" th:field="*{prefix}" class="form-control" id="prefix" />
                <div class="text-danger" th:if="${#fields.hasErrors('prefix')}" th:errors="*{prefix}"></div>
            </div>

            <div class="mb-3">
                <label for="swift" class="form-label" th:text="#{bankaccount.swift}">SWIFT</label>
                <input type="text" th:field="*{swift}" class="form-control" id="swift" />
                <div class="text-danger" th:if="${#fields.hasErrors('swift')}" th:errors="*{swift}"></div>
            </div>

            <div class="mb-3">
                <label for="customerId" class="form-label" th:text="#{bankaccount.customerId}">Customer ID</label>
                <input type="text" th:field="*{customerId}" class="form-control" id="customerId" />
                <div class="text-danger" th:if="${#fields.hasErrors('customerId')}" th:errors="*{customerId}"></div>
            </div>

            <button type="submit" class="btn btn-success" th:text="#{bankaccount.form.save}">Save</button>
            <a th:href="@{/bankaccounts}" class="btn btn-secondary" th:text="#{bankaccount.form.cancel}">Cancel</a>
        </form>
    </div>
</section>
</body>
</html>
