<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:lang="${#locale.language}"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout}">
<head>
    <title th:text="#{bankaccount.list.title}">BankAccount List</title>
</head>

<body>
<section layout:fragment="content">
    <h2 th:text="#{bankaccount.list.heading}">BankAccount List</h2>
    <a th:href="@{/bankaccounts/create}" class="btn btn-primary mb-3" th:text="#{bankaccount.list.add}">Add BankAccount</a>

    <table class="table table-bordered table-striped">
        <thead>
        <tr>
            <th th:text="#{bankaccount.id}">id</th>
            <th th:text="#{bankaccount.bankCode}">bankCode</th>
            <th th:text="#{bankaccount.iban}">iban</th>
            <th th:text="#{bankaccount.number}">number</th>
            <th th:text="#{bankaccount.prefix}">prefix</th>
            <th th:text="#{bankaccount.swift}">swift</th>
            <th th:text="#{bankaccount.customerId}">customerId</th>
            <th th:text="#{bankaccount.actions}">Actions</th>
        </tr>
        </thead>
        <tbody>
        <tr th:each="bankaccount : ${bankaccounts}">
            <td th:text="${bankaccount.id}">id</td>
            <td th:text="${bankaccount.bankCode}">bankCode</td>
            <td th:text="${bankaccount.iban}">iban</td>
            <td th:text="${bankaccount.number}">number</td>
            <td th:text="${bankaccount.prefix}">prefix</td>
            <td th:text="${bankaccount.swift}">swift</td>
            <td th:text="${bankaccount.customerId}">customerId</td>
            <td>
                <a th:href="@{/bankaccounts/edit/{id}(id=${bankaccount.id})}" class="btn btn-sm btn-secondary" th:text="#{bankaccount.edit}">Edit</a>
                <a th:href="@{/bankaccounts/delete/{id}(id=${bankaccount.id})}" class="btn btn-sm btn-danger"
                   onclick="return confirm(/*[[#{bankaccount.confirmDelete}]]*/'Are you sure you want to delete this bankaccount?');"
                   th:text="#{bankaccount.delete}">Delete</a>
            </td>
        </tr>
        </tbody>
    </table>
</section>
</body>
</html>
