<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:lang="${#locale.language}"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout}">
<head>
    <meta charset="UTF-8">
    <title th:text="${event.id != null} ? #{businessEvent.editTitle} : #{businessEvent.createTitle}">Business Event Form</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
</head>

<body>
<section layout:fragment="content">
    <div class="container">
        <form th:action="@{/business-events/save}" th:object="${event}" method="post">
            <h2 th:text="${#fields.hasErrors('*')} ? #{form.error.fix} : (event.id != null ? #{businessEvent.editTitle} : #{businessEvent.createTitle})">
                Business Event</h2>

            <div class="mb-3">
                <label for="eventType" class="form-label" th:text="#{businessEvent.eventType}">Event Type</label>
                <select th:field="*{eventType}" class="form-control" id="eventType">
                    <option th:each="eventType : ${eventTypes}" th:value="${eventType.id}" th:text="${eventType.name}">
                        Event Type
                    </option>
                </select>
                <div class="text-danger" th:errors="*{eventType}"></div>
            </div>

            <div class="mb-3">
                <label for="eventCategory" class="form-label" th:text="#{businessEvent.eventCategory}">Event Category</label>
                <select th:field="*{eventCategory}" class="form-control" id="eventCategory">
                    <option th:each="eventCategory : ${eventCategories}" th:value="${eventCategory.id}"
                            th:text="${eventCategory.name}">
                        Event Category
                    </option>
                </select>
                <div class="text-danger" th:errors="*{eventCategory}"></div>
            </div>

            <div class="mb-3">
                <label for="direction" class="form-label" th:text="#{businessEvent.direction}">Direction</label>
                <input type="text" th:field="*{direction}" class="form-control" id="direction"/>
                <div class="text-danger" th:errors="*{direction}"></div>
            </div>

            <div class="mb-3">
                <label for="scheduledFrom" class="form-label" th:text="#{businessEvent.scheduledFrom}">Scheduled From</label>
                <input type="text" th:field="*{scheduledFrom}" class="form-control" id="scheduledFrom"/>
                <div class="text-danger" th:errors="*{scheduledFrom}"></div>
            </div>

            <div class="mb-3">
                <label for="scheduledTo" class="form-label" th:text="#{businessEvent.scheduledTo}">Scheduled To</label>
                <input type="text" th:field="*{scheduledTo}" class="form-control" id="scheduledTo"/>
                <div class="text-danger" th:errors="*{scheduledTo}"></div>
            </div>

            <div class="mb-3">
                <label for="realizationDate" class="form-label" th:text="#{businessEvent.realizationDate}">Realization Date</label>
                <input type="text" th:field="*{realizationDate}" class="form-control" id="realizationDate"/>
                <div class="text-danger" th:errors="*{realizationDate}"></div>
            </div>

            <div class="mb-3">
                <label for="dealNumber" class="form-label" th:text="#{businessEvent.dealNumber}">Deal Number</label>
                <input type="text" th:field="*{dealNumber}" class="form-control" id="dealNumber"/>
                <div class="text-danger" th:errors="*{dealNumber}"></div>
            </div>

            <div class="mb-3">
                <label for="department" class="form-label" th:text="#{businessEvent.department}">Department</label>
                <input type="text" th:field="*{department}" class="form-control" id="department"/>
                <div class="text-danger" th:errors="*{department}"></div>
            </div>

            <div class="mb-3">
                <label for="note" class="form-label" th:text="#{businessEvent.note}">Note</label>
                <textarea th:field="*{note}" class="form-control" id="note"></textarea>
                <div class="text-danger" th:errors="*{note}"></div>
            </div>

            <div class="mb-3">
                <label for="customer" class="form-label" th:text="#{businessEvent.customer}">Customer</label>
                <select th:field="*{customer}" class="form-control" id="customer">
                    <option th:each="customer : ${customers}" th:value="${customer.id}" th:text="${customer.name}">Customer</option>
                </select>
                <div class="text-danger" th:errors="*{customer}"></div>
            </div>

            <div class="mb-3" th:if="${users != null}">
                <label for="user" class="form-label" th:text="#{businessEvent.user}">User</label>
                <select th:field="*{user}" class="form-control" id="user">
                    <option th:each="user : ${users}" th:value="${user.id}" th:text="${user.email}">User</option>
                </select>
                <div class="text-danger" th:errors="*{user}"></div>
            </div>

            <button type="submit" class="btn btn-success" th:text="#{button.save}">Save</button>
            <a th:href="@{'/business-events/customer/' + ${currentCustomer}}" class="btn btn-secondary me-2" th:text="#{button.cancel}">Cancel</a>
            <a th:href="@{/customers}" id="backToCustomerListBtn" class="btn btn-outline-secondary" th:text="#{button.backToCustomerList}">Back to Customer List</a>
        </form>
    </div>
</section>


<th:block layout:fragment="scripts">
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            flatpickr("#scheduledFrom", {
                enableTime: true,
                dateFormat: "Y-m-d\\TH:i"
            });
            flatpickr("#scheduledTo", {
                enableTime: true,
                dateFormat: "Y-m-d\\TH:i"
            });
            flatpickr("#realizationDate", {
                enableTime: true,
                dateFormat: "Y-m-d\\TH:i"
            });

            // Set up back button with saved filters
            setupBackButton();
        });

        // Function to get cookie value
        function getCookie(name) {
            const nameEQ = name + '=';
            const ca = document.cookie.split(';');
            for (let i = 0; i < ca.length; i++) {
                let c = ca[i];
                while (c.charAt(0) === ' ') c = c.substring(1, c.length);
                if (c.indexOf(nameEQ) === 0) return decodeURIComponent(c.substring(nameEQ.length, c.length));
            }
            return null;
        }

        // Function to build customer list URL with saved filters
        function buildCustomerListUrl() {
            const filters = {
                nameFilter: getCookie('customer_nameFilter') || '',
                icoFilter: getCookie('customer_icoFilter') || '',
                cityFilter: getCookie('customer_cityFilter') || '',
                countryFilter: getCookie('customer_countryFilter') || '',
                typeFilter: getCookie('customer_typeFilter') || '',
                subtypeFilter: getCookie('customer_subtypeFilter') || '',
                responsiblePersonFilter: getCookie('customer_responsiblePersonFilter') || '',
                page: getCookie('customer_page') || '0',
                size: getCookie('customer_size') || '20',
                sortBy: getCookie('customer_sortBy') || 'name',
                sortDir: getCookie('customer_sortDir') || 'asc'
            };

            // Handle tagFilters
            const savedTagFilters = getCookie('customer_tagFilters');
            let tagFilters = [];
            if (savedTagFilters) {
                try {
                    tagFilters = JSON.parse(savedTagFilters);
                } catch (e) {
                    console.error('Error parsing saved tag filters:', e);
                }
            }

            // Build URL with non-empty filters
            const url = new URL('/customers', window.location.origin);
            Object.keys(filters).forEach(key => {
                if (filters[key] && filters[key] !== '') {
                    url.searchParams.set(key, filters[key]);
                }
            });

            // Add tag filters
            if (tagFilters.length > 0) {
                tagFilters.forEach(tagId => {
                    url.searchParams.append('tagFilters', tagId);
                });
            }

            return url.toString();
        }

        // Set up back button
        function setupBackButton() {
            const backBtn = document.getElementById('backToCustomerListBtn');
            if (backBtn) {
                try {
                    const urlWithFilters = buildCustomerListUrl();
                    backBtn.href = urlWithFilters;
                    console.log('Back button URL set to:', urlWithFilters);
                } catch (error) {
                    console.error('Error setting up back button:', error);
                    // Fallback to basic customer list URL
                    backBtn.href = '/customers';
                }
            }
        }
    </script>
</th:block>

</body>
</html>
