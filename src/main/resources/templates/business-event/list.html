<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:lang="${#locale.language}"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout}">
<head>
    <meta charset="UTF-8" />
    <title th:text="${customer != null} ? #{businessEvents.title(${customer.name})} : #{businessEvents.heading}">Business Events</title>
    <style>
        .truncated-note {
            max-width: 150px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            cursor: help;
        }
    </style>
</head>

<section layout:fragment="content">
    <h2 th:text="${customer != null} ? #{businessEvents.title(${customer.name})} : #{businessEvents.heading}">Business Events</h2>
    
    <a th:if="${customer != null}"
       th:href="@{'/business-events/new/customer/' + ${customer.id}}"
       class="btn btn-primary mb-3"
       th:text="#{businessEvents.addForCustomer(${customer.name})}">
        Add Business Event for Customer
    </a>
    
    <a th:if="${customer == null}"
       th:href="@{/business-events/new}"
       class="btn btn-primary mb-3"
       th:text="#{businessEvents.add}">
        Add Business Event
    </a>

    <table class="table table-bordered table-striped">
        <thead>
        <tr>
            <th th:text="#{businessEvents.eventType}">Event Type</th>
            <th th:text="#{businessEvents.category}">Category</th>
            <th th:text="#{businessEvents.direction}">Direction</th>
            <th th:text="#{businessEvents.customer}">Customer</th>
            <th th:text="#{businessEvents.user}">User</th>
            <th th:text="#{businessEvents.note}">Note</th>
            <th th:text="#{businessEvents.actions}">Actions</th>
        </tr>
        </thead>
        <tbody>
        <tr th:each="event : ${businessEvents}">
            <td th:text="${event.eventType?.name}">Event Type</td>
            <td th:text="${event.eventCategory?.name}">Category</td>
            <td th:text="${event.direction}">Direction</td>
            <td>
                <a th:if="${event.customer != null}" 
                   th:href="@{'/customers/edit/' + ${event.customer.id}}" 
                   th:text="${event.customer.name}">Customer</a>
            </td>
            <td th:text="${event.user != null ? event.user.firstName + ' ' + event.user.lastName : ''}">User</td>
            <td>
                <span th:if="${event.note != null && !event.note.isEmpty()}" 
                      class="truncated-note" 
                      th:title="${event.note}" 
                      th:text="${#strings.abbreviate(event.note, 30)}">
                    Note text...
                </span>
            </td>
            <td>
                <a th:href="@{'/business-events/edit/' + ${event.id}}" class="btn btn-sm btn-secondary" th:text="#{button.edit}">Edit</a>
                <form th:action="@{'/business-events/delete/' + ${event.id}}" method="post" style="display:inline;">
                    <button type="submit" class="btn btn-sm btn-danger"
                            th:onclick="'return confirm(\'' + #{businessEvents.confirmDelete} + '\');'"
                            th:text="#{button.delete}">Delete</button>
                </form>
            </td>
        </tr>
        <tr th:if="${businessEvents.empty}">
            <td colspan="11" class="text-center" th:text="#{businessEvents.noRecords}">No business events found</td>
        </tr>
        </tbody>
    </table>
    
    <div th:if="${customer != null}" class="mt-3">
        <a th:href="@{'/customers/edit/' + ${customer.id}}" class="btn btn-secondary me-2" th:text="#{button.backToCustomer}">Back to Customer</a>
        <a th:href="@{/customers}" id="backToCustomerListBtn" class="btn btn-outline-secondary" th:text="#{button.backToCustomerList}">Back to Customer List</a>
    </div>
</section>

<script>
    // Function to get cookie value
    function getCookie(name) {
        const nameEQ = name + '=';
        const ca = document.cookie.split(';');
        for (let i = 0; i < ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) return decodeURIComponent(c.substring(nameEQ.length, c.length));
        }
        return null;
    }

    // Function to build customer list URL with saved filters
    function buildCustomerListUrl() {
        const filters = {
            nameFilter: getCookie('customer_nameFilter') || '',
            icoFilter: getCookie('customer_icoFilter') || '',
            cityFilter: getCookie('customer_cityFilter') || '',
            countryFilter: getCookie('customer_countryFilter') || '',
            typeFilter: getCookie('customer_typeFilter') || '',
            subtypeFilter: getCookie('customer_subtypeFilter') || '',
            responsiblePersonFilter: getCookie('customer_responsiblePersonFilter') || '',
            page: getCookie('customer_page') || '0',
            size: getCookie('customer_size') || '20',
            sortBy: getCookie('customer_sortBy') || 'name',
            sortDir: getCookie('customer_sortDir') || 'asc'
        };

        // Handle tagFilters
        const savedTagFilters = getCookie('customer_tagFilters');
        let tagFilters = [];
        if (savedTagFilters) {
            try {
                tagFilters = JSON.parse(savedTagFilters);
            } catch (e) {
                console.error('Error parsing saved tag filters:', e);
            }
        }

        // Build URL with non-empty filters
        const url = new URL('/customers', window.location.origin);
        Object.keys(filters).forEach(key => {
            if (filters[key] && filters[key] !== '') {
                url.searchParams.set(key, filters[key]);
            }
        });

        // Add tag filters
        if (tagFilters.length > 0) {
            tagFilters.forEach(tagId => {
                url.searchParams.append('tagFilters', tagId);
            });
        }

        return url.toString();
    }

    // Set up back button when page loads
    document.addEventListener('DOMContentLoaded', function() {
        const backBtn = document.getElementById('backToCustomerListBtn');
        if (backBtn) {
            try {
                const urlWithFilters = buildCustomerListUrl();
                backBtn.href = urlWithFilters;
                console.log('Back button URL set to:', urlWithFilters);
            } catch (error) {
                console.error('Error setting up back button:', error);
                // Fallback to basic customer list URL
                backBtn.href = '/customers';
            }
        }
    });
</script>
</html>
