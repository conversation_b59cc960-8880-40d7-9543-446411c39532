<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:lang="${#locale.language}"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout}">
<head>
    <meta charset="UTF-8" />
    <title th:text="${contact.id != null} ? #{contact.editTitle} : #{contact.createTitle}">Contact Form</title>
</head>

<body>
<section layout:fragment="content">
    <div class="container">
        <form th:action="@{/contacts/save}" th:object="${contact}" method="post">

            <h2 th:text="${#fields.hasErrors('*')} ? #{form.errorsFix} : (contact.id != null ? #{contact.editTitle} : #{contact.createTitle})">
                Contact Form
            </h2>

            <div class="mb-3">
                <label for="id" class="form-label" th:text="#{contact.id}">ID</label>
                <input type="text" th:field="*{id}" class="form-control" id="id" />
                <div class="text-danger" th:if="${#fields.hasErrors('id')}" th:errors="*{id}"></div>
            </div>

            <div class="mb-3">
                <label for="email" class="form-label" th:text="#{contact.email}">Email</label>
                <input type="text" th:field="*{email}" class="form-control" id="email" />
                <div class="text-danger" th:if="${#fields.hasErrors('email')}" th:errors="*{email}"></div>
            </div>

            <div class="mb-3">
                <label for="firstName" class="form-label" th:text="#{contact.firstName}">First Name</label>
                <input type="text" th:field="*{firstName}" class="form-control" id="firstName" />
                <div class="text-danger" th:if="${#fields.hasErrors('firstName')}" th:errors="*{firstName}"></div>
            </div>

            <div class="mb-3">
                <label for="lastName" class="form-label" th:text="#{contact.lastName}">Last Name</label>
                <input type="text" th:field="*{lastName}" class="form-control" id="lastName" />
                <div class="text-danger" th:if="${#fields.hasErrors('lastName')}" th:errors="*{lastName}"></div>
            </div>

            <div class="mb-3 form-check">
                <input type="checkbox" th:field="*{mainContact}" class="form-check-input" id="mainContact" />
                <label for="mainContact" class="form-check-label" th:text="#{contact.mainContact}">Main Contact</label>
                <div class="text-danger" th:if="${#fields.hasErrors('mainContact')}" th:errors="*{mainContact}"></div>
            </div>

            <div class="mb-3 form-check">
                <input type="checkbox" th:field="*{newsletterSubscribed}" class="form-check-input" id="newsletterSubscribed" />
                <label for="newsletterSubscribed" class="form-check-label" th:text="#{contact.newsletterSubscribed}">Newsletter Subscribed</label>
                <div class="text-danger" th:if="${#fields.hasErrors('newsletterSubscribed')}" th:errors="*{newsletterSubscribed}"></div>
            </div>

            <div class="mb-3">
                <label for="note" class="form-label" th:text="#{contact.note}">Note</label>
                <input type="text" th:field="*{note}" class="form-control" id="note" />
                <div class="text-danger" th:if="${#fields.hasErrors('note')}" th:errors="*{note}"></div>
            </div>

            <div class="mb-3">
                <label for="otherContact" class="form-label" th:text="#{contact.otherContact}">Other Contact</label>
                <input type="text" th:field="*{otherContact}" class="form-control" id="otherContact" />
                <div class="text-danger" th:if="${#fields.hasErrors('otherContact')}" th:errors="*{otherContact}"></div>
            </div>

            <div class="mb-3">
                <label for="phone1" class="form-label" th:text="#{contact.phone1}">Phone 1</label>
                <input type="text" th:field="*{phone1}" class="form-control" id="phone1" />
                <div class="text-danger" th:if="${#fields.hasErrors('phone1')}" th:errors="*{phone1}"></div>
            </div>

            <div class="mb-3">
                <label for="phone2" class="form-label" th:text="#{contact.phone2}">Phone 2</label>
                <input type="text" th:field="*{phone2}" class="form-control" id="phone2" />
                <div class="text-danger" th:if="${#fields.hasErrors('phone2')}" th:errors="*{phone2}"></div>
            </div>

            <div class="mb-3">
                <label for="position" class="form-label" th:text="#{contact.position}">Position</label>
                <input type="text" th:field="*{position}" class="form-control" id="position" />
                <div class="text-danger" th:if="${#fields.hasErrors('position')}" th:errors="*{position}"></div>
            </div>

            <div class="mb-3">
                <label for="suffix" class="form-label" th:text="#{contact.suffix}">Suffix</label>
                <input type="text" th:field="*{suffix}" class="form-control" id="suffix" />
                <div class="text-danger" th:if="${#fields.hasErrors('suffix')}" th:errors="*{suffix}"></div>
            </div>

            <div class="mb-3">
                <label for="title" class="form-label" th:text="#{contact.title}">Title</label>
                <input type="text" th:field="*{title}" class="form-control" id="title" />
                <div class="text-danger" th:if="${#fields.hasErrors('title')}" th:errors="*{title}"></div>
            </div>

            <div class="mb-3">
                <label for="customerId" class="form-label" th:text="#{contact.customerId}">Customer ID</label>
                <input type="text" th:field="*{customerId}" class="form-control" id="customerId" />
                <div class="text-danger" th:if="${#fields.hasErrors('customerId')}" th:errors="*{customerId}"></div>
            </div>

            <button type="submit" class="btn btn-success" th:text="#{button.save}">Save</button>
            <a th:href="@{/contacts}" class="btn btn-secondary" th:text="#{button.cancel}">Cancel</a>
        </form>
    </div>
</section>
</body>
</html>
