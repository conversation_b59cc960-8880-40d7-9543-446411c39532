<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      th:lang="${#locale.language}"
      layout:decorate="~{layout}">
<head>
    <title th:text="#{contact.listTitle}">Contact List</title>
</head>
<body>
<section layout:fragment="content">
    <h2 th:text="#{contact.listTitle}">Contact List</h2>
    <a th:href="@{/contacts/create}" class="btn btn-primary mb-3" th:text="#{contact.add}">Add Contact</a>

    <table class="table table-bordered table-striped">
        <thead>
        <tr>
            <th th:text="#{contact.id}">ID</th>
            <th th:text="#{contact.email}">Email</th>
            <th th:text="#{contact.firstName}">First Name</th>
            <th th:text="#{contact.lastName}">Last Name</th>
            <th th:text="#{contact.mainContact}">Main Contact</th>
            <th th:text="#{contact.newsletterSubscribed}">Newsletter Subscribed</th>
            <th th:text="#{contact.note}">Note</th>
            <th th:text="#{contact.otherContact}">Other Contact</th>
            <th th:text="#{contact.phone1}">Phone 1</th>
            <th th:text="#{contact.phone2}">Phone 2</th>
            <th th:text="#{contact.position}">Position</th>
            <th th:text="#{contact.suffix}">Suffix</th>
            <th th:text="#{contact.title}">Title</th>
            <th th:text="#{contact.customerId}">Customer ID</th>
            <th th:text="#{actions}">Actions</th>
        </tr>
        </thead>
        <tbody>
        <tr th:each="contact : ${contacts}">
            <td th:text="${contact.id}">ID</td>
            <td th:text="${contact.email}">Email</td>
            <td th:text="${contact.firstName}">First Name</td>
            <td th:text="${contact.lastName}">Last Name</td>
            <td th:text="${contact.mainContact}">Main Contact</td>
            <td th:text="${contact.newsletterSubscribed}">Newsletter Subscribed</td>
            <td th:text="${contact.note}">Note</td>
            <td th:text="${contact.otherContact}">Other Contact</td>
            <td th:text="${contact.phone1}">Phone 1</td>
            <td th:text="${contact.phone2}">Phone 2</td>
            <td th:text="${contact.position}">Position</td>
            <td th:text="${contact.suffix}">Suffix</td>
            <td th:text="${contact.title}">Title</td>
            <td th:text="${contact.customerId}">Customer ID</td>
            <td>
                <a th:href="@{/contacts/edit/{id}(id=${contact.id})}" class="btn btn-sm btn-secondary" th:text="#{button.edit}">Edit</a>
                <a th:href="@{/contacts/delete/{id}(id=${contact.id})}" class="btn btn-sm btn-danger"
                   th:onclick="'return confirm(\'' + #{confirm.deleteContact} + '\');'" th:text="#{button.delete}">Delete</a>
            </td>
        </tr>
        </tbody>
    </table>
</section>
</body>
</html>
