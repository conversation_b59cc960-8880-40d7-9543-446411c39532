<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      th:lang="${#locale.language}"
      layout:decorate="~{layout}">
<head>
    <meta charset="UTF-8"/>
    <title th:text="${pageTitle}">History</title>
</head>

<body>
<section layout:fragment="content">
    <div class="container-fluid">
        <h2 th:text="${pageTitle}">History</h2>
        
        <div th:if="${customer != null}" class="mb-3">
            <p><strong th:text="#{history.customer}">Customer:</strong> 
               <span th:text="${customer.name}">Customer Name</span></p>
            <a th:href="@{/customers}" class="btn btn-outline-secondary me-2" th:text="#{button.backToCustomerList}">Back to Customer List</a>
            <a th:href="@{'/customers/edit/' + ${customer.id}}" class="btn btn-outline-primary" th:text="#{button.backToCustomer}">Back to Customer</a>
        </div>

        <div th:if="${customer == null}" class="mb-3">
            <a th:href="@{/customers}" class="btn btn-outline-secondary" th:text="#{button.backToCustomerList}">Back to Customer List</a>
        </div>

        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th th:text="#{history.type}">Type</th>
                        <th th:text="#{history.title}">Title</th>
                        <th th:text="#{history.user}">Responsible User</th>
                        <th th:text="#{history.status}">Status</th>
                        <th th:text="#{history.createdAt}">Created</th>
                        <th th:text="#{history.realizationDate}">Realized</th>
                        <th th:text="#{history.note}">Note</th>
                        <th th:text="#{history.details}">Details</th>
                        <th th:text="#{label.actions}">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:each="item : ${historyItems}" th:if="${historyItems != null && !historyItems.isEmpty()}">
                        <td>
                            <span th:switch="${item.type}" class="badge">
                                <span th:case="'BusinessEvent'" class="badge bg-primary" th:text="#{history.type.businessEvent}">Business Event</span>
                                <span th:case="'Task'" class="badge bg-success" th:text="#{history.type.task}">Task</span>
                                <span th:case="'Invoice'" class="badge bg-warning text-dark" th:text="#{history.type.invoice}">Invoice</span>
                                <span th:case="*" class="badge bg-secondary" th:text="${item.type}">Unknown</span>
                            </span>
                        </td>
                        <td th:text="${item.title}">Title</td>
                        <td th:text="${item.user != null ? item.user.firstName + ' ' + item.user.lastName : ''}">User</td>
                        <td>
                            <span th:if="${item.status != null}" 
                                  th:text="${item.status}" 
                                  class="badge bg-info">Status</span>
                        </td>
                        <td th:text="${item.createdAt != null ? #temporals.format(item.createdAt, 'yyyy-MM-dd HH:mm') : ''}">Created</td>
                        <td th:text="${item.realizationDate != null ? #temporals.format(item.realizationDate, 'yyyy-MM-dd HH:mm') : ''}">Realized</td>
                        <td>
                            <span th:if="${item.note != null && !item.note.isEmpty()}" 
                                  class="truncated-note" 
                                  th:title="${item.note}" 
                                  th:text="${#strings.abbreviate(item.note, 50)}">Note</span>
                        </td>
                        <td th:text="${item.entitySpecificInfo}">Details</td>
                        <td>
                            <a th:switch="${item.type}" class="btn btn-sm btn-outline-primary">
                                <span th:case="'BusinessEvent'" th:href="@{'/business-events/edit/' + ${item.id}}" th:text="#{button.view}">View</span>
                                <span th:case="'Task'" th:href="@{'/tasks/edit/' + ${item.id}}" th:text="#{button.view}">View</span>
                                <span th:case="'Invoice'" th:href="@{'/invoices/edit/' + ${item.id}}" th:text="#{button.view}">View</span>
                            </a>
                        </td>
                    </tr>
                    <tr th:if="${historyItems == null || historyItems.isEmpty()}">
                        <td colspan="9" class="text-center text-muted" th:text="#{history.noRecords}">No history records found</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</section>

<style>
.truncated-note {
    cursor: help;
}
.table-responsive {
    max-height: 70vh;
    overflow-y: auto;
}
</style>
</body>
</html>
