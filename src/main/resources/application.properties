# Application Version
spring.config.import=classpath:version.properties
#project.version=0.0.2
project.version=${app.version}

server.port=7080
spring.application.name=triple-tea

spring.datasource.url=***********************************************************************
spring.datasource.username=user
spring.datasource.password=pass

spring.thymeleaf.enabled=true
spring.thymeleaf.prefix=classpath:/templates/

spring.jpa.hibernate.ddl-auto=update

spring.flyway.enabled=true
spring.flyway.url=***********************************************************************&allowPublicKeyRetrieval=true
spring.flyway.user=user
spring.flyway.password=pass
spring.flyway.baseline-on-migrate=true
spring.flyway.validate-on-migrate=false

# Internationalization
spring.messages.basename=messages
spring.messages.encoding=UTF-8
spring.messages.cache-duration=3600
spring.web.locale=sk
spring.web.locale-resolver=fixed

import.path=C:\\Users\\<USER>\\Documents\\rpa\\tripple-tea\\import\\

# File upload settings
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=50MB
app.file.upload.customer-files-dir=C:\\Users\\<USER>\\Documents\\rpa\\tripple-tea\\uploads\\customers
