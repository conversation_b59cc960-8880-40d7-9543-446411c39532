<!DOCTYPE html>
<html>
<head>
    <title>Simple Bar Chart Test</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Simple Bar Chart Test</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Traditional Bar Chart (Single Dataset)</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="simpleChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Multi-Dataset Bar Chart (Toggleable Users)</h5>
                        <div class="mt-2">
                            <button class="btn btn-sm btn-outline-primary" onclick="toggleUser(0)">Toggle Ján</button>
                            <button class="btn btn-sm btn-outline-primary" onclick="toggleUser(1)">Toggle Anna</button>
                            <button class="btn btn-sm btn-outline-primary" onclick="toggleUser(2)">Toggle Mária</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <canvas id="multiChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-3">
            <h6>Data Structure Comparison:</h6>
            <div class="row">
                <div class="col-md-6">
                    <h6>Simple Chart Data:</h6>
                    <pre id="simpleData"></pre>
                </div>
                <div class="col-md-6">
                    <h6>Multi-Dataset Chart Data:</h6>
                    <pre id="multiData"></pre>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Sample data
        const users = ['Ján Novák', 'Anna Horváthová', 'Mária Svobodová'];
        const hours = [45, 38, 32];
        const colors = [
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 99, 132, 0.6)',
            'rgba(255, 205, 86, 0.6)'
        ];
        const borderColors = [
            'rgba(54, 162, 235, 1)',
            'rgba(255, 99, 132, 1)',
            'rgba(255, 205, 86, 1)'
        ];

        // Simple chart data structure
        const simpleChartData = {
            labels: users,
            datasets: [{
                label: 'Hodiny',
                data: hours,
                backgroundColor: colors,
                borderColor: borderColors,
                borderWidth: 1
            }]
        };

        // Multi-dataset chart data structure (each user is a separate dataset)
        const multiChartDatasets = users.map((user, index) => {
            const userData = new Array(users.length).fill(0);
            userData[index] = hours[index]; // Only this user's data
            
            return {
                label: user,
                data: userData,
                backgroundColor: colors[index],
                borderColor: borderColors[index],
                borderWidth: 1
            };
        });

        const multiChartData = {
            labels: users,
            datasets: multiChartDatasets
        };

        // Display data structures
        document.getElementById('simpleData').textContent = JSON.stringify(simpleChartData, null, 2);
        document.getElementById('multiData').textContent = JSON.stringify(multiChartData, null, 2);

        // Create simple chart
        const simpleCtx = document.getElementById('simpleChart').getContext('2d');
        const simpleChart = new Chart(simpleCtx, {
            type: 'bar',
            data: simpleChartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Hodiny'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Simple Bar Chart'
                    }
                }
            }
        });

        // Create multi-dataset chart
        const multiCtx = document.getElementById('multiChart').getContext('2d');
        const multiChart = new Chart(multiCtx, {
            type: 'bar',
            data: multiChartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Hodiny'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Multi-Dataset Bar Chart'
                    },
                    legend: {
                        display: true,
                        onClick: function(e, legendItem, legend) {
                            // Toggle dataset visibility
                            const index = legendItem.datasetIndex;
                            const chart = legend.chart;
                            const meta = chart.getDatasetMeta(index);
                            
                            meta.hidden = meta.hidden === null ? !chart.data.datasets[index].hidden : null;
                            chart.update();
                        }
                    }
                }
            }
        });

        // Function to toggle user visibility
        function toggleUser(index) {
            const meta = multiChart.getDatasetMeta(index);
            meta.hidden = meta.hidden === null ? !multiChart.data.datasets[index].hidden : null;
            multiChart.update();
        }

        console.log('Simple Chart Data:', simpleChartData);
        console.log('Multi Chart Data:', multiChartData);
    </script>
</body>
</html>
