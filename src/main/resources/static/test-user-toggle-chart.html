<!DOCTYPE html>
<html>
<head>
    <title>User Toggle Chart Test</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <h1>User Toggle Chart Test</h1>
        
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Odpracovan<PERSON> hodiny podľa používateľov </h5>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary btn-sm" id="prevWeekBtn">
                        <i class="fas fa-chevron-left"></i> Predchádzajúci týždeň
                    </button>
                    <button type="button" class="btn btn-outline-primary btn-sm" id="currentWeekBtn">
                        Aktuálny týždeň
                    </button>
                    <button type="button" class="btn btn-outline-primary btn-sm" id="nextWeekBtn">
                        Nasledujúci týždeň <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="weekTitle" class="text-center mb-3">
                    <h6 class="text-muted">Načítavam...</h6>
                </div>
                
                <!-- User Toggle Controls -->
                <div id="userControls" class="mb-3" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <small class="text-muted">Zobraziť/skryť používateľov:</small>
                        <div>
                            <button type="button" class="btn btn-outline-secondary btn-sm" id="showAllUsersBtn">
                                Zobraziť všetkých
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" id="hideAllUsersBtn">
                                Skryť všetkých
                            </button>
                        </div>
                    </div>
                    <div id="userToggleButtons" class="d-flex flex-wrap gap-1">
                        <!-- User toggle buttons will be dynamically added here -->
                    </div>
                </div>
                
                <div id="chartContainer">
                    <div id="chartLoading" class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Načítavam údaje o odpracovaných hodinách...</p>
                    </div>
                    <canvas id="workHoursChart" width="400" height="200" style="display: none;"></canvas>
                    <div id="chartError" class="text-center text-muted" style="display: none;">
                        <p>Zatiaľ nie sú k dispozícii žiadne údaje o odpracovaných hodinách pre tento týždeň.</p>
                        <small>Údaje sa zobrazia po dokončení úloh s vyplnenými skutočnými hodinami.</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-3">
            <h6>API Test Links:</h6>
            <ul>
                <li><a href="/api/charts/weekly-work-hours" target="_blank">Current Week Data</a></li>
                <li><a href="/api/charts/weekly-work-hours?weekStart=2024-01-01" target="_blank">Specific Week Data (2024-01-01)</a></li>
                <li><a href="/debug/tasks" target="_blank">Debug Tasks</a></li>
            </ul>
            
            <div class="alert alert-info">
                <h6>Features:</h6>
                <ul>
                    <li><strong>Bar Chart:</strong> Column chart with individual user datasets</li>
                    <li><strong>User Toggle Buttons:</strong> Click to show/hide individual users</li>
                    <li><strong>Legend Interaction:</strong> Click legend items to toggle users</li>
                    <li><strong>Permission-based:</strong> Only users with canViewAllWorkHours=true see all users</li>
                    <li><strong>Show/Hide All:</strong> Bulk controls for all users</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        let currentWeekStart = null;
        let currentChart = null;
        
        // Function to fetch weekly chart data and render the chart
        async function loadWeeklyWorkHoursChart(weekStart = null) {
            const loadingElement = document.getElementById('chartLoading');
            const chartCanvas = document.getElementById('workHoursChart');
            const errorElement = document.getElementById('chartError');
            const weekTitleElement = document.getElementById('weekTitle');
            
            // Show loading
            loadingElement.style.display = 'block';
            chartCanvas.style.display = 'none';
            errorElement.style.display = 'none';
            
            try {
                console.log('Fetching weekly work hours data for week:', weekStart);
                
                // Build URL with optional week parameter
                let url = '/api/charts/weekly-work-hours';
                if (weekStart) {
                    url += `?weekStart=${weekStart}`;
                }
                
                // Fetch data from the API endpoint
                const response = await fetch(url);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                console.log('Received weekly data:', data);
                
                // Update current week start for navigation
                currentWeekStart = data.weekStart;
                
                // Update week title
                weekTitleElement.innerHTML = `<h6 class="text-muted">${data.weekTitle}</h6>`;
                
                // Hide loading spinner
                loadingElement.style.display = 'none';
                
                // Check if we have data
                if (!data.success || !data.datasets || data.datasets.length === 0) {
                    console.warn('No chart data available for this week');
                    errorElement.style.display = 'block';
                    return;
                }
                
                // Show chart canvas and create the chart
                chartCanvas.style.display = 'block';
                createChart(data.datasets, data.weekTitle, data.canViewAllUsers, data.userNames);
                
            } catch (error) {
                console.error('Error fetching weekly chart data:', error);
                loadingElement.style.display = 'none';
                errorElement.style.display = 'block';
                errorElement.innerHTML = '<p class="text-danger">Chyba pri načítavaní údajov o odpracovaných hodinách.</p>';
            }
        }
        
        // Function to create the chart
        function createChart(datasets, weekTitle, canViewAllUsers, userNames) {
            console.log('Creating weekly chart with', datasets.length, 'users for', weekTitle);
            
            // Destroy existing chart if it exists
            if (currentChart) {
                currentChart.destroy();
            }

            // Show/hide user controls based on permissions and data
            const userControlsElement = document.getElementById('userControls');
            if (canViewAllUsers && datasets.length > 1) {
                userControlsElement.style.display = 'block';
                createUserToggleButtons(datasets);
            } else {
                userControlsElement.style.display = 'none';
            }

            // Create the chart using bar type with user visibility control
            const ctx = document.getElementById('workHoursChart').getContext('2d');
            currentChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: datasets.map(dataset => dataset.label), // User names as labels
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Hodiny'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Používatelia'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            onClick: function(e, legendItem, legend) {
                                // Toggle dataset visibility
                                const index = legendItem.datasetIndex;
                                const chart = legend.chart;
                                const meta = chart.getDatasetMeta(index);

                                meta.hidden = meta.hidden === null ? !chart.data.datasets[index].hidden : null;
                                chart.update();

                                // Update toggle button state
                                updateToggleButtonState(index, meta.hidden);
                            }
                        },
                        title: {
                            display: true,
                            text: weekTitle
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': ' + context.parsed.y + ' hodín';
                                }
                            }
                        }
                    }
                }
            });
        }

        // Function to create user toggle buttons
        function createUserToggleButtons(datasets) {
            const container = document.getElementById('userToggleButtons');
            container.innerHTML = ''; // Clear existing buttons
            
            datasets.forEach((dataset, index) => {
                const button = document.createElement('button');
                button.type = 'button';
                button.className = 'btn btn-sm btn-outline-primary user-toggle-btn';
                button.dataset.index = index;
                button.innerHTML = `
                    <span class="color-indicator" style="background-color: ${dataset.backgroundColor}; width: 12px; height: 12px; display: inline-block; border-radius: 50%; margin-right: 5px;"></span>
                    ${dataset.label}
                `;
                
                button.addEventListener('click', function() {
                    toggleUser(index);
                });
                
                container.appendChild(button);
            });
        }

        // Function to toggle user visibility
        function toggleUser(index) {
            if (currentChart) {
                const meta = currentChart.getDatasetMeta(index);
                meta.hidden = meta.hidden === null ? !currentChart.data.datasets[index].hidden : null;
                currentChart.update();
                updateToggleButtonState(index, meta.hidden);
            }
        }

        // Function to update toggle button state
        function updateToggleButtonState(index, isHidden) {
            const button = document.querySelector(`[data-index="${index}"]`);
            if (button) {
                if (isHidden) {
                    button.classList.remove('btn-outline-primary');
                    button.classList.add('btn-outline-secondary');
                    button.style.opacity = '0.5';
                } else {
                    button.classList.remove('btn-outline-secondary');
                    button.classList.add('btn-outline-primary');
                    button.style.opacity = '1';
                }
            }
        }

        // Function to show all users
        function showAllUsers() {
            if (currentChart) {
                currentChart.data.datasets.forEach((dataset, index) => {
                    const meta = currentChart.getDatasetMeta(index);
                    meta.hidden = null;
                    updateToggleButtonState(index, false);
                });
                currentChart.update();
            }
        }

        // Function to hide all users
        function hideAllUsers() {
            if (currentChart) {
                currentChart.data.datasets.forEach((dataset, index) => {
                    const meta = currentChart.getDatasetMeta(index);
                    meta.hidden = true;
                    updateToggleButtonState(index, true);
                });
                currentChart.update();
            }
        }

        // Navigation functions
        function goToPreviousWeek() {
            if (currentWeekStart) {
                const prevWeek = new Date(currentWeekStart);
                prevWeek.setDate(prevWeek.getDate() - 7);
                const prevWeekStr = prevWeek.toISOString().split('T')[0];
                loadWeeklyWorkHoursChart(prevWeekStr);
            }
        }
        
        function goToNextWeek() {
            if (currentWeekStart) {
                const nextWeek = new Date(currentWeekStart);
                nextWeek.setDate(nextWeek.getDate() + 7);
                const nextWeekStr = nextWeek.toISOString().split('T')[0];
                loadWeeklyWorkHoursChart(nextWeekStr);
            }
        }
        
        function goToCurrentWeek() {
            loadWeeklyWorkHoursChart(); // No parameter = current week
        }
        
        // Initialize the chart and navigation when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Load current week data
            loadWeeklyWorkHoursChart();
            
            // Add event listeners for navigation buttons
            document.getElementById('prevWeekBtn').addEventListener('click', goToPreviousWeek);
            document.getElementById('nextWeekBtn').addEventListener('click', goToNextWeek);
            document.getElementById('currentWeekBtn').addEventListener('click', goToCurrentWeek);
            
            // Add event listeners for user control buttons
            document.getElementById('showAllUsersBtn').addEventListener('click', showAllUsers);
            document.getElementById('hideAllUsersBtn').addEventListener('click', hideAllUsers);
        });
    </script>
</body>
</html>
