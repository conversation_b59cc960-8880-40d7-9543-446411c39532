<!DOCTYPE html>
<html>
<head>
    <title>Weekly Chart Test</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <h1>Weekly Work Hours Chart Test</h1>
        
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Odpracovan<PERSON> hodiny podľa používateľov </h5>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary btn-sm" id="prevWeekBtn">
                        <i class="fas fa-chevron-left"></i> Predchádzaj<PERSON><PERSON> týždeň
                    </button>
                    <button type="button" class="btn btn-outline-primary btn-sm" id="currentWeekBtn">
                        Aktuálny týždeň
                    </button>
                    <button type="button" class="btn btn-outline-primary btn-sm" id="nextWeekBtn">
                        Nasledujúci týždeň <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="weekTitle" class="text-center mb-3">
                    <h6 class="text-muted">Načítavam...</h6>
                </div>
                <div id="chartContainer">
                    <div id="chartLoading" class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Načítavam údaje o odpracovaných hodinách...</p>
                    </div>
                    <canvas id="workHoursChart" width="400" height="200" style="display: none;"></canvas>
                    <div id="chartError" class="text-center text-muted" style="display: none;">
                        <p>Zatiaľ nie sú k dispozícii žiadne údaje o odpracovaných hodinách pre tento týždeň.</p>
                        <small>Údaje sa zobrazia po dokončení úloh s vyplnenými skutočnými hodinami.</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-3">
            <h6>API Test Links:</h6>
            <ul>
                <li><a href="/api/charts/weekly-work-hours" target="_blank">Current Week Data</a></li>
                <li><a href="/api/charts/weekly-work-hours?weekStart=2024-01-01" target="_blank">Specific Week Data (2024-01-01)</a></li>
                <li><a href="/debug/tasks" target="_blank">Debug Tasks</a></li>
            </ul>
        </div>
    </div>

    <script>
        let currentWeekStart = null;
        let currentChart = null;
        
        // Function to fetch weekly chart data and render the chart
        async function loadWeeklyWorkHoursChart(weekStart = null) {
            const loadingElement = document.getElementById('chartLoading');
            const chartCanvas = document.getElementById('workHoursChart');
            const errorElement = document.getElementById('chartError');
            const weekTitleElement = document.getElementById('weekTitle');
            
            // Show loading
            loadingElement.style.display = 'block';
            chartCanvas.style.display = 'none';
            errorElement.style.display = 'none';
            
            try {
                console.log('Fetching weekly work hours data for week:', weekStart);
                
                // Build URL with optional week parameter
                let url = '/api/charts/weekly-work-hours';
                if (weekStart) {
                    url += `?weekStart=${weekStart}`;
                }
                
                // Fetch data from the API endpoint
                const response = await fetch(url);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                console.log('Received weekly data:', data);
                
                // Update current week start for navigation
                currentWeekStart = data.weekStart;
                
                // Update week title
                weekTitleElement.innerHTML = `<h6 class="text-muted">${data.weekTitle}</h6>`;
                
                // Hide loading spinner
                loadingElement.style.display = 'none';
                
                // Check if we have data
                if (!data.success || !data.userNames || !data.workHours || 
                    data.userNames.length === 0 || data.workHours.length === 0) {
                    console.warn('No chart data available for this week');
                    errorElement.style.display = 'block';
                    return;
                }
                
                // Show chart canvas and create the chart
                chartCanvas.style.display = 'block';
                createChart(data.userNames, data.workHours, data.weekTitle);
                
            } catch (error) {
                console.error('Error fetching weekly chart data:', error);
                loadingElement.style.display = 'none';
                errorElement.style.display = 'block';
                errorElement.innerHTML = '<p class="text-danger">Chyba pri načítavaní údajov o odpracovaných hodinách.</p>';
            }
        }
        
        // Function to create the chart
        function createChart(userNames, workHours, weekTitle) {
            console.log('Creating weekly chart with', userNames.length, 'users for', weekTitle);
            
            // Destroy existing chart if it exists
            if (currentChart) {
                currentChart.destroy();
            }
            
            // Create the chart
            const ctx = document.getElementById('workHoursChart').getContext('2d');
            currentChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: userNames,
                    datasets: [{
                        label: 'Hodiny',
                        data: workHours,
                        backgroundColor: [
                            'rgba(54, 162, 235, 0.6)',
                            'rgba(255, 99, 132, 0.6)',
                            'rgba(255, 205, 86, 0.6)',
                            'rgba(75, 192, 192, 0.6)',
                            'rgba(153, 102, 255, 0.6)',
                            'rgba(255, 159, 64, 0.6)',
                            'rgba(199, 199, 199, 0.6)',
                            'rgba(83, 102, 255, 0.6)'
                        ],
                        borderColor: [
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 99, 132, 1)',
                            'rgba(255, 205, 86, 1)',
                            'rgba(75, 192, 192, 1)',
                            'rgba(153, 102, 255, 1)',
                            'rgba(255, 159, 64, 1)',
                            'rgba(199, 199, 199, 1)',
                            'rgba(83, 102, 255, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Hodiny'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Používatelia'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        title: {
                            display: true,
                            text: weekTitle
                        }
                    }
                }
            });
        }
        
        // Navigation functions
        function goToPreviousWeek() {
            if (currentWeekStart) {
                const prevWeek = new Date(currentWeekStart);
                prevWeek.setDate(prevWeek.getDate() - 7);
                const prevWeekStr = prevWeek.toISOString().split('T')[0];
                loadWeeklyWorkHoursChart(prevWeekStr);
            }
        }
        
        function goToNextWeek() {
            if (currentWeekStart) {
                const nextWeek = new Date(currentWeekStart);
                nextWeek.setDate(nextWeek.getDate() + 7);
                const nextWeekStr = nextWeek.toISOString().split('T')[0];
                loadWeeklyWorkHoursChart(nextWeekStr);
            }
        }
        
        function goToCurrentWeek() {
            loadWeeklyWorkHoursChart(); // No parameter = current week
        }
        
        // Initialize the chart and navigation when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Load current week data
            loadWeeklyWorkHoursChart();
            
            // Add event listeners for navigation buttons
            document.getElementById('prevWeekBtn').addEventListener('click', goToPreviousWeek);
            document.getElementById('nextWeekBtn').addEventListener('click', goToNextWeek);
            document.getElementById('currentWeekBtn').addEventListener('click', goToCurrentWeek);
        });
    </script>
</body>
</html>
