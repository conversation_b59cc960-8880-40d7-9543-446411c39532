<!DOCTYPE html>
<html>
<head>
    <title>Chart Test</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <h1>Chart Test</h1>
    <div style="width: 600px; height: 400px;">
        <canvas id="testChart"></canvas>
    </div>

    <script>
        // Test data similar to what we're generating
        const userNames = ["<PERSON><PERSON>","<PERSON>","<PERSON><PERSON><PERSON>","<PERSON>","admin User"];
        const workHours = [45,38,32,28,25];

        console.log('Test user names:', userNames);
        console.log('Test work hours:', workHours);

        // Create the chart
        const ctx = document.getElementById('testChart').getContext('2d');
        const testChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: userNames,
                datasets: [{
                    label: 'Hodiny',
                    data: workHours,
                    backgroundColor: [
                        'rgba(54, 162, 235, 0.6)',
                        'rgba(255, 99, 132, 0.6)',
                        'rgba(255, 205, 86, 0.6)',
                        'rgba(75, 192, 192, 0.6)',
                        'rgba(153, 102, 255, 0.6)'
                    ],
                    borderColor: [
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 99, 132, 1)',
                        'rgba(255, 205, 86, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Hodiny'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Používatelia'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'Odpracované hodiny podľa používateľov'
                    }
                }
            }
        });

        console.log('Chart created successfully!');
    </script>
</body>
</html>
