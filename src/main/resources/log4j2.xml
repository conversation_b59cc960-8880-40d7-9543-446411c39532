<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
    <Properties>
        <!-- Define log pattern -->
        <Property name="LOG_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n</Property>
        <!-- Define log file path -->
        <Property name="LOG_FILE">logs/application.log</Property>
    </Properties>

    <Appenders>
        <!-- Console Appender -->
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <ThresholdFilter level="DEBUG" onMatch="ACCEPT" onMismatch="DENY"/>
        </Console>

        <!-- File Appender -->
        <File name="FileAppender" fileName="${LOG_FILE}">
            <PatternLayout pattern="${LOG_PATTERN}"/>
        </File>

        <!-- Rolling File Appender -->
        <RollingFile name="RollingFileAppender" fileName="${LOG_FILE}" 
                     filePattern="logs/application-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <!-- Roll over daily -->
                <TimeBasedTriggeringPolicy />
                <!-- Roll over when file reaches 10MB -->
                <SizeBasedTriggeringPolicy size="10MB"/>
            </Policies>
            <!-- Keep maximum 30 files -->
            <DefaultRolloverStrategy max="30"/>
        </RollingFile>

        <!-- Error File Appender -->
        <RollingFile name="ErrorFileAppender" fileName="logs/error.log" 
                     filePattern="logs/error-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            <Policies>
                <TimeBasedTriggeringPolicy />
                <SizeBasedTriggeringPolicy size="10MB"/>
            </Policies>
            <DefaultRolloverStrategy max="30"/>
        </RollingFile>
    </Appenders>

    <Loggers>
        <!-- Application specific loggers -->
        <Logger name="com.solax" level="DEBUG" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFileAppender"/>
            <AppenderRef ref="ErrorFileAppender"/>
        </Logger>

        <!-- Spring Framework loggers -->
        <Logger name="org.springframework" level="INFO" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFileAppender"/>
        </Logger>

        <!-- Hibernate/JPA loggers -->
        <Logger name="org.hibernate" level="WARN" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFileAppender"/>
        </Logger>

        <!-- SQL logging (uncomment to see SQL queries) -->
        <!--
        <Logger name="org.hibernate.SQL" level="DEBUG" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFileAppender"/>
        </Logger>
        <Logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="TRACE" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFileAppender"/>
        </Logger>
        -->

        <!-- Security loggers -->
        <Logger name="org.springframework.security" level="INFO" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFileAppender"/>
        </Logger>

        <!-- Thymeleaf loggers -->
        <Logger name="org.thymeleaf" level="WARN" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFileAppender"/>
        </Logger>

        <!-- Database connection pool loggers -->
        <Logger name="com.zaxxer.hikari" level="WARN" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFileAppender"/>
        </Logger>

        <!-- Root logger -->
        <Root level="INFO">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFileAppender"/>
            <AppenderRef ref="ErrorFileAppender"/>
        </Root>
    </Loggers>
</Configuration>
