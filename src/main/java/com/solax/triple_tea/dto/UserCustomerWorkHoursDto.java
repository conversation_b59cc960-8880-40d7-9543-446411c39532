package com.solax.triple_tea.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserCustomerWorkHoursDto {
    private String firstName;
    private String lastName;
    private String customerName;
    private Long customerId;
    private Long totalHours;

    public String getFullName() {
        return firstName + " " + lastName;
    }

    public String getCustomerDisplayName() {
        return customerName != null ? customerName : "Bez zákazníka";
    }
}
