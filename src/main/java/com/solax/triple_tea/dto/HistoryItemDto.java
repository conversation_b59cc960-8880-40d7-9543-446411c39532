package com.solax.triple_tea.dto;

import java.time.LocalDateTime;

import com.solax.triple_tea.entity.ProgressStatus;
import com.solax.triple_tea.entity.User;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class HistoryItemDto {
    
    private Long id;
    private String type; // "BusinessEvent", "Task", "Invoice"
    private String title; // Display name/title for the item
    private LocalDateTime realizationDate;
    private String note;
    private User user;
    private ProgressStatus status;
    private LocalDateTime createdAt;
    
    // Additional context fields
    private String entitySpecificInfo; // e.g., event type, task category, invoice number
}
