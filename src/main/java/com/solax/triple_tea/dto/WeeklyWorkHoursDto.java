package com.solax.triple_tea.dto;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.WeekFields;
import java.util.Locale;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class WeeklyWorkHoursDto {
    private String firstName;
    private String lastName;
    private Long totalHours;
    private LocalDate weekStart;
    private LocalDate weekEnd;
    private int year;
    private int weekNumber;

    public String getFullName() {
        return firstName + " " + lastName;
    }

    public String getWeekLabel() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd.MM.yyyy");
        return weekStart.format(formatter) + " - " + weekEnd.format(formatter);
    }

    public String getWeekTitle() {
        return "Týždeň " + weekNumber + "/" + year + " (" + getWeekLabel() + ")";
    }

    public static WeeklyWorkHoursDto fromUserWorkHours(UserWorkHoursDto userDto, LocalDate weekStart) {
        LocalDate weekEnd = weekStart.plusDays(6);
        WeekFields weekFields = WeekFields.of(Locale.getDefault());
        int weekNumber = weekStart.get(weekFields.weekOfYear());
        int year = weekStart.getYear();

        return new WeeklyWorkHoursDto(
            userDto.getFirstName(),
            userDto.getLastName(),
            userDto.getTotalHours(),
            weekStart,
            weekEnd,
            year,
            weekNumber
        );
    }
}
