package com.solax.triple_tea.test;

import java.time.LocalDateTime;

import com.solax.triple_tea.entity.Task;
import com.solax.triple_tea.entity.User;

/**
 * Test to verify sample data creation
 */
public class SampleDataTest {
    
    public static void main(String[] args) {
        // Test creating a user
        User user = new User();
        user.setId(1L);
        user.setFirstName("John");
        user.setLastName("Doe");
        user.setEmail("<EMAIL>");
        user.setEnabled(true);
        
        System.out.println("Created user: " + user.getFullName());
        
        // Test creating a task
        Task task = new Task();
        task.setId(1L);
        task.setTitle("Test Task");
        task.setDescription("Test Description");
        task.setUser(user);
        task.setCreatedBy(user);
        task.setActualDurationHours(25L);
        task.setRealizationDate(LocalDateTime.now());
        task.setCreatedAt(LocalDateTime.now().minusDays(5));
        
        System.out.println("Created task: " + task.getTitle());
        System.out.println("Assigned to: " + (task.getUser() != null ? task.getUser().getFullName() : "NULL"));
        System.out.println("Actual hours: " + task.getActualDurationHours());
        System.out.println("Realization date: " + task.getRealizationDate());
        
        // Test the query conditions
        boolean hasUser = task.getUser() != null;
        boolean hasActualHours = task.getActualDurationHours() != null;
        boolean isCompleted = task.getRealizationDate() != null;
        
        System.out.println("\nQuery conditions:");
        System.out.println("Has user: " + hasUser);
        System.out.println("Has actual hours: " + hasActualHours);
        System.out.println("Is completed: " + isCompleted);
        System.out.println("Would be included in query: " + (hasUser && hasActualHours));
    }
}
