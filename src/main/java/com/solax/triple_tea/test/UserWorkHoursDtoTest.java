package com.solax.triple_tea.test;

import com.solax.triple_tea.dto.UserWorkHoursDto;

/**
 * Simple test to verify UserWorkHoursDto functionality
 */
public class UserWorkHoursDtoTest {
    
    public static void main(String[] args) {
        // Test 1: Normal case
        UserWorkHoursDto dto1 = new UserWorkHoursDto("<PERSON>", "Do<PERSON>", 25L);
        System.out.println("Test 1 - Full name: " + dto1.getFullName());
        System.out.println("Test 1 - Total hours: " + dto1.getTotalHours());
        
        // Test 2: Null values
        UserWorkHoursDto dto2 = new UserWorkHoursDto(null, "Smith", 10L);
        System.out.println("Test 2 - Full name: " + dto2.getFullName());
        
        // Test 3: Empty constructor
        UserWorkHoursDto dto3 = new UserWorkHoursDto();
        dto3.setFirstName("Jane");
        dto3.setLastName("<PERSON>");
        dto3.setTotalHours(15L);
        System.out.println("Test 3 - Full name: " + dto3.getFullName());
        System.out.println("Test 3 - Total hours: " + dto3.getTotalHours());
        
        // Test 4: Edge cases
        UserWorkHoursDto dto4 = new UserWorkHoursDto("", "", 0L);
        System.out.println("Test 4 - Full name: '" + dto4.getFullName() + "'");
        
        System.out.println("All tests completed!");
    }
}
