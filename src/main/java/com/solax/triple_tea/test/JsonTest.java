package com.solax.triple_tea.test;

import java.util.Arrays;
import java.util.List;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.solax.triple_tea.dto.UserWorkHoursDto;

/**
 * Test JSON serialization for the chart data
 */
public class JsonTest {
    
    public static void main(String[] args) {
        // Create sample data
        List<UserWorkHoursDto> userWorkHours = Arrays.asList(
            new UserWorkHoursDto("<PERSON>", "Doe", 25L),
            new UserWorkHoursDto("<PERSON>", "<PERSON>", 30L),
            new UserWorkHoursDto("<PERSON>", "<PERSON>", 15L)
        );
        
        try {
            ObjectMapper mapper = new ObjectMapper();
            
            // Extract user names
            List<String> userNames = userWorkHours.stream()
                    .map(UserWorkHoursDto::getFullName)
                    .toList();
            
            // Extract work hours
            List<Long> workHours = userWorkHours.stream()
                    .map(UserWorkHoursDto::getTotalHours)
                    .toList();
            
            // Convert to JSON
            String userNamesJson = mapper.writeValueAsString(userNames);
            String workHoursJson = mapper.writeValueAsString(workHours);
            
            System.out.println("User Names JSON: " + userNamesJson);
            System.out.println("Work Hours JSON: " + workHoursJson);
            
            // Test in JavaScript-like format
            System.out.println("\nJavaScript format:");
            System.out.println("const userNames = " + userNamesJson + ";");
            System.out.println("const workHours = " + workHoursJson + ";");
            
        } catch (JsonProcessingException e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
