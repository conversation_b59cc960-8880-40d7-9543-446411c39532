package com.solax.triple_tea.controller;

import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.solax.triple_tea.dto.UserWorkHoursDto;
import com.solax.triple_tea.entity.Task;
import com.solax.triple_tea.service.TaskService;

@Controller
public class HomeController {

    private static final Logger logger = LoggerFactory.getLogger(HomeController.class);
    private final TaskService taskService;

    public HomeController(TaskService taskService) {
        this.taskService = taskService;
    }

    @GetMapping("/home")
    public String home(Model model) {
        model.addAttribute("tasks", taskService.findActiveByCurrentUser());
        return "home";
    }

    @GetMapping("/debug/tasks")
    @ResponseBody
    public String debugTasks() {
        StringBuilder sb = new StringBuilder();
        sb.append("=== DEBUG: Task Information ===\n");

        List<Task> allTasks = taskService.findAll();
        sb.append("Total tasks in database: ").append(allTasks.size()).append("\n\n");

        for (Task task : allTasks) {
            sb.append("Task ID: ").append(task.getId()).append("\n");
            sb.append("  Title: ").append(task.getTitle()).append("\n");
            sb.append("  User: ").append(task.getUser() != null ? task.getUser().getFullName() : "NULL").append("\n");
            sb.append("  Actual Hours: ").append(task.getActualDurationHours()).append("\n");
            sb.append("  Realization Date: ").append(task.getRealizationDate()).append("\n");
            sb.append("  Created By: ").append(task.getCreatedBy() != null ? task.getCreatedBy().getFullName() : "NULL").append("\n");
            sb.append("\n");
        }

        sb.append("=== Work Hours Statistics ===\n");
        List<UserWorkHoursDto> stats = taskService.getUserWorkHoursStatistics();
        sb.append("Statistics count: ").append(stats.size()).append("\n");

        for (UserWorkHoursDto dto : stats) {
            sb.append("User: ").append(dto.getFullName()).append(" - Hours: ").append(dto.getTotalHours()).append("\n");
        }

        return sb.toString().replace("\n", "<br>");
    }
}
