package com.solax.triple_tea.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.solax.triple_tea.entity.Contract;
import com.solax.triple_tea.service.ContractService;

@RestController
@RequestMapping("/api/contract")
public class ContractController {

    private final Logger log = LoggerFactory.getLogger(ContractController.class);

    private final ContractService entityService;

    public ContractController(ContractService entityService) {
        this.entityService = entityService;
    }

    @PostMapping()
    public ResponseEntity<Contract> createContract(@RequestBody Contract contract) {
        log.debug("REST request to save Contract : {}", contract);
        return new ResponseEntity<>(entityService.create(contract), HttpStatus.CREATED);
    }

    @PutMapping()
    public ResponseEntity<Contract> updateContract(@RequestBody Contract contract) {
        log.debug("REST request to update Contract : {}", contract);
        Contract result = entityService.update(contract);
        return ResponseEntity.ok().body(result);
    }

    @GetMapping()
    public ResponseEntity<List<Contract>> getAllContract() {
        log.debug("REST request to get all contracts");
        List<Contract> lst = entityService.getAll();

        return new ResponseEntity<>(lst, HttpStatus.OK);
    }

    @GetMapping(value = "/{id}")
    public ResponseEntity<Contract> getOneContract(@PathVariable("id") long id) {
        log.debug("REST request to get Contract : {}", id);
        Contract e = entityService.getOne(id);

        return new ResponseEntity<>(e, HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteContract(@PathVariable("id") long id) {
        log.debug("REST request to delete Contract : {}", id);
        entityService.delete(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
