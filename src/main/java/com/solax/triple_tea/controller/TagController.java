package com.solax.triple_tea.controller;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.solax.triple_tea.entity.Tag;
import com.solax.triple_tea.service.CustomerService;
import com.solax.triple_tea.service.TagService;

@Controller
@RequestMapping("/tags")
public class TagController {

    private final TagService tagService;
    private final CustomerService customerService;

    public TagController(TagService tagService, CustomerService customerService) {
        this.tagService = tagService;
        this.customerService = customerService;
    }

    @GetMapping
    public String list(Model model) {
        model.addAttribute("tags", tagService.findAll());
        return "tag/list";
    }

    @GetMapping("/new")
    public String createForm(Model model) {
        model.addAttribute("tag", new Tag());
        model.addAttribute("customers", customerService.findAll());
        return "tag/form";
    }

    @GetMapping("/edit/{id}")
    public String editForm(@PathVariable Long id, Model model) {
        Tag tag = tagService.findById(id);
        model.addAttribute("tag", tag);
        model.addAttribute("customers", customerService.findAll());
        model.addAttribute("selectedCustomers", tag.getCustomers());

        // Add all other tags for migration dropdown (excluding current tag)
        model.addAttribute("availableTags", tagService.findAll().stream()
            .filter(t -> !t.getId().equals(id))
            .toList());

        return "tag/form";
    }

    @PostMapping("/save")
    public String save(@ModelAttribute Tag tag) {
        tagService.save(tag);
        return "redirect:/tags";
    }

    @PostMapping("/migrate/{id}")
    public String migrateTag(@PathVariable Long id, @RequestParam Long targetTagId) {
        tagService.migrateTagToAnother(id, targetTagId);
        return "redirect:/tags";
    }

    @PostMapping("/delete/{id}")
    public String delete(@PathVariable Long id) {
        tagService.deleteById(id);
        return "redirect:/tags";
    }

    @GetMapping("/{id}/customers")
    public String viewTagCustomers(@PathVariable Long id, Model model) {
        Tag tag = tagService.findById(id);
        model.addAttribute("tag", tag);
        model.addAttribute("customers", tag.getCustomers());
        return "tag/list";
    }
}
