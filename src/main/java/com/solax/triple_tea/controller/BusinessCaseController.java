package com.solax.triple_tea.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.solax.triple_tea.service.BusinessCaseService;

@RestController
@RequestMapping("/business-case")
public class BusinessCaseController {

    private final Logger log = LoggerFactory.getLogger(BusinessCaseController.class);

    private final BusinessCaseService businessCaseService;

    public BusinessCaseController(BusinessCaseService businessCaseService) {
        this.businessCaseService = businessCaseService;
    }
}
