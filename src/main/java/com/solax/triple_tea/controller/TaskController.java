package com.solax.triple_tea.controller;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.solax.triple_tea.entity.Task;
import com.solax.triple_tea.service.CustomerService;
import com.solax.triple_tea.service.TaskService;
import com.solax.triple_tea.service.UserService;


@Controller
@RequestMapping("/tasks")
public class TaskController {

    private final TaskService taskService;

    private final UserService userService;

    private final CustomerService customerService;

    public TaskController(TaskService taskService, UserService userService, CustomerService customerService) {
        this.taskService = taskService;
        this.userService = userService;
        this.customerService = customerService;
    }

    @GetMapping
    public String list(Model model) {
        model.addAttribute("tasks", taskService.findNotArchived());
        return "task/list";
    }

    @GetMapping("/new")
    public String createForm(Model model) {
        Task task = new Task();
        task.setCreatedBy(userService.getCurrentUser());
        // Set the responsible user (from HistoryOverview) to current user by default
        task.setUser(userService.getCurrentUser());
        model.addAttribute("task", task);
        updateModelValues(model);
        return "task/form";
    }

    @GetMapping("/edit/{id}")
    public String editForm(@PathVariable Long id, Model model) {
        Task task = taskService.findById(id).orElseThrow(() -> new RuntimeException("Task not found"));
        model.addAttribute("task", task);
        updateModelValues(model);
        return "task/form";
    }

    private void updateModelValues(Model model) {
        model.addAttribute("users", userService.findEnabled());
        model.addAttribute("customers", customerService.findEnabled());
        model.addAttribute("categories", List.of("Technical", "Administrative", "Other"));
    }

    @PostMapping("/save")
    public String save(@ModelAttribute Task task) {
        taskService.save(task);
        return "redirect:/tasks";
    }

    @GetMapping("/delete/{id}")
    public String delete(@PathVariable Long id) {
        taskService.delete(id);
        return "redirect:/tasks";
    }

    @PostMapping("/{id}/start")
    public String startTask(@PathVariable Long id, Model model) {
        Task task = taskService.findById(id).orElseThrow(() -> new RuntimeException("Task not found"));
        task.setStartedAt(LocalDateTime.now());
        taskService.save(task);
        return "redirect:/home";
    }

    @PostMapping("/{id}/finish")
    public String finishTask(@PathVariable Long id, Model model) {
        Task task = taskService.findById(id).orElseThrow(() -> new RuntimeException("Task not found"));

        LocalDateTime finishTime = LocalDateTime.now();

        // If task is currently paused, add the current pause duration to total
        if (task.getIsCurrentlyPaused() != null && task.getIsCurrentlyPaused() && task.getPausedAt() != null) {
            long currentPauseDuration = Duration.between(task.getPausedAt(), finishTime).getSeconds();
            task.setTotalPausedDurationSeconds(task.getTotalPausedDurationSeconds() + currentPauseDuration);
            task.setIsCurrentlyPaused(false);
            task.setPausedAt(null);
        }

        // Set end time and realization date
        task.setEndedAt(finishTime);
        task.setRealizationDate(finishTime);

        // Calculate actual duration excluding paused time
        if (task.getStartedAt() != null) {
            Double actualHours = task.getTotalActiveDurationHours();
            task.setActualDurationHours(actualHours.longValue());
        }

        taskService.save(task);
        return "redirect:/home";
    }

    @PostMapping("/{id}/pause")
    public String pauseTask(@PathVariable Long id, Model model) {
        Task task = taskService.findById(id).orElseThrow(() -> new RuntimeException("Task not found"));

        if (task.getStartedAt() != null && task.getEndedAt() == null &&
            (task.getIsCurrentlyPaused() == null || !task.getIsCurrentlyPaused())) {
            task.setPausedAt(LocalDateTime.now());
            task.setIsCurrentlyPaused(true);
            taskService.save(task);
        }

        return "redirect:/home";
    }

    @PostMapping("/{id}/resume")
    public String resumeTask(@PathVariable Long id, Model model) {
        Task task = taskService.findById(id).orElseThrow(() -> new RuntimeException("Task not found"));

        if (task.getIsCurrentlyPaused() != null && task.getIsCurrentlyPaused() && task.getPausedAt() != null) {
            // Add the pause duration to total paused time
            long pauseDuration = Duration.between(task.getPausedAt(), LocalDateTime.now()).getSeconds();
            task.setTotalPausedDurationSeconds(task.getTotalPausedDurationSeconds() + pauseDuration);

            // Clear pause state
            task.setIsCurrentlyPaused(false);
            task.setPausedAt(null);

            taskService.save(task);
        }

        return "redirect:/home";
    }

    @PostMapping("/{id}/archive")
    public String archiveTask(@PathVariable Long id, Model model) {
        Task task = taskService.findById(id).orElseThrow(() -> new RuntimeException("Task not found"));
        task.setArchivedAt(LocalDateTime.now());
        taskService.save(task);
        return "redirect:/tasks";
    }
}
