package com.solax.triple_tea.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.solax.triple_tea.entity.BankAccount;
import com.solax.triple_tea.service.BankAccountService;

@RestController
@RequestMapping("/api/bankAccount")
public class BankAccountController {

    private final Logger log = LoggerFactory.getLogger(BankAccountController.class);
	
    private final BankAccountService entityService;

 	public BankAccountController (BankAccountService entityService) {
		this.entityService = entityService;
	}

	@PostMapping()
	public ResponseEntity<BankAccount> createBankAccount(@RequestBody BankAccount bankAccount) {
         log.debug("REST request to save BankAccount : {}", bankAccount);
         return new ResponseEntity<>(entityService.create(bankAccount), HttpStatus.CREATED);
    }

    @PutMapping()
    public ResponseEntity<BankAccount> updateBankAccount( @RequestBody BankAccount bankAccount) {
        log.debug("REST request to update BankAccount : {}", bankAccount);
        BankAccount result = entityService.update(bankAccount);
        return ResponseEntity.ok().body(result);
    }

    @GetMapping()
    public ResponseEntity<List<BankAccount>> getAllBankAccount() {
	    log.debug("REST request to get all bankAccounts");
        List<BankAccount> lst = entityService.getAll();

        return new ResponseEntity<>(lst,HttpStatus.OK);
    }

    @GetMapping(value = "/{id}")
    public ResponseEntity<BankAccount> getOneBankAccount(@PathVariable("id") long id) {
        log.debug("REST request to get BankAccount : {}", id);
        BankAccount e = entityService.getOne(id);

        return new ResponseEntity<>(e, HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteBankAccount(@PathVariable("id") long id) {
        log.debug("REST request to delete BankAccount : {}", id);
        entityService.delete(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
