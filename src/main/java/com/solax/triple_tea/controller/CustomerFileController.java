package com.solax.triple_tea.controller;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.solax.triple_tea.entity.Customer;
import com.solax.triple_tea.service.CustomerService;

import lombok.extern.slf4j.Slf4j;

@Controller
@RequestMapping("/customers/{customerId}/files")
@Slf4j
public class CustomerFileController {

    @Value("${app.file.upload.customer-files-dir}")
    private String customerFilesDir;

    private final CustomerService customerService;

    public CustomerFileController(CustomerService customerService) {
        this.customerService = customerService;
    }

    @GetMapping
    public String listFiles(@PathVariable Long customerId, Model model) {
        Customer customer = customerService.findById(customerId)
                .orElseThrow(() -> new RuntimeException("Customer not found"));

        model.addAttribute("customer", customer);
        
        List<CustomerFileInfo> files = getCustomerFiles(customerId);
        model.addAttribute("files", files);
        
        return "customer/files";
    }

    @PostMapping("/upload")
    public String uploadFile(@PathVariable Long customerId,
                           @RequestParam("file") MultipartFile file,
                           @RequestParam(value = "description", required = false) String description,
                           RedirectAttributes redirectAttributes) {
        
        if (file.isEmpty()) {
            redirectAttributes.addFlashAttribute("error", "Prosím vyberte súbor na nahranie.");
            return "redirect:/customers/" + customerId + "/files";
        }

        try {
            // Create customer directory if it doesn't exist
            Path customerDir = Paths.get(customerFilesDir, customerId.toString());
            Files.createDirectories(customerDir);

            // Generate unique filename with timestamp
            String originalFilename = file.getOriginalFilename();
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String filename = timestamp + "_" + originalFilename;
            
            // Save file
            Path targetPath = customerDir.resolve(filename);
            Files.copy(file.getInputStream(), targetPath, StandardCopyOption.REPLACE_EXISTING);

            log.info("File uploaded successfully: {} for customer {}", filename, customerId);
            redirectAttributes.addFlashAttribute("success", 
                "Súbor '" + originalFilename + "' bol úspešne nahraný.");

        } catch (IOException e) {
            log.error("Error uploading file for customer {}: {}", customerId, e.getMessage());
            redirectAttributes.addFlashAttribute("error", 
                "Chyba pri nahrávaní súboru: " + e.getMessage());
        }

        return "redirect:/customers/" + customerId + "/files";
    }

    @GetMapping("/download/{filename}")
    public ResponseEntity<Resource> downloadFile(@PathVariable Long customerId,
                                               @PathVariable String filename) {
        try {
            Path filePath = Paths.get(customerFilesDir, customerId.toString(), filename);
            Resource resource = new UrlResource(filePath.toUri());

            if (resource.exists() && resource.isReadable()) {
                // Determine content type
                String contentType = Files.probeContentType(filePath);
                if (contentType == null) {
                    contentType = "application/octet-stream";
                }

                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType(contentType))
                        .header(HttpHeaders.CONTENT_DISPOSITION, 
                               "attachment; filename=\"" + getOriginalFilename(filename) + "\"")
                        .body(resource);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("Error downloading file {} for customer {}: {}", filename, customerId, e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    @PostMapping("/delete/{filename}")
    public String deleteFile(@PathVariable Long customerId,
                           @PathVariable String filename,
                           RedirectAttributes redirectAttributes) {
        try {
            Path filePath = Paths.get(customerFilesDir, customerId.toString(), filename);
            
            if (Files.exists(filePath)) {
                Files.delete(filePath);
                log.info("File deleted successfully: {} for customer {}", filename, customerId);
                redirectAttributes.addFlashAttribute("success", 
                    "Súbor '" + getOriginalFilename(filename) + "' bol úspešne vymazaný.");
            } else {
                redirectAttributes.addFlashAttribute("error", "Súbor nebol nájdený.");
            }
        } catch (IOException e) {
            log.error("Error deleting file {} for customer {}: {}", filename, customerId, e.getMessage());
            redirectAttributes.addFlashAttribute("error", 
                "Chyba pri mazaní súboru: " + e.getMessage());
        }

        return "redirect:/customers/" + customerId + "/files";
    }

    private List<CustomerFileInfo> getCustomerFiles(Long customerId) {
        List<CustomerFileInfo> files = new ArrayList<>();
        Path customerDir = Paths.get(customerFilesDir, customerId.toString());

        if (Files.exists(customerDir)) {
            try (Stream<Path> paths = Files.list(customerDir)) {
                paths.filter(Files::isRegularFile)
                     .forEach(path -> {
                         try {
                             String filename = path.getFileName().toString();
                             CustomerFileInfo fileInfo = new CustomerFileInfo();
                             fileInfo.setFilename(filename);
                             fileInfo.setOriginalFilename(getOriginalFilename(filename));
                             fileInfo.setSize(Files.size(path));
                             fileInfo.setLastModified(Files.getLastModifiedTime(path).toInstant());
                             files.add(fileInfo);
                         } catch (IOException e) {
                             log.warn("Error reading file info for {}: {}", path, e.getMessage());
                         }
                     });
            } catch (IOException e) {
                log.error("Error listing files for customer {}: {}", customerId, e.getMessage());
            }
        }

        // Sort by last modified date (newest first)
        files.sort((a, b) -> b.getLastModified().compareTo(a.getLastModified()));
        return files;
    }

    private String getOriginalFilename(String timestampedFilename) {
        // Remove timestamp prefix (format: yyyyMMdd_HHmmss_originalname)
        if (timestampedFilename.matches("\\d{8}_\\d{6}_.*")) {
            return timestampedFilename.substring(16); // Remove "yyyyMMdd_HHmmss_" prefix
        }
        return timestampedFilename;
    }

    // Inner class for file information
    public static class CustomerFileInfo {
        private String filename;
        private String originalFilename;
        private long size;
        private java.time.Instant lastModified;

        // Getters and setters
        public String getFilename() { return filename; }
        public void setFilename(String filename) { this.filename = filename; }
        
        public String getOriginalFilename() { return originalFilename; }
        public void setOriginalFilename(String originalFilename) { this.originalFilename = originalFilename; }
        
        public long getSize() { return size; }
        public void setSize(long size) { this.size = size; }
        
        public java.time.Instant getLastModified() { return lastModified; }
        public void setLastModified(java.time.Instant lastModified) { this.lastModified = lastModified; }
        
        public String getFormattedSize() {
            if (size < 1024) return size + " B";
            if (size < 1024 * 1024) return String.format("%.1f KB", size / 1024.0);
            if (size < 1024 * 1024 * 1024) return String.format("%.1f MB", size / (1024.0 * 1024.0));
            return String.format("%.1f GB", size / (1024.0 * 1024.0 * 1024.0));
        }
    }
}
