package com.solax.triple_tea.controller;

import java.util.Comparator;
import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.solax.triple_tea.entity.Address;
import com.solax.triple_tea.entity.Customer;
import com.solax.triple_tea.entity.Tag;
import com.solax.triple_tea.entity.User;
import com.solax.triple_tea.enums.CompanyOrIndividual;
import com.solax.triple_tea.service.CustomerService;
import com.solax.triple_tea.service.CustomerSubtypeService;
import com.solax.triple_tea.service.CustomerTypeService;
import com.solax.triple_tea.service.TagService;
import com.solax.triple_tea.service.UserService;

@Controller
@RequestMapping("/customers")
public class CustomerController {

    private final CustomerService customerService;
    private final CustomerTypeService customerTypeService;
    private final CustomerSubtypeService customerSubtypeService;
    private final UserService userService;
    private final TagService tagService;

    public CustomerController(CustomerService customerService, CustomerTypeService customerTypeService,
                              CustomerSubtypeService customerSubtypeService, UserService userService, TagService tagService) {
        this.customerService = customerService;
        this.customerTypeService = customerTypeService;
        this.customerSubtypeService = customerSubtypeService;
        this.userService = userService;
        this.tagService = tagService;
    }

    @GetMapping
    public String list(Model model,
                      @RequestParam(defaultValue = "0") int page,
                      @RequestParam(defaultValue = "20") int size,
                      @RequestParam(defaultValue = "name") String sortBy,
                      @RequestParam(defaultValue = "asc") String sortDir,
                      @RequestParam(required = false) String nameFilter,
                      @RequestParam(required = false) String icoFilter,
                      @RequestParam(required = false) String cityFilter,
                      @RequestParam(required = false) String countryFilter,
                      @RequestParam(required = false) String typeFilter,
                      @RequestParam(required = false) String subtypeFilter,
                      @RequestParam(required = false) List<Long> tagFilters,
                      @RequestParam(required = false) Long responsiblePersonFilter) {

        Sort sort = sortDir.equalsIgnoreCase("desc") ?
            Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);

        Page<Customer> customerPage = customerService.findAllWithFilters(
            pageable, nameFilter, icoFilter, cityFilter, countryFilter, typeFilter, subtypeFilter, tagFilters, responsiblePersonFilter);

        model.addAttribute("customers", customerPage);
        model.addAttribute("currentPage", page);
        model.addAttribute("totalPages", customerPage.getTotalPages());
        model.addAttribute("totalElements", customerPage.getTotalElements());
        model.addAttribute("size", size);
        model.addAttribute("sortBy", sortBy);
        model.addAttribute("sortDir", sortDir);
        model.addAttribute("nameFilter", nameFilter);
        model.addAttribute("icoFilter", icoFilter);
        model.addAttribute("cityFilter", cityFilter);
        model.addAttribute("countryFilter", countryFilter);
        model.addAttribute("typeFilter", typeFilter);
        model.addAttribute("subtypeFilter", subtypeFilter);
        model.addAttribute("tagFilters", tagFilters);
        model.addAttribute("responsiblePersonFilter", responsiblePersonFilter);
        model.addAttribute("tags", tagService.findAll().stream().sorted(Comparator.comparing(Tag::getName)).toList());
        model.addAttribute("users", userService.findEnabled().stream().sorted(Comparator.comparing(User::getFullName)).toList());

        return "customer/list";
    }

    @GetMapping("/new")
    public String createForm(Model model) {
        Customer customer = new Customer();
        customer.setBillingAddress(new Address());
        customer.setCorrespondenceAddress(new Address());
        customer.setDeliveryAddress(new Address());
        model.addAttribute("customer", customer);
        updateModelValues(model);

        return "customer/form";
    }

    @GetMapping("/edit/{id}")
    public String editForm(@PathVariable Long id, Model model) {
        Customer customer = customerService.findById(id).orElseThrow();

        if (customer.getBillingAddress() == null) {
            customer.setBillingAddress(new Address());
        }
        if (customer.getDeliveryAddress() == null) {
            customer.setDeliveryAddress(new Address());
        }
        if (customer.getCorrespondenceAddress() == null) {
            customer.setCorrespondenceAddress(new Address());
        }

        model.addAttribute("customer", customer);
        updateModelValues(model);
        return "customer/form";
    }

    private void updateModelValues(Model model) {
        model.addAttribute("customerTypes", customerTypeService.findAll());
        model.addAttribute("users", userService.findAll());
        model.addAttribute("tags", tagService.findAll().stream().sorted(Comparator.comparing(Tag::getName)).toList());
        model.addAttribute("customerTypes", customerTypeService.findAll());
        model.addAttribute("customerSubtypes", customerSubtypeService.findAll());
        model.addAttribute("companyOrIndividuals", CompanyOrIndividual.values());
    }

    @PostMapping("/save")
    public String save(@ModelAttribute Customer customer) {
        customer.setName(customer.getName());
        customerService.save(customer);
        return "redirect:/customers";
    }

    @GetMapping("/delete/{id}")
    public String delete(@PathVariable Long id) {
        customerService.deleteById(id);
        return "redirect:/customers";
    }
}
