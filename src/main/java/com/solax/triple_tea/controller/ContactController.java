package com.solax.triple_tea.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.solax.triple_tea.entity.Contact;
import com.solax.triple_tea.service.ContactService;


@RestController
@RequestMapping("/api/contact")
public class ContactController {

    private final Logger log = LoggerFactory.getLogger(ContactController.class);
	
    private final ContactService entityService;

 	public ContactController (ContactService entityService) {
		this.entityService = entityService;
	}

    /**
     * {@code POST  /contact} : Create a new contact.
     *
     * @param contact the contact to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new contact.
     */
	@PostMapping()
	public ResponseEntity<Contact> createContact(@RequestBody Contact contact) {
         log.debug("REST request to save Contact : {}", contact);
         return new ResponseEntity<>(entityService.create(contact), HttpStatus.CREATED);
    }

   /**
     * {@code PUT  /contact} : Updates an existing contact.
     *
     * @param contact the contact to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated contact,
     * or with status {@code 400 (Bad Request)} if the contact is not valid,
     * or with status {@code 500 (Internal Server Error)} if the contact couldn't be updated.
     */
    @PutMapping()
    public ResponseEntity<Contact> updateContact(@RequestBody Contact contact) {
        log.debug("REST request to update Contact : {}", contact);
        Contact result = entityService.update(contact);
        return ResponseEntity.ok().body(result);
    }

    /**
     * {@code GET  /contact} : get all the contacts.
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of contact in body.
     */

    @GetMapping()
    public ResponseEntity<List<Contact>> getAllContact() {
	    log.debug("REST request to get all contacts");
        List<Contact> lst = entityService.getAll();

        return new ResponseEntity<>(lst,HttpStatus.OK);
    }

    /**
     * {@code GET  /contact/:id} : get the "id" contact.
     *
     * @param id the id of the contact to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the contact, or with status {@code 404 (Not Found)}.
     */
    @GetMapping(value = "/{id}")
    public ResponseEntity<Contact> getOneContact(@PathVariable("id") long id) {
        log.debug("REST request to get Contact : {}", id);
        Contact e = entityService.getOne(id);

        return new ResponseEntity<>(e, HttpStatus.OK);
    }

  /**
     * {@code DELETE  /contact/:id} : delete the "id" contact.
     *
     * @param id the id of the contact to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteContact(@PathVariable("id") long id) {
        log.debug("REST request to delete Contact : {}", id);
        entityService.delete(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

}
