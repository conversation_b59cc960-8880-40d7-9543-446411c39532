package com.solax.triple_tea.controller;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.solax.triple_tea.entity.Address;
import com.solax.triple_tea.service.AddressService;

@Controller
@RequestMapping("/addresses")
public class AddressController {

    private final AddressService addressService;

 	public AddressController (AddressService addressService) {
		this.addressService = addressService;
	}

    @GetMapping
    public String listAddresses(Model model) {
        model.addAttribute("addresses", addressService.findAll());
        return "address/list";
    }

    @GetMapping("/new")
    public String showCreateForm(Model model) {
        model.addAttribute("address", new Address());
        return "address/form";
    }

    @PostMapping("/save")
    public String saveAddress(@ModelAttribute("address") Address address, BindingResult bindingResult, RedirectAttributes redirectAttributes) {
        if (bindingResult.hasErrors()) {
            return "address/form";
        }

        addressService.save(address);
        redirectAttributes.addFlashAttribute("message", "Address saved successfully!");
        return "redirect:/address";
    }

    @GetMapping("/edit/{id}")
    public String showEditForm(@PathVariable Long id, Model model) {
        model.addAttribute("address", addressService.findById(id).orElse(new Address()));
        return "address/form";
    }

    @DeleteMapping("/delete/{id}")
    public String deleteAddress(@PathVariable("id") long id) {
        addressService.delete(id);
        return "redirect:/address";
    }
}
