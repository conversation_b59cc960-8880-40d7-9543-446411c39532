package com.solax.triple_tea.controller;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import com.solax.triple_tea.entity.Invoice;
import com.solax.triple_tea.entity.ProgressStatus;
import com.solax.triple_tea.service.CustomerService;
import com.solax.triple_tea.service.InvoiceService;
import com.solax.triple_tea.service.UserService;

@Controller
@RequestMapping("/invoices")
public class InvoiceController {

    private final InvoiceService invoiceService;
    private final UserService userService;
    private final CustomerService customerService;

    public InvoiceController(InvoiceService invoiceService, UserService userService, CustomerService customerService) {
        this.invoiceService = invoiceService;
        this.userService = userService;
        this.customerService = customerService;
    }

    @GetMapping
    public String list(Model model) {
        model.addAttribute("invoices", invoiceService.getAll());
        return "invoice/list";
    }

    @GetMapping("/new")
    public String createForm(Model model) {
        Invoice invoice = new Invoice();
        // Set the responsible user from HistoryOverview to current user by default
        invoice.setUser(userService.getCurrentUser());
        // Set default status to DRAFT
        invoice.setStatus(ProgressStatus.DRAFT);
        model.addAttribute("invoice", invoice);
        updateModelValues(model);
        return "invoice/form";
    }

    @GetMapping("/new/customer/{customerId}")
    public String createFormForCustomer(@PathVariable Long customerId, Model model) {
        Invoice invoice = new Invoice();
        // Set the responsible user from HistoryOverview to current user by default
        invoice.setUser(userService.getCurrentUser());
        // Set default status to DRAFT
        invoice.setStatus(ProgressStatus.DRAFT);

        // Set the customer
        invoice.setCustomer(customerService.findById(customerId)
                .orElseThrow(() -> new IllegalArgumentException("Invalid customer ID: " + customerId)));

        model.addAttribute("invoice", invoice);
        model.addAttribute("currentCustomer", invoice.getCustomer());
        updateModelValues(model);
        return "invoice/form";
    }

    @GetMapping("/edit/{id}")
    public String editForm(@PathVariable Long id, Model model) {
        Invoice invoice = invoiceService.getOne(id);
        model.addAttribute("invoice", invoice);
        updateModelValues(model);
        return "invoice/form";
    }

    @PostMapping("/save")
    public String save(@ModelAttribute Invoice invoice) {
        if (invoice.getId() == null) {
            invoiceService.create(invoice);
        } else {
            invoiceService.update(invoice);
        }
        return "redirect:/invoices";
    }

    private void updateModelValues(Model model) {
        model.addAttribute("users", userService.findAll());
        model.addAttribute("customers", customerService.findAll());
        // Only include invoice-specific statuses
        model.addAttribute("statuses", java.util.Arrays.asList(
            ProgressStatus.DRAFT,
            ProgressStatus.SENT,
            ProgressStatus.PAID,
            ProgressStatus.OVERDUE,
            ProgressStatus.CANCELLED
        ));
    }

    @GetMapping("/delete/{id}")
    public String delete(@PathVariable Long id) {
        invoiceService.delete(id);
        return "redirect:/invoices";
    }
}
