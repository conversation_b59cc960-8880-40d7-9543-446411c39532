
package com.solax.triple_tea.controller;


import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.solax.triple_tea.entity.CustomerType;
import com.solax.triple_tea.service.CustomerTypeService;

@Controller
@RequestMapping("/customer-types")
public class CustomerTypeController {

    private final CustomerTypeService customerTypeService;

    public CustomerTypeController(CustomerTypeService customerTypeService) {
        this.customerTypeService = customerTypeService;
    }

    @GetMapping
    public String listCustomerTypes(Model model) {
        model.addAttribute("customerTypes", customerTypeService.getAll());
        return "customertype/list";
    }

    @GetMapping("/new")
    public String showCreateForm(Model model) {
        model.addAttribute("customerType", new CustomerType());
        return "customertype/form";
    }

    @PostMapping("/save")
    public String saveCustomerType(@ModelAttribute("customerType") CustomerType customerType,
                                   BindingResult bindingResult,
                                   RedirectAttributes redirectAttributes) {
        if (bindingResult.hasErrors()) {
            return "customertype/form";
        }

        customerTypeService.save(customerType);
        redirectAttributes.addFlashAttribute("message", "Customer Type saved successfully!");
        return "redirect:/customer-type";
    }

    @GetMapping("/edit/{id}")
    public String showEditForm(@PathVariable Long id, Model model) {
        model.addAttribute("customerType", customerTypeService.getOne(id));
        return "customertype/form";
    }

    @DeleteMapping("/delete/{id}")
    public String deleteCustomerType(@PathVariable("id") long id) {
        customerTypeService.delete(id);
        return "redirect:/customer-type";
    }
}
