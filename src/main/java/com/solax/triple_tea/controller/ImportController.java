package com.solax.triple_tea.controller;


import org.springframework.beans.factory.annotation.Value;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.solax.triple_tea.xlsx.Parser;


@RestController
@RequestMapping("/import")
public class ImportController {

    @Value("${import.path}")
    private String importPath;
    
    private final Parser parser;

    public ImportController(final Parser parser) {
        this.parser = parser;
    }

    @GetMapping("/users")
    public String users(Model model) {
        parser.parseUsers(importPath + "uzivatelia.xlsx");
        return "user/form";
    }

    @GetMapping("/partners")
    public String partners(Model model) {
        parser.parsePartners(importPath + "partneri.xlsx");
        return "user/form";
    }

    @GetMapping("/products")
    public String products(Model model) {
        parser.parseProducts(importPath + "moj_cennik.xlsx");
        return "user/form";
    }

    @GetMapping("/documents")
    public String documents(Model model) {
        parser.parseDocuments(importPath + "zoznam_dokladov.xlsx");
        return "user/form";
    }

    @GetMapping("/business-cases")
    public String businessCases(Model model) {
        parser.parseBusinessCases(importPath + "zoznam_obchodnych_pripadov.xlsx");
        return "user/form";
    }

    @GetMapping("/events")
    public String events(Model model) {
        parser.parseEvents(importPath + "zoznam_udalosti.xlsx");
        return "user/form";
    }

    @GetMapping("/tasks")
    public String tasks(Model model) {
        parser.parseTasks(importPath + "20250411_zoznam_uloh.xlsx");
        return "user/form";
    }

    @GetMapping("/contracts")
    public String contracts(Model model) {
        parser.parseContracts(importPath + "20250411_zoznam_zakaziek.xlsx");
        return "user/form";
    }
}
