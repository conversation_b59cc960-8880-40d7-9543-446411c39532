package com.solax.triple_tea.controller;

import java.util.List;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import com.solax.triple_tea.dto.HistoryItemDto;
import com.solax.triple_tea.entity.Customer;
import com.solax.triple_tea.service.CustomerService;
import com.solax.triple_tea.service.HistoryService;

@Controller
@RequestMapping("/history")
public class HistoryController {

    private final HistoryService historyService;
    private final CustomerService customerService;

    public HistoryController(HistoryService historyService, CustomerService customerService) {
        this.historyService = historyService;
        this.customerService = customerService;
    }

    @GetMapping
    public String allHistory(Model model) {
        List<HistoryItemDto> historyItems = historyService.getAllHistory();
        model.addAttribute("historyItems", historyItems);
        model.addAttribute("pageTitle", "All History");
        return "history/list";
    }

    @GetMapping("/customer/{customerId}")
    public String customerHistory(@PathVariable Long customerId, Model model) {
        Customer customer = customerService.findById(customerId)
                .orElseThrow(() -> new IllegalArgumentException("Invalid customer ID: " + customerId));
        
        List<HistoryItemDto> historyItems = historyService.getCustomerHistory(customer);
        model.addAttribute("historyItems", historyItems);
        model.addAttribute("customer", customer);
        model.addAttribute("pageTitle", "History for " + customer.getName());
        return "history/list";
    }
}
