package com.solax.triple_tea.controller;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.solax.triple_tea.entity.BusinessEvent;
import com.solax.triple_tea.entity.Customer;
import com.solax.triple_tea.repository.EventCategoryRepository;
import com.solax.triple_tea.repository.EventTypeRepository;
import com.solax.triple_tea.service.BusinessCaseService;
import com.solax.triple_tea.service.BusinessEventService;
import com.solax.triple_tea.service.CustomerService;
import com.solax.triple_tea.service.UserService;

@Controller
@RequestMapping("/business-events")
public class BusinessEventController {

    private final BusinessEventService businessEventService;
    private final CustomerService customerService;
    private final UserService userService;
    private final BusinessCaseService businessCaseService;
    private final EventTypeRepository eventTypeRepository;
    private final EventCategoryRepository eventCategoryRepository;

    public BusinessEventController(BusinessEventService businessEventService, CustomerService customerService,
                                   UserService userService, BusinessCaseService businessCaseService,
                                   EventTypeRepository eventTypeRepository, EventCategoryRepository eventCategoryRepository) {
        this.businessEventService = businessEventService;
        this.customerService = customerService;
        this.userService = userService;
        this.businessCaseService = businessCaseService;
        this.eventTypeRepository = eventTypeRepository;
        this.eventCategoryRepository = eventCategoryRepository;
    }

    @GetMapping("/customer/{customerId}")
    public String customersBbusinessEventsList(@PathVariable Long customerId, Model model) {
        Customer customer = customerService.findById(customerId).orElseThrow();
        model.addAttribute("customer", customer);
        model.addAttribute("businessEvents", businessEventService.findAllByCustomer(customer));
        return "business-event/list";
    }

    @GetMapping("/new/customer/{customerId}")
    public String createForm(@PathVariable Long customerId, Model model) {
        model.addAttribute("event", new BusinessEvent());
        model.addAttribute("businessCases", businessCaseService.findAll());

        if (customerId != null) {
            model.addAttribute("currentCustomer", customerService.findById(customerId));
        }

        updateModelValues(model);

        return "business-event/form";
    }

    @GetMapping("/edit/{id}")
    public String editForm(@PathVariable Long id, Model model) {
        BusinessEvent businessEvent = businessEventService.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("Invalid business event ID: " + id));
        model.addAttribute("event", businessEvent);
        
        if (businessEvent.getCustomer() != null) {
            model.addAttribute("currentCustomer", businessEvent.getCustomer());
        }
        
        updateModelValues(model);
        return "business-event/form";
    }

    private void updateModelValues(Model model) {
        model.addAttribute("eventTypes", eventTypeRepository.findAll());
        model.addAttribute("eventCategories", eventCategoryRepository.findAll());
        model.addAttribute("customers", customerService.findAll());
        model.addAttribute("users", userService.findAll());
        model.addAttribute("businessCases", businessCaseService.findAll());
    }

    @PostMapping("/save")
    public String save(@ModelAttribute BusinessEvent businessEvent) {
        businessEventService.save(businessEvent);
        return "redirect:/business-events/customer/" + businessEvent.getCustomer().getId();
    }

    @PostMapping("/delete/{id}")
    public String delete(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        BusinessEvent businessEvent = businessEventService.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("Invalid business event ID: " + id));
        
        Long customerId = businessEvent.getCustomer() != null ? businessEvent.getCustomer().getId() : null;
        
        businessEventService.deleteById(id);
        
        redirectAttributes.addFlashAttribute("successMessage", "Business event successfully deleted");
        
        if (customerId != null) {
            return "redirect:/business-events/customer/" + customerId;
        } else {
            return "redirect:/business-events";
        }
    }
}
