package com.solax.triple_tea.controller;

import org.springframework.context.MessageSource;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.time.ZoneId;
import java.util.Date;
import java.util.Locale;

import com.solax.triple_tea.entity.Customer;
import com.solax.triple_tea.entity.ProgressStatus;
import com.solax.triple_tea.entity.Quote;
import com.solax.triple_tea.service.CustomerService;
import com.solax.triple_tea.service.QuoteService;
import com.solax.triple_tea.service.UserService;

@Controller
@RequestMapping("/quotes")
public class QuoteController {

    private final QuoteService quoteService;
    private final UserService userService;
    private final CustomerService customerService;
    private final MessageSource messageSource;

    public QuoteController(QuoteService quoteService, UserService userService, CustomerService customerService, MessageSource messageSource) {
        this.quoteService = quoteService;
        this.userService = userService;
        this.customerService = customerService;
        this.messageSource = messageSource;
    }

    @GetMapping
    public String list(Model model) {
        model.addAttribute("quotes", quoteService.getAll());
        return "quote/list";
    }

    @GetMapping("/new")
    public String createForm(Model model) {
        Quote quote = new Quote();
        // Set the responsible user from HistoryOverview to current user by default
        quote.setUser(userService.getCurrentUser());
        // Set default status to DRAFT
        quote.setStatus(ProgressStatus.DRAFT);
        // Set default currency to EUR
        quote.setCurrency("EUR");
        model.addAttribute("quote", quote);
        updateModelValues(model);
        return "quote/form";
    }

    @GetMapping("/new/customer/{customerId}")
    public String createFormForCustomer(@PathVariable Long customerId, Model model) {
        Quote quote = new Quote();
        // Set the responsible user from HistoryOverview to current user by default
        quote.setUser(userService.getCurrentUser());
        // Set default status to DRAFT
        quote.setStatus(ProgressStatus.DRAFT);
        // Set default currency to EUR
        quote.setCurrency("EUR");

        // Set the customer
        quote.setCustomer(customerService.findById(customerId)
                .orElseThrow(() -> new IllegalArgumentException("Invalid customer ID: " + customerId)));

        model.addAttribute("quote", quote);
        model.addAttribute("currentCustomer", quote.getCustomer());
        updateModelValues(model);
        return "quote/form";
    }

    @GetMapping("/edit/{id}")
    public String editForm(@PathVariable Long id, Model model) {
        Quote quote = quoteService.getOne(id);
        model.addAttribute("quote", quote);
        updateModelValues(model);
        return "quote/form";
    }

    @PostMapping("/save")
    public String save(@ModelAttribute Quote quote) {
        Quote savedQuote;
        if (quote.getId() == null) {
            savedQuote = quoteService.create(quote);
        } else {
            savedQuote = quoteService.update(quote);
        }

        // Redirect to customer quotes if quote has a customer
        if (savedQuote != null && savedQuote.getCustomer() != null) {
            return "redirect:/quotes/customer/" + savedQuote.getCustomer().getId();
        }

        return "redirect:/quotes";
    }

    private void updateModelValues(Model model) {
        model.addAttribute("users", userService.findAll());
        model.addAttribute("customers", customerService.findAll());
        // Only include quote-specific statuses
        model.addAttribute("statuses", java.util.Arrays.asList(
            ProgressStatus.DRAFT,
            ProgressStatus.SENT,
            ProgressStatus.ACCEPTED,
            ProgressStatus.REJECTED,
            ProgressStatus.EXPIRED,
            ProgressStatus.CANCELLED
        ));
        // Add currencies
        model.addAttribute("currencies", java.util.Arrays.asList(
            "EUR", "USD", "GBP", "CZK", "PLN", "HUF"
        ));
    }

    @GetMapping("/customer/{customerId}")
    public String customerQuotes(@PathVariable Long customerId, Model model) {
        Customer customer = customerService.findById(customerId)
                .orElseThrow(() -> new IllegalArgumentException("Invalid customer ID: " + customerId));

        model.addAttribute("quotes", quoteService.findAllByCustomer(customer));
        model.addAttribute("customer", customer);
        return "quote/customer-list";
    }

    @GetMapping("/preview/{id}")
    public String previewQuote(@PathVariable Long id, Model model, Locale locale) {
        Quote quote = quoteService.getOne(id);
        if (quote == null) {
            throw new IllegalArgumentException("Quote not found with ID: " + id);
        }

        // Add quote data to model for template
        model.addAttribute("quote", quote);
        model.addAttribute("customer", quote.getCustomer());
        model.addAttribute("user", quote.getUser()); // Responsible user
        model.addAttribute("createdBy", quote.getCreatedBy()); // User who created the quote
        model.addAttribute("issueDateFormatted", Date.from(quote.getIssueDate().atStartOfDay(ZoneId.systemDefault()).toInstant()));

        // Translate status if available
        if (quote.getStatus() != null) {
            String statusKey = "status." + quote.getStatus().name().toLowerCase();
            String translatedStatus = messageSource.getMessage(statusKey, null, quote.getStatus().name(), locale);
            model.addAttribute("translatedStatus", translatedStatus);
        } else {
            model.addAttribute("translatedStatus", "-");
        }

        // Return the HTML template view
        return "quote/preview";
    }

    @GetMapping("/delete/{id}")
    public String delete(@PathVariable Long id) {
        quoteService.delete(id);
        return "redirect:/quotes";
    }
}
