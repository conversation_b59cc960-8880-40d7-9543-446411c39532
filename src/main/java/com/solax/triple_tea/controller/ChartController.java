package com.solax.triple_tea.controller;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.WeekFields;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.solax.triple_tea.dto.UserCustomerWorkHoursDto;
import com.solax.triple_tea.dto.UserWorkHoursDto;
import com.solax.triple_tea.dto.WeeklyWorkHoursDto;
import com.solax.triple_tea.entity.User;
import com.solax.triple_tea.service.TaskService;
import com.solax.triple_tea.service.UserService;

@RestController
@RequestMapping("/api/charts")
public class ChartController {

    private static final Logger logger = LoggerFactory.getLogger(ChartController.class);
    private final TaskService taskService;
    private final UserService userService;

    public ChartController(TaskService taskService, UserService userService) {
        this.taskService = taskService;
        this.userService = userService;
    }

    @GetMapping("/user-work-hours")
    public Map<String, Object> getUserWorkHoursData() {
        logger.info("Fetching user work hours data for chart");
        
        List<UserWorkHoursDto> userWorkHours = taskService.getUserWorkHoursStatistics();
        logger.info("Retrieved {} user work hours records", userWorkHours.size());

        // Extract user names and work hours
        List<String> userNames = userWorkHours.stream()
                .map(dto -> dto.getFullName() != null ? dto.getFullName() : "Unknown User")
                .collect(Collectors.toList());
        
        List<Long> workHours = userWorkHours.stream()
                .map(dto -> dto.getTotalHours() != null ? dto.getTotalHours() : 0L)
                .collect(Collectors.toList());

        // Create response map
        Map<String, Object> response = new HashMap<>();
        response.put("userNames", userNames);
        response.put("workHours", workHours);
        response.put("success", true);
        response.put("message", "Data retrieved successfully");

        logger.info("Returning chart data: {} users", userNames.size());
        return response;
    }

    @GetMapping("/weekly-work-hours")
    public Map<String, Object> getWeeklyWorkHoursData(@RequestParam(required = false) String weekStart) {
        logger.info("Fetching weekly work hours data for week: {}", weekStart);

        // Check current user permissions
        User currentUser = userService.getCurrentUser();
        if (currentUser == null) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "User not authenticated");
            return errorResponse;
        }

        boolean canViewAllUsers = currentUser.isCanViewAllWorkHours();
        logger.info("User {} can view all work hours: {}", currentUser.getFullName(), canViewAllUsers);

        // Parse the week start date or default to current week
        LocalDate targetWeekStart;
        if (weekStart != null && !weekStart.isEmpty()) {
            try {
                targetWeekStart = LocalDate.parse(weekStart, DateTimeFormatter.ISO_LOCAL_DATE);
            } catch (Exception e) {
                logger.warn("Invalid date format: {}, using current week", weekStart);
                targetWeekStart = getCurrentWeekStart();
            }
        } else {
            targetWeekStart = getCurrentWeekStart();
        }

        // Ensure we start from Monday
        targetWeekStart = getWeekStart(targetWeekStart);

        // Get data based on user permissions
        List<WeeklyWorkHoursDto> weeklyWorkHours;
        if (canViewAllUsers) {
            weeklyWorkHours = taskService.getWeeklyWorkHoursStatistics(targetWeekStart);
            logger.info("Retrieved {} weekly work hours records for all users for week {}", weeklyWorkHours.size(), targetWeekStart);
        } else {
            weeklyWorkHours = taskService.getWeeklyWorkHoursStatisticsForCurrentUser(targetWeekStart);
            logger.info("Retrieved {} weekly work hours records for current user only for week {}", weeklyWorkHours.size(), targetWeekStart);
        }

        // Create datasets for bar chart (each user will be a separate dataset for toggle functionality)
        List<Map<String, Object>> datasets = new ArrayList<>();
        List<String> userNames = new ArrayList<>();
        List<Long> workHours = new ArrayList<>();

        // Extract user names and work hours for labels and data
        for (WeeklyWorkHoursDto dto : weeklyWorkHours) {
            String userName = dto.getFullName() != null ? dto.getFullName() : "Unknown User";
            Long hours = dto.getTotalHours() != null ? dto.getTotalHours() : 0L;

            userNames.add(userName);
            workHours.add(hours);
        }

        // Create individual datasets for each user (for toggle functionality)
        for (int i = 0; i < weeklyWorkHours.size(); i++) {
            WeeklyWorkHoursDto dto = weeklyWorkHours.get(i);
            String userName = dto.getFullName() != null ? dto.getFullName() : "Unknown User";
            Long hours = dto.getTotalHours() != null ? dto.getTotalHours() : 0L;

            // Create a dataset for each user with data array matching all users (but only this user's data)
            List<Long> userData = new ArrayList<>();
            for (int j = 0; j < weeklyWorkHours.size(); j++) {
                userData.add(j == i ? hours : 0L); // Only show data for this user's position
            }

            Map<String, Object> dataset = new HashMap<>();
            dataset.put("label", userName);
            dataset.put("data", userData);
            dataset.put("backgroundColor", getColorForIndex(i));
            dataset.put("borderColor", getBorderColorForIndex(i));
            dataset.put("borderWidth", 1);
            dataset.put("hidden", false); // Initially visible

            datasets.add(dataset);
        }

        // Calculate navigation dates
        LocalDate previousWeek = targetWeekStart.minusWeeks(1);
        LocalDate nextWeek = targetWeekStart.plusWeeks(1);

        // Get week info
        String weekTitle = weeklyWorkHours.isEmpty() ?
            getWeekTitle(targetWeekStart) :
            weeklyWorkHours.get(0).getWeekTitle();

        // Create response map
        Map<String, Object> response = new HashMap<>();
        response.put("datasets", datasets);
        response.put("userNames", userNames);
        response.put("workHours", workHours); // For simple bar chart if needed
        response.put("weekStart", targetWeekStart.toString());
        response.put("weekEnd", targetWeekStart.plusDays(6).toString());
        response.put("weekTitle", weekTitle);
        response.put("previousWeek", previousWeek.toString());
        response.put("nextWeek", nextWeek.toString());
        response.put("canViewAllUsers", canViewAllUsers);
        response.put("currentUserName", currentUser.getFullName());
        response.put("success", true);
        response.put("message", "Weekly data retrieved successfully");

        logger.info("Returning weekly chart data: {} users for week {}", userNames.size(), weekTitle);
        return response;
    }

    private LocalDate getCurrentWeekStart() {
        return getWeekStart(LocalDate.now());
    }

    private LocalDate getWeekStart(LocalDate date) {
        WeekFields weekFields = WeekFields.of(Locale.getDefault());
        return date.with(weekFields.dayOfWeek(), 1); // Monday is day 1
    }

    private String getWeekTitle(LocalDate weekStart) {
        LocalDate weekEnd = weekStart.plusDays(6);
        WeekFields weekFields = WeekFields.of(Locale.getDefault());
        int weekNumber = weekStart.get(weekFields.weekOfYear());
        int year = weekStart.getYear();

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd.MM.yyyy");
        return "Týždeň " + weekNumber + "/" + year + " (" +
               weekStart.format(formatter) + " - " + weekEnd.format(formatter) + ")";
    }

    private String getColorForIndex(int index) {
        String[] colors = {
            "rgba(54, 162, 235, 0.6)",   // Blue
            "rgba(255, 99, 132, 0.6)",   // Red
            "rgba(255, 205, 86, 0.6)",   // Yellow
            "rgba(75, 192, 192, 0.6)",   // Teal
            "rgba(153, 102, 255, 0.6)",  // Purple
            "rgba(255, 159, 64, 0.6)",   // Orange
            "rgba(199, 199, 199, 0.6)",  // Grey
            "rgba(83, 102, 255, 0.6)",   // Indigo
            "rgba(255, 99, 255, 0.6)",   // Pink
            "rgba(99, 255, 132, 0.6)"    // Green
        };
        return colors[index % colors.length];
    }

    private String getBorderColorForIndex(int index) {
        String[] colors = {
            "rgba(54, 162, 235, 1)",     // Blue
            "rgba(255, 99, 132, 1)",     // Red
            "rgba(255, 205, 86, 1)",     // Yellow
            "rgba(75, 192, 192, 1)",     // Teal
            "rgba(153, 102, 255, 1)",    // Purple
            "rgba(255, 159, 64, 1)",     // Orange
            "rgba(199, 199, 199, 1)",    // Grey
            "rgba(83, 102, 255, 1)",     // Indigo
            "rgba(255, 99, 255, 1)",     // Pink
            "rgba(99, 255, 132, 1)"      // Green
        };
        return colors[index % colors.length];
    }

    @GetMapping("/user-customer-work-hours")
    public Map<String, Object> getUserCustomerWorkHoursData(@RequestParam(required = false) String weekStart) {
        logger.info("Fetching user-customer work hours data for week: {}", weekStart);

        // Check current user permissions
        User currentUser = userService.getCurrentUser();
        if (currentUser == null) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "User not authenticated");
            return errorResponse;
        }

        boolean canViewAllUsers = currentUser.isCanViewAllWorkHours();
        logger.info("User {} can view all users: {}", currentUser.getFullName(), canViewAllUsers);

        List<UserCustomerWorkHoursDto> customerWorkHours;

        if (weekStart != null && !weekStart.trim().isEmpty()) {
            // Weekly data
            LocalDate targetWeekStart;
            try {
                targetWeekStart = LocalDate.parse(weekStart);
            } catch (Exception e) {
                logger.warn("Invalid week start date format: {}, using current week", weekStart);
                targetWeekStart = LocalDate.now();
            }

            // Ensure we start from Monday
            targetWeekStart = getWeekStart(targetWeekStart);

            // Get data based on user permissions
            if (canViewAllUsers) {
                customerWorkHours = taskService.getUserCustomerWorkHoursStatisticsByWeek(targetWeekStart);
                logger.info("Retrieved {} customer work hours records for all users for week {}", customerWorkHours.size(), targetWeekStart);
            } else {
                customerWorkHours = taskService.getUserCustomerWorkHoursStatisticsByWeekForCurrentUser(targetWeekStart);
                logger.info("Retrieved {} customer work hours records for current user only for week {}", customerWorkHours.size(), targetWeekStart);
            }
        } else {
            // All-time data
            if (canViewAllUsers) {
                customerWorkHours = taskService.getUserCustomerWorkHoursStatistics();
                logger.info("Retrieved {} customer work hours records for all users (all-time)", customerWorkHours.size());
            } else {
                // For current user only, we'll use a very wide date range
                LocalDate currentWeek = getWeekStart(LocalDate.now());
                customerWorkHours = taskService.getUserCustomerWorkHoursStatisticsByWeekForCurrentUser(currentWeek.minusYears(10));
                logger.info("Retrieved {} customer work hours records for current user only (all-time)", customerWorkHours.size());
            }
        }

        // Group data by customer and create datasets for each user
        Map<String, List<UserCustomerWorkHoursDto>> customerUserMap = customerWorkHours.stream()
                .collect(Collectors.groupingBy(UserCustomerWorkHoursDto::getCustomerDisplayName));

        // Get all unique users
        Set<String> allUsers = customerWorkHours.stream()
                .map(UserCustomerWorkHoursDto::getFullName)
                .collect(Collectors.toSet());

        List<String> userNames = new ArrayList<>(allUsers);
        Collections.sort(userNames);

        List<String> customerNames = new ArrayList<>(customerUserMap.keySet());
        Collections.sort(customerNames);

        // Create datasets for each user
        List<Map<String, Object>> datasets = new ArrayList<>();
        int userIndex = 0;
        for (String userName : userNames) {
            // Create data array for this user across all customers
            List<Long> userDataArray = new ArrayList<>();
            for (String customerName : customerNames) {
                Long hours = customerUserMap.getOrDefault(customerName, new ArrayList<>()).stream()
                        .filter(dto -> dto.getFullName().equals(userName))
                        .mapToLong(UserCustomerWorkHoursDto::getTotalHours)
                        .sum();
                userDataArray.add(hours);
            }

            Map<String, Object> dataset = new HashMap<>();
            dataset.put("label", userName);
            dataset.put("data", userDataArray);
            dataset.put("backgroundColor", getColorForIndex(userIndex));
            dataset.put("borderColor", getBorderColorForIndex(userIndex));
            dataset.put("borderWidth", 1);
            dataset.put("hidden", false);

            datasets.add(dataset);
            userIndex++;
        }

        // Create response map
        Map<String, Object> response = new HashMap<>();
        response.put("datasets", datasets);
        response.put("customerNames", customerNames);
        response.put("userNames", userNames);
        response.put("canViewAllUsers", canViewAllUsers);
        response.put("currentUserName", currentUser.getFullName());
        response.put("success", true);
        response.put("message", "Customer work hours data retrieved successfully");

        logger.info("Returning customer chart data with {} datasets and {} customers", datasets.size(), customerNames.size());
        return response;
    }
}
