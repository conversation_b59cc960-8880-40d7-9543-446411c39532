package com.solax.triple_tea.controller;


import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import com.solax.triple_tea.entity.User;
import com.solax.triple_tea.model.ChangePasswordDto;
import com.solax.triple_tea.service.UserService;

@Controller
@RequestMapping("/users")
public class UserController {

    private final UserService userService;

    public UserController(UserService userService) {
        this.userService = userService;
    }

    @GetMapping
    public String listUsers(Model model) {
        model.addAttribute("users", userService.findAll());
        return "user/list";
    }

    @GetMapping("/new")
    public String showCreateForm(Model model) {
        model.addAttribute("user", new User());
        return "user/form";
    }

    @GetMapping("/edit/{id}")
    public String showEditForm(@PathVariable Long id, Model model) {
        User user = userService.findById(id).orElseThrow(() -> new IllegalArgumentException("Invalid user ID: " + id));
        model.addAttribute("user", user);
        return "user/form";
    }

    @PostMapping("/save")
    public String save(@ModelAttribute User user) {
        userService.save(user);
        return "redirect:/users";
    }

    @PostMapping("/disable/{id}")
    public String disableUser(@PathVariable Long id) {
        User user = userService.findById(id).orElseThrow(() -> new IllegalArgumentException("Invalid user ID: " + id));
        user.setEnabled(false);
        userService.save(user);
        return "redirect:/users";
    }

    @PostMapping("/enable/{id}")
    public String enableUser(@PathVariable Long id) {
        User user = userService.findById(id).orElseThrow(() -> new IllegalArgumentException("Invalid user ID: " + id));
        user.setEnabled(true);
        userService.save(user);
        return "redirect:/users";
    }

    @GetMapping("/delete/{id}")
    public String delete(@PathVariable Long id) {
        User user = userService.findById(id).orElseThrow(() -> new IllegalArgumentException("Invalid user ID: " + id));
        user.setEnabled(false);
        userService.save(user);
        return "redirect:/users";
    }

    @GetMapping("/change-password/{id}")
    public String showChangePasswordForm(@PathVariable Long id, Model model) {
        ChangePasswordDto form = new ChangePasswordDto();
        form.setUserId(id);
        model.addAttribute("passwordChangeForm", form);
        return "user/change-password";
    }

    @PostMapping("/change-password")
    public String changePassword(@ModelAttribute ChangePasswordDto form, Model model) {
        try {
            userService.changePassword(form.getUserId(), form.getCurrentPassword(), form.getNewPassword(), form.getConfirmPassword());
            return "redirect:/users";
        } catch (IllegalArgumentException ex) {
            model.addAttribute("passwordChangeForm", form);
            model.addAttribute("error", ex.getMessage());
            return "user/change-password";
        }
    }
}
