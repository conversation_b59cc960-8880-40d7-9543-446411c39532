package com.solax.triple_tea.service;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import com.solax.triple_tea.entity.BankAccount;

public interface BankAccountService  {

    BankAccount create(BankAccount d);

    BankAccount update(BankAccount d);

    BankAccount getOne(long id) ;

    List<BankAccount> getAll();

    long getTotal();

    void delete(long id);

    Page<BankAccount> findAllPaginate(Pageable pageable);

	Page<BankAccount> findAllSpecification(Specification<BankAccount> specs, Pageable pageable);
}
