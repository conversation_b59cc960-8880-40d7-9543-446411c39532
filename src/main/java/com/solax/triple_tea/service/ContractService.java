package com.solax.triple_tea.service;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import com.solax.triple_tea.entity.Contract;


public interface ContractService  {

    Contract create(Contract d);

    Contract update(Contract d);

    Contract getOne(long id) ;

    List<Contract> getAll();

    long getTotal();

    void delete(long id);

    Page<Contract> findAllPaginate(Pageable pageable);

	Page<Contract> findAllSpecification(Specification<Contract> specs, Pageable pageable);
}
