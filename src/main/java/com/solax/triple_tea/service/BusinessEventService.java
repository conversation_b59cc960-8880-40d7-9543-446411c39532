package com.solax.triple_tea.service;

import java.util.List;
import java.util.Optional;

import com.solax.triple_tea.entity.BusinessEvent;
import com.solax.triple_tea.entity.Customer;

public interface BusinessEventService  {

    List<BusinessEvent> findAll();

    List<BusinessEvent> findAllByCustomer(Customer customer);

    BusinessEvent save(BusinessEvent businessEvent);

    Optional<BusinessEvent> findById(Long id);

    void deleteById(Long id);
}
