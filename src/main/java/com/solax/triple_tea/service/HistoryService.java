package com.solax.triple_tea.service;

import java.util.List;

import com.solax.triple_tea.dto.HistoryItemDto;
import com.solax.triple_tea.entity.Customer;

public interface HistoryService {
    
    /**
     * Get all history items for a specific customer
     * @param customer The customer to get history for
     * @return List of history items sorted by creation date (newest first)
     */
    List<HistoryItemDto> getCustomerHistory(Customer customer);
    
    /**
     * Get all history items across all customers
     * @return List of all history items sorted by creation date (newest first)
     */
    List<HistoryItemDto> getAllHistory();
}
