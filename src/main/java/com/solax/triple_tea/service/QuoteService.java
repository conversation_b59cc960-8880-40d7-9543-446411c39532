package com.solax.triple_tea.service;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import com.solax.triple_tea.entity.Customer;
import com.solax.triple_tea.entity.Quote;

public interface QuoteService {

    Quote create(Quote quote);

    Quote update(Quote quote);

    Quote getOne(long id);

    List<Quote> getAll();

    long getTotal();

    void delete(long id);

    Page<Quote> findAllPaginate(Pageable pageable);

	Page<Quote> findAllSpecification(Specification<Quote> specs, Pageable pageable);
	
	List<Quote> findAllByCustomer(Customer customer);
}
