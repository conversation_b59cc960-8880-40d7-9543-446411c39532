package com.solax.triple_tea.service;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import com.solax.triple_tea.dto.UserCustomerWorkHoursDto;
import com.solax.triple_tea.dto.UserWorkHoursDto;
import com.solax.triple_tea.dto.WeeklyWorkHoursDto;
import com.solax.triple_tea.entity.Task;

public interface TaskService {

    List<Task> findAll();

    void save(Task task);

    void delete(Long id);

    Optional<Task> findById(Long id);

    List<Task> findActiveByCurrentUser();

    List<Task> findNotArchived();

    List<UserWorkHoursDto> getUserWorkHoursStatistics();

    List<WeeklyWorkHoursDto> getWeeklyWorkHoursStatistics(LocalDate weekStart);

    List<WeeklyWorkHoursDto> getWeeklyWorkHoursStatisticsForCurrentUser(LocalDate weekStart);

    List<UserCustomerWorkHoursDto> getUserCustomerWorkHoursStatistics();

    List<UserCustomerWorkHoursDto> getUserCustomerWorkHoursStatisticsByWeek(LocalDate weekStart);

    List<UserCustomerWorkHoursDto> getUserCustomerWorkHoursStatisticsByWeekForCurrentUser(LocalDate weekStart);
}
