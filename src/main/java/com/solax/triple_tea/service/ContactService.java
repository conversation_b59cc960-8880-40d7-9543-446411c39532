package com.solax.triple_tea.service;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import com.solax.triple_tea.entity.Contact;

public interface ContactService  {

    Contact create(Contact d);

    Contact update(Contact d);

    Contact getOne(long id) ;

    List<Contact> getAll();

    long getTotal();

    void delete(long id);

    Page<Contact> findAllPaginate(Pageable pageable);

	Page<Contact> findAllSpecification(Specification<Contact> specs, Pageable pageable);

}
