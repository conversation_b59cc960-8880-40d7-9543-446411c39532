package com.solax.triple_tea.service;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.solax.triple_tea.entity.CustomerType;

public interface CustomerTypeService {
    List<CustomerType> findAll();

    CustomerType save(CustomerType d);

    CustomerType update(CustomerType d);

    CustomerType getOne(long id) ;

    List<CustomerType> getAll();
    
    long getTotal();

    void delete(long id);

    Page<CustomerType> findAllPaginate(Pageable pageable);
}
