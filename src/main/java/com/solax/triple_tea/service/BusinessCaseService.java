package com.solax.triple_tea.service;

import java.util.List;
import java.util.Optional;

import com.solax.triple_tea.entity.BusinessCase;
import com.solax.triple_tea.entity.Customer;

public interface BusinessCaseService  {

    List<BusinessCase> findAll();

    List<BusinessCase> findAllByCustomer(Customer customer);

    BusinessCase save(BusinessCase businessCase);

    Optional<BusinessCase> findById(Long id);

    void deleteById(Long id);
}
