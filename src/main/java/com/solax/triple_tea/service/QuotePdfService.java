package com.solax.triple_tea.service;

import com.solax.triple_tea.entity.Quote;

public interface QuotePdfService {
    
    /**
     * Generate PDF for a quote
     * @param quote The quote to generate PDF for
     * @return PDF content as byte array
     */
    byte[] generateQuotePdf(Quote quote);
    
    /**
     * Generate filename for the quote PDF
     * @param quote The quote
     * @return Suggested filename for the PDF
     */
    String generatePdfFilename(Quote quote);
}
