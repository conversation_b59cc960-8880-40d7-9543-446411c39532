package com.solax.triple_tea.service;

import java.util.List;
import java.util.Set;

import com.solax.triple_tea.entity.Customer;
import com.solax.triple_tea.entity.Tag;

public interface TagService {

    Tag save(Tag tag);

    Tag findById(Long id);

    List<Tag> findAll();

    Long getTotal();

    void deleteById(Long id);

    Set<Customer> getCustomersByTagId(Long tagId);

    void migrateTagToAnother(Long fromTagId, Long toTagId);
}
