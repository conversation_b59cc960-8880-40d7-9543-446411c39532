package com.solax.triple_tea.service;

import java.io.ByteArrayOutputStream;

import com.solax.triple_tea.entity.Invoice;

public interface InvoicePdfService {
    
    /**
     * Generate PDF for an invoice
     * @param invoice The invoice to generate PDF for
     * @return PDF content as byte array
     */
    byte[] generateInvoicePdf(Invoice invoice);
    
    /**
     * Generate filename for the invoice PDF
     * @param invoice The invoice
     * @return Suggested filename for the PDF
     */
    String generatePdfFilename(Invoice invoice);
}
