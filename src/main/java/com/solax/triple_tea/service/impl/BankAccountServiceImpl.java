/*
*  Copyright (c) 2019. All right reserved
* Created on 2025-05-06 ( Date ISO 2025-05-06 - Time 08:15:48 )
* Generated by Telosys embedded generator ( version 4.2.0 )
*/
package com.solax.triple_tea.service.impl;


import java.util.Collections;
import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.solax.triple_tea.entity.BankAccount;
import com.solax.triple_tea.repository.BankAccountRepository;
import com.solax.triple_tea.service.BankAccountService;

/**
 * Service Implementation for managing {@link BankAccount}.
 * <AUTHOR>
 */
@Service
@Transactional
public class BankAccountServiceImpl implements BankAccountService {


    private final BankAccountRepository repository;

    public BankAccountServiceImpl(BankAccountRepository repo) {
         this.repository = repo;
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public BankAccount create(BankAccount d) {
        try {
            return repository.save(d);

        } catch (Exception ex) {
            return null;
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public BankAccount update(BankAccount d) {
        try {
            return repository.saveAndFlush(d);

        } catch (Exception ex) {
            return null;
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public BankAccount getOne(long id) {
        try {
            return repository.findById(id).orElse(null);

        } catch (Exception ex) {
            return null;
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<BankAccount> getAll() {
        try {
            return repository.findAll();

        } catch (Exception ex) {
            return Collections.emptyList();
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public long getTotal() {
        try {
            return repository.count();
        } catch (Exception ex) {
            return 0;
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void delete(long id) {
        repository.deleteById(id);
    }

    /**
     * {@inheritDoc}
     */
   	@Override
	public Page<BankAccount> findAllPaginate(Pageable pageable) {

		return repository.findAll(pageable);
	}

    /**
     * {@inheritDoc}
     */
    @Override
	public Page<BankAccount> findAllSpecification(Specification<BankAccount> specs, Pageable pageable) {
		return repository.findAll(specs, pageable);
	}

}
