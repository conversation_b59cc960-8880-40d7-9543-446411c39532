package com.solax.triple_tea.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import jakarta.persistence.criteria.Predicate;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import com.solax.triple_tea.entity.Customer;
import com.solax.triple_tea.repository.CustomerRepository;
import com.solax.triple_tea.service.CustomerService;

@Service
public class CustomerServiceImpl implements CustomerService {

    private final CustomerRepository customerRepository;

    public CustomerServiceImpl(CustomerRepository customerRepository) {
        this.customerRepository = customerRepository;
    }

    @Override
    public List<Customer> findAll() {
        return customerRepository.findAll();
    }

    @Override
    public List<Customer> findEnabled() {
        return customerRepository.findByEnabled(true);
    }

    @Override
    public Customer save(Customer customer) {
        return customerRepository.save(customer);
    }

    @Override
    public Optional<Customer> findById(Long id) {
        return customerRepository.findById(id);
    }

    @Override
    public void deleteById(Long id) {
        Optional<Customer> optionalCustomer = customerRepository.findById(id);

        if (optionalCustomer.isPresent()) {
            Customer customer = optionalCustomer.get();
            customer.setEnabled(false);
            customerRepository.save(customer);
        }
    }

    @Override
    public Page<Customer> findAllWithFilters(Pageable pageable, String nameFilter, String icoFilter,
                                           String cityFilter, String countryFilter, String typeFilter, String subtypeFilter, List<Long> tagFilters, Long responsiblePersonFilter) {
        Specification<Customer> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (nameFilter != null && !nameFilter.trim().isEmpty()) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("name")),
                    "%" + nameFilter.toLowerCase() + "%"));
            }

            if (icoFilter != null && !icoFilter.trim().isEmpty()) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("ico")),
                    "%" + icoFilter.toLowerCase() + "%"));
            }

            if (cityFilter != null && !cityFilter.trim().isEmpty()) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("billingAddress").get("city")),
                    "%" + cityFilter.toLowerCase() + "%"));
            }

            if (countryFilter != null && !countryFilter.trim().isEmpty()) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("billingAddress").get("country")),
                    "%" + countryFilter.toLowerCase() + "%"));
            }

            if (typeFilter != null && !typeFilter.trim().isEmpty()) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("customerType").get("name")),
                    "%" + typeFilter.toLowerCase() + "%"));
            }

            if (subtypeFilter != null && !subtypeFilter.trim().isEmpty()) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("customerSubtype").get("name")),
                    "%" + subtypeFilter.toLowerCase() + "%"));
            }

            if (tagFilters != null && !tagFilters.isEmpty()) {
                predicates.add(root.join("tags").get("id").in(tagFilters));
            }

            if (responsiblePersonFilter != null) {
                predicates.add(criteriaBuilder.equal(
                    root.get("responsiblePerson").get("id"),
                    responsiblePersonFilter));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        return customerRepository.findAll(spec, pageable);
    }
}
