package com.solax.triple_tea.service.impl;


import java.util.Collections;
import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.solax.triple_tea.entity.Customer;
import com.solax.triple_tea.entity.Invoice;
import com.solax.triple_tea.repository.InvoiceRepository;
import com.solax.triple_tea.service.InvoiceService;
import com.solax.triple_tea.service.NumberGenerationService;

@Service
@Transactional
public class InvoiceServiceImpl implements InvoiceService {

    private final InvoiceRepository repository;
    private final NumberGenerationService numberGenerationService;

    public InvoiceServiceImpl(InvoiceRepository repo, NumberGenerationService numberGenerationService) {
         this.repository = repo;
         this.numberGenerationService = numberGenerationService;
    }

    @Override
    public Invoice create(Invoice d) {
        try {
            // Auto-generate invoice number if not set
            if (d.getNumber() == null || d.getNumber().trim().isEmpty()) {
                d.setNumber(generateNextInvoiceNumber());
            }
            return repository.save(d);

        } catch (Exception ex) {
            return null;
        }
    }

    @Override
    public Invoice update(Invoice d) {
        try {
            return repository.saveAndFlush(d);

        } catch (Exception ex) {
            return null;
        }
    }

    @Override
    public Invoice getOne(long id) {
        try {
            return repository.findById(id).orElse(null);

        } catch (Exception ex) {
            return null;
        }
    }

    @Override
    public List<Invoice> getAll() {
        try {
            return repository.findAll();

        } catch (Exception ex) {
            return Collections.emptyList();
        }
    }

    @Override
    public long getTotal() {
        try {
            return repository.count();
        } catch (Exception ex) {
            return 0;
        }
    }

    @Override
    public void delete(long id) {
        repository.deleteById(id);
    }

   	@Override
	public Page<Invoice> findAllPaginate(Pageable pageable) {

		return repository.findAll(pageable);
	}

    @Override
	public Page<Invoice> findAllSpecification(Specification<Invoice> specs, Pageable pageable) {
		return repository.findAll(specs, pageable);
	}

	@Override
	public List<Invoice> findAllByCustomer(Customer customer) {
		try {
			return repository.findByCustomer(customer);
		} catch (Exception ex) {
			return Collections.emptyList();
		}
	}

	@Override
	public String generateNextInvoiceNumber() {
		return numberGenerationService.generateNextInvoiceNumber();
	}
}
