package com.solax.triple_tea.service.impl;

import java.io.ByteArrayOutputStream;
import java.time.format.DateTimeFormatter;

import org.springframework.stereotype.Service;

import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.UnitValue;
import com.solax.triple_tea.entity.Quote;
import com.solax.triple_tea.service.QuotePdfService;

@Service
public class QuotePdfServiceImpl implements QuotePdfService {

    @Override
    public byte[] generateQuotePdf(Quote quote) {
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            PdfWriter writer = new PdfWriter(baos);
            PdfDocument pdfDoc = new PdfDocument(writer);
            Document document = new Document(pdfDoc);

            // Header with company info and quote title
            Table headerTable = new Table(UnitValue.createPercentArray(new float[]{3, 2})).useAllAvailableWidth();
            headerTable.setMarginBottom(20);

            // Left side - Company info (SOLAX RP s.r.o.)
            Paragraph companyInfo = new Paragraph()
                .add(new Paragraph("SOLAX RP s.r.o.").setFontSize(16))
                .add(new Paragraph("Bratislava, Slovakia"))
                .add(new Paragraph("IČO: 36743844"))
                .add(new Paragraph("DIČ: 2022336190"))
                .add(new Paragraph("IČ DPH: SK2022336190"))
                .add(new Paragraph("Tel: +*********** 789"))
                .add(new Paragraph("Email: <EMAIL>"));
            headerTable.addCell(companyInfo);

            // Right side - Quote title and number
            Paragraph quoteTitle = new Paragraph()
                .add(new Paragraph("CENOVÁ PONUKA").setFontSize(20).setTextAlignment(TextAlignment.RIGHT))
                .add(new Paragraph("QUOTE").setFontSize(16).setTextAlignment(TextAlignment.RIGHT))
                .add(new Paragraph("\n"))
                .add(new Paragraph("Číslo / Number: " + (quote.getNumber() != null ? quote.getNumber() : "Q-" + quote.getId()))
                    .setTextAlignment(TextAlignment.RIGHT));
            headerTable.addCell(quoteTitle);

            document.add(headerTable);

            // Customer and quote details
            Table detailsTable = new Table(UnitValue.createPercentArray(new float[]{1, 1})).useAllAvailableWidth();
            detailsTable.setMarginBottom(20);

            // Customer information
            StringBuilder customerDetails = new StringBuilder();
            customerDetails.append("ODBERATEĽ / CUSTOMER:\n");
            if (quote.getCustomer() != null) {
                customerDetails.append(quote.getCustomer().getName()).append("\n");
                if (quote.getCustomer().getIco() != null) {
                    customerDetails.append("IČO: ").append(quote.getCustomer().getIco()).append("\n");
                }
                if (quote.getCustomer().getDic() != null) {
                    customerDetails.append("DIČ: ").append(quote.getCustomer().getDic()).append("\n");
                }
            }
            detailsTable.addCell(new Paragraph(customerDetails.toString()));

            // Quote details - simplified
            StringBuilder quoteDetails = new StringBuilder();
            quoteDetails.append("ÚDAJE PONUKY / QUOTE DETAILS:\n");
            quoteDetails.append("Dátum vystavenia / Issue Date: ")
                .append(quote.getIssueDate() != null ? quote.getIssueDate().format(DateTimeFormatter.ofPattern("dd.MM.yyyy")) : "-")
                .append("\n");
            quoteDetails.append("Platnosť do / Valid Until: ")
                .append(quote.getValidUntilDate() != null ? quote.getValidUntilDate().format(DateTimeFormatter.ofPattern("dd.MM.yyyy")) : "-")
                .append("\n");
            detailsTable.addCell(new Paragraph(quoteDetails.toString()));

            document.add(detailsTable);

            // Items table header
            document.add(new Paragraph("POLOŽKY / ITEMS").setFontSize(14).setMarginBottom(10));

            // Items table
            Table itemsTable = new Table(UnitValue.createPercentArray(new float[]{1, 4, 1, 1, 1, 1.5f})).useAllAvailableWidth();
            itemsTable.setMarginBottom(20);

            // Table headers
            itemsTable.addHeaderCell(new Paragraph("Č. / No.").setTextAlignment(TextAlignment.CENTER));
            itemsTable.addHeaderCell(new Paragraph("Popis / Description"));
            itemsTable.addHeaderCell(new Paragraph("Množstvo / Qty").setTextAlignment(TextAlignment.CENTER));
            itemsTable.addHeaderCell(new Paragraph("Jednotka / Unit").setTextAlignment(TextAlignment.CENTER));
            itemsTable.addHeaderCell(new Paragraph("Cena / Price").setTextAlignment(TextAlignment.RIGHT));
            itemsTable.addHeaderCell(new Paragraph("Celkom / Total").setTextAlignment(TextAlignment.RIGHT));

            // Sample item (you can extend this to handle multiple items)
            itemsTable.addCell(new Paragraph("1").setTextAlignment(TextAlignment.CENTER));
            itemsTable.addCell(new Paragraph(quote.getNote() != null && !quote.getNote().trim().isEmpty() ?
                quote.getNote() : "Služby / Services"));
            itemsTable.addCell(new Paragraph("1").setTextAlignment(TextAlignment.CENTER));
            itemsTable.addCell(new Paragraph("ks / pcs").setTextAlignment(TextAlignment.CENTER));

            String priceText = quote.getTotal() != null ?
                String.format("%.2f %s", quote.getTotal(), quote.getCurrency() != null ? quote.getCurrency() : "EUR") :
                "0.00 EUR";
            itemsTable.addCell(new Paragraph(priceText).setTextAlignment(TextAlignment.RIGHT));
            itemsTable.addCell(new Paragraph(priceText).setTextAlignment(TextAlignment.RIGHT));

            document.add(itemsTable);

            // Summary table
            Table summaryTable = new Table(UnitValue.createPercentArray(new float[]{3, 1.5f})).useAllAvailableWidth();
            summaryTable.setMarginBottom(20);

            // Empty cell for spacing
            summaryTable.addCell(new Paragraph(""));

            // Summary details
            Table totalsTable = new Table(UnitValue.createPercentArray(new float[]{2, 1})).useAllAvailableWidth();

            if (quote.getTotalWithoutVat() != null) {
                totalsTable.addCell(new Paragraph("Suma bez DPH / Subtotal:"));
                totalsTable.addCell(new Paragraph(String.format("%.2f %s", quote.getTotalWithoutVat(),
                    quote.getCurrency() != null ? quote.getCurrency() : "EUR")).setTextAlignment(TextAlignment.RIGHT));

                // Calculate VAT (assuming 20%)
                double vat = quote.getTotal() != null && quote.getTotalWithoutVat() != null ?
                    quote.getTotal().doubleValue() - quote.getTotalWithoutVat().doubleValue() : 0.0;
                totalsTable.addCell(new Paragraph("DPH 20% / VAT 20%:"));
                totalsTable.addCell(new Paragraph(String.format("%.2f %s", vat,
                    quote.getCurrency() != null ? quote.getCurrency() : "EUR")).setTextAlignment(TextAlignment.RIGHT));
            }

            totalsTable.addCell(new Paragraph("CELKOM / TOTAL:"));
            totalsTable.addCell(new Paragraph(priceText).setTextAlignment(TextAlignment.RIGHT));

            summaryTable.addCell(totalsTable);
            document.add(summaryTable);

            // Terms and conditions - simplified
            document.add(new Paragraph("PODMIENKY / TERMS & CONDITIONS").setFontSize(12).setMarginBottom(10));
            document.add(new Paragraph("• Platnosť ponuky: " +
                (quote.getValidUntilDate() != null ? quote.getValidUntilDate().format(DateTimeFormatter.ofPattern("dd.MM.yyyy")) : "30 dní"))
                .setFontSize(10));
            document.add(new Paragraph("• Platobné podmienky: 14 dní od vystavenia faktúry").setFontSize(10));

            // Footer
            document.add(new Paragraph("\n\n"));
            document.add(new Paragraph("Vygenerované / Generated: " +
                java.time.LocalDateTime.now().format(DateTimeFormatter.ofPattern("dd.MM.yyyy HH:mm")))
                .setTextAlignment(TextAlignment.RIGHT)
                .setFontSize(8));

            document.close();
            return baos.toByteArray();

        } catch (Exception e) {
            throw new RuntimeException("Error generating PDF: " + e.getMessage(), e);
        }
    }

    @Override
    public String generatePdfFilename(Quote quote) {
        StringBuilder filename = new StringBuilder("quote");
        
        if (quote.getNumber() != null && !quote.getNumber().trim().isEmpty()) {
            filename.append("_").append(quote.getNumber().replaceAll("[^a-zA-Z0-9]", "_"));
        } else if (quote.getId() != null) {
            filename.append("_").append(quote.getId());
        }
        
        if (quote.getCustomer() != null && quote.getCustomer().getName() != null) {
            String customerName = quote.getCustomer().getName()
                .replaceAll("[^a-zA-Z0-9]", "_")
                .substring(0, Math.min(20, quote.getCustomer().getName().length()));
            filename.append("_").append(customerName);
        }
        
        filename.append(".pdf");
        return filename.toString();
    }
}
