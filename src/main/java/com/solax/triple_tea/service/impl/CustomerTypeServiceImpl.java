package com.solax.triple_tea.service.impl;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import com.solax.triple_tea.entity.CustomerType;
import com.solax.triple_tea.repository.CustomerTypeRepository;
import com.solax.triple_tea.service.CustomerTypeService;

import jakarta.persistence.EntityNotFoundException;

@Service
public class CustomerTypeServiceImpl implements CustomerTypeService {

    private final CustomerTypeRepository customerTypeRepository;

    public CustomerTypeServiceImpl(CustomerTypeRepository customerTypeRepository) {
        this.customerTypeRepository = customerTypeRepository;
    }

    @Override
    public List<CustomerType> findAll() {
        return customerTypeRepository.findAll();
    }

    @Override
    public CustomerType save(CustomerType customerType) {
        return customerTypeRepository.save(customerType);
    }

    @Override
    public CustomerType update(CustomerType customerType) {
        if (customerType.getId() == null || !customerTypeRepository.existsById(customerType.getId())) {
            throw new EntityNotFoundException("CustomerType with id " + customerType.getId() + " not found");
        }
        return customerTypeRepository.save(customerType);
    }

    @Override
    public CustomerType getOne(long id) {
        return customerTypeRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("CustomerType with id " + id + " not found"));
    }

    @Override
    public List<CustomerType> getAll() {
        return customerTypeRepository.findAll();
    }

    @Override
    public long getTotal() {
        return customerTypeRepository.count();
    }

    @Override
    public void delete(long id) {
        if (!customerTypeRepository.existsById(id)) {
            throw new EntityNotFoundException("CustomerType with id " + id + " not found");
        }
        customerTypeRepository.deleteById(id);
    }

    @Override
    public Page<CustomerType> findAllPaginate(Pageable pageable) {
        return customerTypeRepository.findAll(pageable);
    }
}
