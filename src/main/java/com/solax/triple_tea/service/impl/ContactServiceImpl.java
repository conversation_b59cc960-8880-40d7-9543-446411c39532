package com.solax.triple_tea.service.impl;

import java.util.Collections;
import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.solax.triple_tea.entity.Contact;
import com.solax.triple_tea.repository.ContactRepository;
import com.solax.triple_tea.service.ContactService;

@Service
@Transactional
public class ContactServiceImpl implements ContactService {

    private final ContactRepository repository;

    public ContactServiceImpl(ContactRepository repo) {
        this.repository = repo;
    }

    @Override
    public Contact create(Contact d) {
        try {
            return repository.save(d);

        } catch (Exception ex) {
            return null;
        }
    }

    @Override
    public Contact update(Contact d) {
        try {
            return repository.saveAndFlush(d);

        } catch (Exception ex) {
            return null;
        }
    }

    @Override
    public Contact getOne(long id) {
        try {
            return repository.findById(id).orElse(null);

        } catch (Exception ex) {
            return null;
        }
    }

    @Override
    public List<Contact> getAll() {
        try {
            return repository.findAll();

        } catch (Exception ex) {
            return Collections.emptyList();
        }
    }

    @Override
    public long getTotal() {
        try {
            return repository.count();
        } catch (Exception ex) {
            return 0;
        }
    }

    @Override
    public void delete(long id) {
        repository.deleteById(id);
    }

    @Override
    public Page<Contact> findAllPaginate(Pageable pageable) {
        return repository.findAll(pageable);
    }

    @Override
    public Page<Contact> findAllSpecification(Specification<Contact> specs, Pageable pageable) {
        return repository.findAll(specs, pageable);
    }

}
