package com.solax.triple_tea.service.impl;

import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.solax.triple_tea.entity.BusinessEvent;
import com.solax.triple_tea.entity.Customer;
import com.solax.triple_tea.repository.BusinessEventRepository;
import com.solax.triple_tea.service.BusinessEventService;

@Service
@Transactional
public class BusinessEventServiceImpl implements BusinessEventService {

    private final BusinessEventRepository businessEventRepository;

    public BusinessEventServiceImpl(BusinessEventRepository businessEventRepository) {
        this.businessEventRepository = businessEventRepository;
    }

    @Override
    public List<BusinessEvent> findAll() {
        return businessEventRepository.findAll();
    }

    @Override
    public List<BusinessEvent> findAllByCustomer(Customer customer) {
        return businessEventRepository.findByCustomer(customer);
    }

    @Override
    public BusinessEvent save(BusinessEvent businessEvent) {
        return businessEventRepository.save(businessEvent);
    }

    @Override
    public Optional<BusinessEvent> findById(Long id) {
        return businessEventRepository.findById(id);
    }

    @Override
    public void deleteById(Long id) {
        businessEventRepository.deleteById(id);
    }
}
