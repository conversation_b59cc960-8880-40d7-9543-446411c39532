package com.solax.triple_tea.service.impl;

import java.io.ByteArrayOutputStream;
import java.time.format.DateTimeFormatter;

import org.springframework.stereotype.Service;

import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.UnitValue;
import com.solax.triple_tea.entity.Invoice;
import com.solax.triple_tea.service.InvoicePdfService;

@Service
public class InvoicePdfServiceImpl implements InvoicePdfService {

    @Override
    public byte[] generateInvoicePdf(Invoice invoice) {
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            PdfWriter writer = new PdfWriter(baos);
            PdfDocument pdfDoc = new PdfDocument(writer);
            Document document = new Document(pdfDoc);

            // Add title
            document.add(new Paragraph("INVOICE")
                    .setTextAlignment(TextAlignment.CENTER)
                    .setFontSize(20));

            // Add invoice details
            document.add(new Paragraph("\n"));
            
            // Invoice header information
            Table headerTable = new Table(UnitValue.createPercentArray(2)).useAllAvailableWidth();
            
            // Left column - Invoice details
            StringBuilder invoiceDetails = new StringBuilder();
            invoiceDetails.append("Invoice Number: ").append(invoice.getNumber() != null ? invoice.getNumber() : "N/A").append("\n");
            invoiceDetails.append("Issue Date: ").append(invoice.getIssueDate() != null ? 
                invoice.getIssueDate().format(DateTimeFormatter.ofPattern("dd.MM.yyyy")) : "N/A").append("\n");
            invoiceDetails.append("Due Date: ").append(invoice.getDueDate() != null ? 
                invoice.getDueDate().format(DateTimeFormatter.ofPattern("dd.MM.yyyy")) : "N/A").append("\n");
            invoiceDetails.append("Status: ").append(invoice.getStatus() != null ? invoice.getStatus().toString() : "N/A");
            
            headerTable.addCell(new Paragraph(invoiceDetails.toString()));
            
            // Right column - Company details
            StringBuilder companyDetails = new StringBuilder();
            companyDetails.append("Company: ").append(invoice.getCompany() != null ? invoice.getCompany() : "N/A").append("\n");
            companyDetails.append("Issued By: ").append(invoice.getIssuedBy() != null ? invoice.getIssuedBy() : "N/A").append("\n");
            if (invoice.getUser() != null) {
                companyDetails.append("Responsible: ").append(invoice.getUser().getFirstName())
                    .append(" ").append(invoice.getUser().getLastName()).append("\n");
            }
            
            headerTable.addCell(new Paragraph(companyDetails.toString()));
            document.add(headerTable);

            document.add(new Paragraph("\n"));

            // Customer information
            if (invoice.getCustomer() != null) {
                document.add(new Paragraph("CUSTOMER INFORMATION")
                        .setFontSize(14));
                
                StringBuilder customerInfo = new StringBuilder();
                customerInfo.append("Customer: ").append(invoice.getCustomer().getName()).append("\n");
                if (invoice.getCustomer().getIco() != null) {
                    customerInfo.append("ICO: ").append(invoice.getCustomer().getIco()).append("\n");
                }
                if (invoice.getCustomer().getDic() != null) {
                    customerInfo.append("DIC: ").append(invoice.getCustomer().getDic()).append("\n");
                }
                
                document.add(new Paragraph(customerInfo.toString()));
                document.add(new Paragraph("\n"));
            }

            // Financial information
            document.add(new Paragraph("FINANCIAL DETAILS")
                    .setFontSize(14));
            
            Table financialTable = new Table(UnitValue.createPercentArray(2)).useAllAvailableWidth();
            
            if (invoice.getTotal() != null) {
                financialTable.addCell("Total Amount:");
                financialTable.addCell(invoice.getTotal().toString() + " " + 
                    (invoice.getCurrency() != null ? invoice.getCurrency() : "EUR"));
            }
            
            if (invoice.getTotalWithoutVat() != null) {
                financialTable.addCell("Total without VAT:");
                financialTable.addCell(invoice.getTotalWithoutVat().toString() + " " + 
                    (invoice.getCurrency() != null ? invoice.getCurrency() : "EUR"));
            }
            
            if (invoice.getPaymentMethod() != null) {
                financialTable.addCell("Payment Method:");
                financialTable.addCell(invoice.getPaymentMethod());
            }
            
            document.add(financialTable);

            // Notes
            if (invoice.getNote() != null && !invoice.getNote().trim().isEmpty()) {
                document.add(new Paragraph("\n"));
                document.add(new Paragraph("NOTES")
                        .setFontSize(14));
                document.add(new Paragraph(invoice.getNote()));
            }

            // Footer
            document.add(new Paragraph("\n\n"));
            document.add(new Paragraph("Generated on: " + 
                java.time.LocalDateTime.now().format(DateTimeFormatter.ofPattern("dd.MM.yyyy HH:mm")))
                    .setTextAlignment(TextAlignment.RIGHT)
                    .setFontSize(10));

            document.close();
            return baos.toByteArray();
            
        } catch (Exception e) {
            throw new RuntimeException("Error generating PDF: " + e.getMessage(), e);
        }
    }

    @Override
    public String generatePdfFilename(Invoice invoice) {
        StringBuilder filename = new StringBuilder("invoice");
        
        if (invoice.getNumber() != null && !invoice.getNumber().trim().isEmpty()) {
            filename.append("_").append(invoice.getNumber().replaceAll("[^a-zA-Z0-9]", "_"));
        } else if (invoice.getId() != null) {
            filename.append("_").append(invoice.getId());
        }
        
        if (invoice.getCustomer() != null && invoice.getCustomer().getName() != null) {
            String customerName = invoice.getCustomer().getName()
                .replaceAll("[^a-zA-Z0-9]", "_")
                .substring(0, Math.min(20, invoice.getCustomer().getName().length()));
            filename.append("_").append(customerName);
        }
        
        filename.append(".pdf");
        return filename.toString();
    }
}
