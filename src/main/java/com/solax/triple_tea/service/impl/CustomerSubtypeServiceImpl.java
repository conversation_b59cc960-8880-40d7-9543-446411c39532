package com.solax.triple_tea.service.impl;

import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Service;

import com.solax.triple_tea.entity.CustomerSubType;
import com.solax.triple_tea.repository.CustomerSubtypeRepository;
import com.solax.triple_tea.service.CustomerSubtypeService;

@Service
public class CustomerSubtypeServiceImpl implements CustomerSubtypeService {

    private final CustomerSubtypeRepository customerSubtypeRepository;

    public CustomerSubtypeServiceImpl(CustomerSubtypeRepository customerSubtypeRepository) {
        this.customerSubtypeRepository = customerSubtypeRepository;
    }

    @Override
    public List<CustomerSubType> findAll() {
        return customerSubtypeRepository.findAll();
    }

    @Override
    public CustomerSubType save(CustomerSubType customerSubType) {
        return customerSubtypeRepository.save(customerSubType);
    }

    @Override
    public Optional<CustomerSubType> findById(Long id) {
        return customerSubtypeRepository.findById(id);
    }

    @Override
    public void deleteById(Long id) {
        customerSubtypeRepository.deleteById(id);
    }
}
