package com.solax.triple_tea.service.impl;

import java.util.Collections;
import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.solax.triple_tea.entity.Customer;
import com.solax.triple_tea.entity.Quote;
import com.solax.triple_tea.repository.QuoteRepository;
import com.solax.triple_tea.service.QuoteService;

@Service
@Transactional
public class QuoteServiceImpl implements QuoteService {

    private final QuoteRepository repository;

    public QuoteServiceImpl(QuoteRepository repository) {
        this.repository = repository;
    }

    @Override
    public Quote create(Quote quote) {
        try {
            return repository.save(quote);
        } catch (Exception ex) {
            return null;
        }
    }

    @Override
    public Quote update(Quote quote) {
        try {
            return repository.saveAndFlush(quote);
        } catch (Exception ex) {
            return null;
        }
    }

    @Override
    public Quote getOne(long id) {
        try {
            return repository.findById(id).orElse(null);
        } catch (Exception ex) {
            return null;
        }
    }

    @Override
    public List<Quote> getAll() {
        try {
            return repository.findAll();
        } catch (Exception ex) {
            return Collections.emptyList();
        }
    }

    @Override
    public long getTotal() {
        try {
            return repository.count();
        } catch (Exception ex) {
            return 0;
        }
    }

    @Override
    public void delete(long id) {
        try {
            repository.deleteById(id);
        } catch (Exception ex) {
            // Log error if needed
        }
    }

    @Override
    public Page<Quote> findAllPaginate(Pageable pageable) {
        return repository.findAll(pageable);
    }

    @Override
    public Page<Quote> findAllSpecification(Specification<Quote> specs, Pageable pageable) {
        return repository.findAll(specs, pageable);
    }

    @Override
    public List<Quote> findAllByCustomer(Customer customer) {
        try {
            return repository.findByCustomer(customer);
        } catch (Exception ex) {
            return Collections.emptyList();
        }
    }
}
