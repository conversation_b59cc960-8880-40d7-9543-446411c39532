package com.solax.triple_tea.service.impl;

import java.util.Collections;
import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.solax.triple_tea.entity.Contract;
import com.solax.triple_tea.repository.ContractRepository;
import com.solax.triple_tea.service.ContractService;

@Service
@Transactional
public class ContractServiceImpl implements ContractService {


    private final ContractRepository repository;

    public ContractServiceImpl(ContractRepository repo) {
         this.repository = repo;
    }


    
    @Override
    public Contract create(Contract d) {
        try {
            return repository.save(d);

        } catch (Exception ex) {
            return null;
        }
    }

    @Override
    public Contract update(Contract d) {
        try {
            return repository.saveAndFlush(d);

        } catch (Exception ex) {
            return null;
        }
    }

    @Override
    public Contract getOne(long id) {
        try {
            return repository.findById(id).orElse(null);

        } catch (Exception ex) {
            return null;
        }
    }

    @Override
    public List<Contract> getAll() {
        try {
            return repository.findAll();

        } catch (Exception ex) {
            return Collections.emptyList();
        }
    }

    @Override
    public long getTotal() {
        try {
            return repository.count();
        } catch (Exception ex) {
            return 0;
        }
    }

    @Override
    public void delete(long id) {
        repository.deleteById(id);
    }

   	@Override
	public Page<Contract> findAllPaginate(Pageable pageable) {

		return repository.findAll(pageable);
	}

    @Override
	public Page<Contract> findAllSpecification(Specification<Contract> specs, Pageable pageable) {
		return repository.findAll(specs, pageable);
	}
}
