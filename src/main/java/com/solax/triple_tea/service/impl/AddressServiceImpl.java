package com.solax.triple_tea.service.impl;


import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.solax.triple_tea.entity.Address;
import com.solax.triple_tea.repository.AddressRepository;
import com.solax.triple_tea.service.AddressService;

@Service
@Transactional
public class AddressServiceImpl implements AddressService {

    private final AddressRepository repository;

    public AddressServiceImpl(AddressRepository repo) {
        this.repository = repo;
    }

    @Override
    public List<Address> findAll() {
        return repository.findAll();
    }

    @Override
    public void save(Address address) {
        repository.save(address);
    }

    @Override
    public void delete(Long id) {
        repository.deleteById(id);
    }

    @Override
    public Optional<Address> findById(Long id) {
        return repository.findById(id);
    }
}
