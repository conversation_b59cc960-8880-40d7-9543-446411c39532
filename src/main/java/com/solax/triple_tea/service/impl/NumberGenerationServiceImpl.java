package com.solax.triple_tea.service.impl;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.solax.triple_tea.repository.InvoiceRepository;
import com.solax.triple_tea.service.NumberGenerationService;

@Service
@Transactional
public class NumberGenerationServiceImpl implements NumberGenerationService {

    private final InvoiceRepository invoiceRepository;
    
    public NumberGenerationServiceImpl(InvoiceRepository invoiceRepository) {
        this.invoiceRepository = invoiceRepository;
    }

    @Override
    public String generateNextInvoiceNumber() {
        LocalDate now = LocalDate.now();
        String yearMonth = now.format(DateTimeFormatter.ofPattern("yyMM"));
        String pattern = yearMonth + "%";
        
        Optional<String> latestNumber = invoiceRepository.findLatestNumberByPattern(pattern);
        
        if (latestNumber.isPresent()) {
            String lastNumber = latestNumber.get();
            // Extract the sequence number (last 4 digits)
            String sequenceStr = lastNumber.substring(4); // Skip yyMM part
            try {
                int sequence = Integer.parseInt(sequenceStr);
                sequence++;
                return yearMonth + String.format("%04d", sequence);
            } catch (NumberFormatException e) {
                // If parsing fails, start from 0001
                return yearMonth + "0001";
            }
        } else {
            // No invoices for this month yet, start with 0001
            return yearMonth + "0001";
        }
    }
}
