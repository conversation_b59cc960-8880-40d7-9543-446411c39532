package com.solax.triple_tea.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.solax.triple_tea.entity.Customer;
import com.solax.triple_tea.entity.Tag;
import com.solax.triple_tea.repository.TagRepository;
import com.solax.triple_tea.service.TagService;

@Service
@Transactional
public class TagServiceImpl implements TagService {

    private final TagRepository tagRepository;

    public TagServiceImpl(TagRepository tagRepository) {
        this.tagRepository = tagRepository;
    }

    @Override
    public Tag save(Tag tag) {
        try {
            return tagRepository.save(tag);
        } catch (Exception ex) {
            throw new RuntimeException("Failed to create tag: " + ex.getMessage(), ex);
        }
    }

    @Override
    public Tag findById(Long id) {
        try {
            return tagRepository.findById(id)
                    .orElseThrow(() -> new RuntimeException("Tag not found with id: " + id));
        } catch (Exception ex) {
            throw new RuntimeException("Failed to get tag: " + ex.getMessage(), ex);
        }
    }

    @Override
    public List<Tag> findAll() {
        try {
            return new ArrayList<>(tagRepository.findAll());
        } catch (Exception ex) {
            return Collections.emptyList();
        }
    }

    @Override
    public Long getTotal() {
        try {
            return tagRepository.count();
        } catch (Exception ex) {
            return 0L;
        }
    }

    @Override
    public void deleteById(Long id) {
        try {
            Tag tag = findById(id);
            for (Customer customer : tag.getCustomers()) {
                customer.getTags().remove(tag);
            }
            tagRepository.delete(tag);
        } catch (Exception ex) {
            throw new RuntimeException("Failed to delete tag: " + ex.getMessage(), ex);
        }
    }

    @Override
    public Set<Customer> getCustomersByTagId(Long tagId) {
        try {
            Tag tag = findById(tagId);
            return tag.getCustomers();
        } catch (Exception ex) {
            return Collections.emptySet();
        }
    }

    @Override
    public void migrateTagToAnother(Long fromTagId, Long toTagId) {
        try {
            Tag fromTag = findById(fromTagId);
            Tag toTag = findById(toTagId);

            // Get all customers with the fromTag
            Set<Customer> customersToMigrate = new HashSet<>(fromTag.getCustomers());

            // Add the toTag to all customers that had the fromTag
            for (Customer customer : customersToMigrate) {
                customer.getTags().remove(fromTag);
                customer.getTags().add(toTag);
            }

            // Clear the fromTag's customers
            fromTag.getCustomers().clear();

            // Delete the fromTag
            tagRepository.delete(fromTag);

        } catch (Exception ex) {
            throw new RuntimeException("Failed to migrate tag: " + ex.getMessage(), ex);
        }
    }
}
