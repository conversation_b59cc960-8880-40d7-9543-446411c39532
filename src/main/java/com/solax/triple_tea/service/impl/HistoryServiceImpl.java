package com.solax.triple_tea.service.impl;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.solax.triple_tea.dto.HistoryItemDto;
import com.solax.triple_tea.entity.BusinessEvent;
import com.solax.triple_tea.entity.Customer;
import com.solax.triple_tea.entity.Invoice;
import com.solax.triple_tea.entity.Task;
import com.solax.triple_tea.service.BusinessEventService;
import com.solax.triple_tea.service.HistoryService;
import com.solax.triple_tea.service.InvoiceService;
import com.solax.triple_tea.service.TaskService;

@Service
public class HistoryServiceImpl implements HistoryService {

    private final BusinessEventService businessEventService;
    private final TaskService taskService;
    private final InvoiceService invoiceService;

    public HistoryServiceImpl(BusinessEventService businessEventService, 
                             TaskService taskService, 
                             InvoiceService invoiceService) {
        this.businessEventService = businessEventService;
        this.taskService = taskService;
        this.invoiceService = invoiceService;
    }

    @Override
    public List<HistoryItemDto> getCustomerHistory(Customer customer) {
        List<HistoryItemDto> historyItems = new ArrayList<>();

        // Add business events for the customer
        List<BusinessEvent> businessEvents = businessEventService.findAllByCustomer(customer);
        for (BusinessEvent event : businessEvents) {
            historyItems.add(mapBusinessEventToDto(event));
        }

        // Add tasks (tasks are not directly linked to customers, so we'll include all tasks)
        // You might want to modify this logic based on your business requirements
        List<Task> tasks = taskService.findAll();
        for (Task task : tasks) {
            historyItems.add(mapTaskToDto(task));
        }

        // Add invoices (invoices are not directly linked to customers in current model)
        // You might want to add customer relationship to Invoice entity
        List<Invoice> invoices = invoiceService.getAll();
        for (Invoice invoice : invoices) {
            historyItems.add(mapInvoiceToDto(invoice));
        }

        // Sort by creation date (newest first)
        return historyItems.stream()
                .sorted(Comparator.comparing(HistoryItemDto::getCreatedAt, 
                        Comparator.nullsLast(Comparator.reverseOrder())))
                .collect(Collectors.toList());
    }

    @Override
    public List<HistoryItemDto> getAllHistory() {
        List<HistoryItemDto> historyItems = new ArrayList<>();

        // Add all business events
        List<BusinessEvent> businessEvents = businessEventService.findAll();
        for (BusinessEvent event : businessEvents) {
            historyItems.add(mapBusinessEventToDto(event));
        }

        // Add all tasks
        List<Task> tasks = taskService.findAll();
        for (Task task : tasks) {
            historyItems.add(mapTaskToDto(task));
        }

        // Add all invoices
        List<Invoice> invoices = invoiceService.getAll();
        for (Invoice invoice : invoices) {
            historyItems.add(mapInvoiceToDto(invoice));
        }

        // Sort by creation date (newest first)
        return historyItems.stream()
                .sorted(Comparator.comparing(HistoryItemDto::getCreatedAt, 
                        Comparator.nullsLast(Comparator.reverseOrder())))
                .collect(Collectors.toList());
    }

    private HistoryItemDto mapBusinessEventToDto(BusinessEvent event) {
        return HistoryItemDto.builder()
                .id(event.getId())
                .type("BusinessEvent")
                .title(event.getEventType() != null ? event.getEventType().getName() : "Business Event")
                .realizationDate(event.getRealizationDate())
                .note(event.getNote())
                .user(event.getUser())
                .status(event.getStatus())
                .createdAt(event.getCreatedAt())
                .entitySpecificInfo(event.getEventCategory() != null ? event.getEventCategory().getName() : "")
                .build();
    }

    private HistoryItemDto mapTaskToDto(Task task) {
        return HistoryItemDto.builder()
                .id(task.getId())
                .type("Task")
                .title(task.getTitle() != null ? task.getTitle() : "Task")
                .realizationDate(task.getRealizationDate())
                .note(task.getNote())
                .user(task.getUser())
                .status(task.getStatus())
                .createdAt(task.getCreatedAt())
                .entitySpecificInfo(task.getCategory() != null ? task.getCategory().getName() : "")
                .build();
    }

    private HistoryItemDto mapInvoiceToDto(Invoice invoice) {
        return HistoryItemDto.builder()
                .id(invoice.getId())
                .type("Invoice")
                .title(invoice.getNumber() != null ? "Invoice " + invoice.getNumber() : "Invoice")
                .realizationDate(invoice.getRealizationDate())
                .note(invoice.getNote())
                .user(invoice.getUser())
                .status(invoice.getStatus())
                .createdAt(invoice.getCreatedAt())
                .entitySpecificInfo(invoice.getType() != null ? invoice.getType() : "")
                .build();
    }
}
