/*
*  Copyright (c) 2019. All right reserved
* Created on 2025-05-06 ( Date ISO 2025-05-06 - Time 08:15:48 )
* Generated by Telosys embedded generator ( version 4.2.0 )
*/
package com.solax.triple_tea.service.impl;


import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.solax.triple_tea.entity.BusinessCase;
import com.solax.triple_tea.entity.Customer;
import com.solax.triple_tea.repository.BusinessCaseRepository;
import com.solax.triple_tea.service.BusinessCaseService;

@Service
@Transactional
public class BusinessCaseServiceImpl implements BusinessCaseService {


    private final BusinessCaseRepository businessCaseRepository;

    public BusinessCaseServiceImpl(BusinessCaseRepository businessCaseRepository) {
        this.businessCaseRepository = businessCaseRepository;
    }

    @Override
    public List<BusinessCase> findAll() {
        return businessCaseRepository.findAll();
    }

    @Override
    public List<BusinessCase> findAllByCustomer(Customer customer) {
        return businessCaseRepository.findByCustomer(customer);
    }

    @Override
    public BusinessCase save(BusinessCase businessCase) {
        return businessCaseRepository.save(businessCase);
    }

    @Override
    public Optional<BusinessCase> findById(Long id) {
        return businessCaseRepository.findById(id);
    }

    @Override
    public void deleteById(Long id) {
        businessCaseRepository.deleteById(id);
    }
}
