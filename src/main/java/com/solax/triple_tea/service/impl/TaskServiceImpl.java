package com.solax.triple_tea.service.impl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.WeekFields;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.solax.triple_tea.dto.UserCustomerWorkHoursDto;
import com.solax.triple_tea.dto.UserWorkHoursDto;
import com.solax.triple_tea.dto.WeeklyWorkHoursDto;
import com.solax.triple_tea.entity.Task;
import com.solax.triple_tea.repository.TaskRepository;
import com.solax.triple_tea.service.TaskService;
import com.solax.triple_tea.service.UserService;


@Service
@Transactional
public class TaskServiceImpl implements TaskService {

    private final TaskRepository repository;
    private final UserService userService;

    public TaskServiceImpl(TaskRepository repo, UserService userService) {
        this.repository = repo;
        this.userService = userService;
    }

    @Override
    public List<Task> findAll() {
        return repository.findAll();
    }

    @Override
    public void save(Task task) {
        repository.save(task);
    }

    @Override
    public void delete(Long id) {
        repository.deleteById(id);
    }

    @Override
    public Optional<Task> findById(Long id) {
        return repository.findById(id);
    }

    @Override
    public List<Task> findActiveByCurrentUser() {
        return repository.findByUserAndRealizationDateIsNull(userService.getCurrentUser().getId());
    }

    @Override
    public List<Task> findNotArchived() {
        return repository.findByArchivedAtIsNull();
    }

    @Override
    public List<UserWorkHoursDto> getUserWorkHoursStatistics() {
        System.out.println("=== TaskService.getUserWorkHoursStatistics() called ===");

        List<Object[]> results = repository.findUserWorkHoursStatistics();
        System.out.println("Raw query results count: " + results.size());

        for (int i = 0; i < results.size(); i++) {
            Object[] result = results.get(i);
            System.out.println("Result " + i + ": " + java.util.Arrays.toString(result));
        }

        List<UserWorkHoursDto> dtos = results.stream()
                .filter(result -> result != null && result.length >= 3)
                .map(result -> {
                    String firstName = (String) result[0];
                    String lastName = (String) result[1];
                    Long totalHours = result[2] instanceof Number ? ((Number) result[2]).longValue() : 0L;

                    // Ensure we have valid names
                    if (firstName == null) firstName = "Unknown";
                    if (lastName == null) lastName = "User";

                    System.out.println("Creating DTO: " + firstName + " " + lastName + " - " + totalHours + " hours");
                    return new UserWorkHoursDto(firstName, lastName, totalHours);
                })
                .filter(dto -> dto.getFullName() != null && !dto.getFullName().trim().isEmpty())
                .collect(Collectors.toList());

        System.out.println("Final DTO count: " + dtos.size());
        return dtos;
    }

    @Override
    public List<WeeklyWorkHoursDto> getWeeklyWorkHoursStatistics(LocalDate weekStart) {
        System.out.println("=== TaskService.getWeeklyWorkHoursStatistics() called for week: " + weekStart + " ===");

        // Calculate week end (Sunday)
        LocalDate weekEnd = weekStart.plusDays(7);

        // Convert to LocalDateTime for the query
        LocalDateTime weekStartDateTime = weekStart.atStartOfDay();
        LocalDateTime weekEndDateTime = weekEnd.atStartOfDay();

        System.out.println("Querying tasks from " + weekStartDateTime + " to " + weekEndDateTime);

        List<Object[]> results = repository.findUserWorkHoursStatisticsByWeek(weekStartDateTime, weekEndDateTime);
        System.out.println("Raw weekly query results count: " + results.size());

        for (int i = 0; i < results.size(); i++) {
            Object[] result = results.get(i);
            System.out.println("Weekly Result " + i + ": " + java.util.Arrays.toString(result));
        }

        // Calculate week number
        WeekFields weekFields = WeekFields.of(Locale.getDefault());
        int weekNumber = weekStart.get(weekFields.weekOfYear());
        int year = weekStart.getYear();

        List<WeeklyWorkHoursDto> dtos = results.stream()
                .filter(result -> result != null && result.length >= 3)
                .map(result -> {
                    String firstName = (String) result[0];
                    String lastName = (String) result[1];
                    Long totalHours = result[2] instanceof Number ? ((Number) result[2]).longValue() : 0L;

                    // Ensure we have valid names
                    if (firstName == null) firstName = "Unknown";
                    if (lastName == null) lastName = "User";

                    System.out.println("Creating Weekly DTO: " + firstName + " " + lastName + " - " + totalHours + " hours for week " + weekNumber + "/" + year);

                    return new WeeklyWorkHoursDto(
                        firstName,
                        lastName,
                        totalHours,
                        weekStart,
                        weekStart.plusDays(6), // Week end is 6 days after start
                        year,
                        weekNumber
                    );
                })
                .filter(dto -> dto.getFullName() != null && !dto.getFullName().trim().isEmpty())
                .collect(Collectors.toList());

        System.out.println("Final Weekly DTO count: " + dtos.size());
        return dtos;
    }

    @Override
    public List<WeeklyWorkHoursDto> getWeeklyWorkHoursStatisticsForCurrentUser(LocalDate weekStart) {
        System.out.println("=== TaskService.getWeeklyWorkHoursStatisticsForCurrentUser() called for week: " + weekStart + " ===");

        // Get current user
        Long currentUserId = userService.getCurrentUserId();
        if (currentUserId == null) {
            System.out.println("No current user found, returning empty list");
            return new ArrayList<>();
        }

        // Calculate week end (Sunday)
        LocalDate weekEnd = weekStart.plusDays(7);

        // Convert to LocalDateTime for the query
        LocalDateTime weekStartDateTime = weekStart.atStartOfDay();
        LocalDateTime weekEndDateTime = weekEnd.atStartOfDay();

        System.out.println("Querying tasks for user " + currentUserId + " from " + weekStartDateTime + " to " + weekEndDateTime);

        List<Object[]> results = repository.findUserWorkHoursStatisticsByWeekForUser(weekStartDateTime, weekEndDateTime, currentUserId);
        System.out.println("Raw weekly query results count for current user: " + results.size());

        for (int i = 0; i < results.size(); i++) {
            Object[] result = results.get(i);
            System.out.println("Weekly Result for current user " + i + ": " + java.util.Arrays.toString(result));
        }

        // Calculate week number
        WeekFields weekFields = WeekFields.of(Locale.getDefault());
        int weekNumber = weekStart.get(weekFields.weekOfYear());
        int year = weekStart.getYear();

        List<WeeklyWorkHoursDto> dtos = results.stream()
                .filter(result -> result != null && result.length >= 3)
                .map(result -> {
                    String firstName = (String) result[0];
                    String lastName = (String) result[1];
                    Long totalHours = result[2] instanceof Number ? ((Number) result[2]).longValue() : 0L;

                    // Ensure we have valid names
                    if (firstName == null) firstName = "Unknown";
                    if (lastName == null) lastName = "User";

                    System.out.println("Creating Weekly DTO for current user: " + firstName + " " + lastName + " - " + totalHours + " hours for week " + weekNumber + "/" + year);

                    return new WeeklyWorkHoursDto(
                        firstName,
                        lastName,
                        totalHours,
                        weekStart,
                        weekStart.plusDays(6), // Week end is 6 days after start
                        year,
                        weekNumber
                    );
                })
                .filter(dto -> dto.getFullName() != null && !dto.getFullName().trim().isEmpty())
                .collect(Collectors.toList());

        System.out.println("Final Weekly DTO count for current user: " + dtos.size());
        return dtos;
    }

    @Override
    public List<UserCustomerWorkHoursDto> getUserCustomerWorkHoursStatistics() {
        System.out.println("=== TaskService.getUserCustomerWorkHoursStatistics() called ===");

        List<Object[]> results = repository.findUserCustomerWorkHoursStatistics();
        System.out.println("Raw customer query results count: " + results.size());

        for (int i = 0; i < results.size(); i++) {
            Object[] result = results.get(i);
            System.out.println("Customer Result " + i + ": " + java.util.Arrays.toString(result));
        }

        List<UserCustomerWorkHoursDto> dtos = results.stream()
                .filter(result -> result != null && result.length >= 5)
                .map(result -> {
                    String firstName = (String) result[0];
                    String lastName = (String) result[1];
                    String customerName = (String) result[2];
                    Long customerId = result[3] instanceof Number ? ((Number) result[3]).longValue() : 0L;
                    Long totalHours = result[4] instanceof Number ? ((Number) result[4]).longValue() : 0L;

                    // Ensure we have valid names
                    if (firstName == null) firstName = "Unknown";
                    if (lastName == null) lastName = "User";

                    System.out.println("Creating Customer DTO: " + firstName + " " + lastName + " - " + customerName + " - " + totalHours + " hours");
                    return new UserCustomerWorkHoursDto(firstName, lastName, customerName, customerId, totalHours);
                })
                .filter(dto -> dto.getFullName() != null && !dto.getFullName().trim().isEmpty())
                .collect(Collectors.toList());

        System.out.println("Final Customer DTO count: " + dtos.size());
        return dtos;
    }

    @Override
    public List<UserCustomerWorkHoursDto> getUserCustomerWorkHoursStatisticsByWeek(LocalDate weekStart) {
        System.out.println("=== TaskService.getUserCustomerWorkHoursStatisticsByWeek() called for week: " + weekStart + " ===");

        // Calculate week end (Sunday)
        LocalDate weekEnd = weekStart.plusDays(7);

        // Convert to LocalDateTime for the query
        LocalDateTime weekStartDateTime = weekStart.atStartOfDay();
        LocalDateTime weekEndDateTime = weekEnd.atStartOfDay();

        System.out.println("Querying customer tasks from " + weekStartDateTime + " to " + weekEndDateTime);

        List<Object[]> results = repository.findUserCustomerWorkHoursStatisticsByWeek(weekStartDateTime, weekEndDateTime);
        System.out.println("Raw weekly customer query results count: " + results.size());

        for (int i = 0; i < results.size(); i++) {
            Object[] result = results.get(i);
            System.out.println("Weekly Customer Result " + i + ": " + java.util.Arrays.toString(result));
        }

        List<UserCustomerWorkHoursDto> dtos = results.stream()
                .filter(result -> result != null && result.length >= 5)
                .map(result -> {
                    String firstName = (String) result[0];
                    String lastName = (String) result[1];
                    String customerName = (String) result[2];
                    Long customerId = result[3] instanceof Number ? ((Number) result[3]).longValue() : 0L;
                    Long totalHours = result[4] instanceof Number ? ((Number) result[4]).longValue() : 0L;

                    // Ensure we have valid names
                    if (firstName == null) firstName = "Unknown";
                    if (lastName == null) lastName = "User";

                    System.out.println("Creating Weekly Customer DTO: " + firstName + " " + lastName + " - " + customerName + " - " + totalHours + " hours");
                    return new UserCustomerWorkHoursDto(firstName, lastName, customerName, customerId, totalHours);
                })
                .filter(dto -> dto.getFullName() != null && !dto.getFullName().trim().isEmpty())
                .collect(Collectors.toList());

        System.out.println("Final Weekly Customer DTO count: " + dtos.size());
        return dtos;
    }

    @Override
    public List<UserCustomerWorkHoursDto> getUserCustomerWorkHoursStatisticsByWeekForCurrentUser(LocalDate weekStart) {
        System.out.println("=== TaskService.getUserCustomerWorkHoursStatisticsByWeekForCurrentUser() called for week: " + weekStart + " ===");

        // Get current user
        Long currentUserId = userService.getCurrentUserId();
        if (currentUserId == null) {
            System.out.println("No current user found, returning empty list");
            return new ArrayList<>();
        }

        // Calculate week end (Sunday)
        LocalDate weekEnd = weekStart.plusDays(7);

        // Convert to LocalDateTime for the query
        LocalDateTime weekStartDateTime = weekStart.atStartOfDay();
        LocalDateTime weekEndDateTime = weekEnd.atStartOfDay();

        System.out.println("Querying customer tasks for user " + currentUserId + " from " + weekStartDateTime + " to " + weekEndDateTime);

        List<Object[]> results = repository.findUserCustomerWorkHoursStatisticsByWeekForUser(weekStartDateTime, weekEndDateTime, currentUserId);
        System.out.println("Raw weekly customer query results count for current user: " + results.size());

        for (int i = 0; i < results.size(); i++) {
            Object[] result = results.get(i);
            System.out.println("Weekly Customer Result for current user " + i + ": " + java.util.Arrays.toString(result));
        }

        List<UserCustomerWorkHoursDto> dtos = results.stream()
                .filter(result -> result != null && result.length >= 5)
                .map(result -> {
                    String firstName = (String) result[0];
                    String lastName = (String) result[1];
                    String customerName = (String) result[2];
                    Long customerId = result[3] instanceof Number ? ((Number) result[3]).longValue() : 0L;
                    Long totalHours = result[4] instanceof Number ? ((Number) result[4]).longValue() : 0L;

                    // Ensure we have valid names
                    if (firstName == null) firstName = "Unknown";
                    if (lastName == null) lastName = "User";

                    System.out.println("Creating Weekly Customer DTO for current user: " + firstName + " " + lastName + " - " + customerName + " - " + totalHours + " hours");
                    return new UserCustomerWorkHoursDto(firstName, lastName, customerName, customerId, totalHours);
                })
                .filter(dto -> dto.getFullName() != null && !dto.getFullName().trim().isEmpty())
                .collect(Collectors.toList());

        System.out.println("Final Weekly Customer DTO count for current user: " + dtos.size());
        return dtos;
    }
}
