package com.solax.triple_tea.service;

import java.util.List;
import java.util.Optional;

import com.solax.triple_tea.entity.User;

public interface UserService {

    List<User> findAll();

    void save(User user);

    void delete(Long id);

    Optional<User> findById(Long id);

    void changePassword(Long userId, String currentPassword, String newPassword, String confirmPassword);

    User findByEmail(String email);

    User getCurrentUser();

    Long getCurrentUserId();

    List<User> findEnabled();
}
