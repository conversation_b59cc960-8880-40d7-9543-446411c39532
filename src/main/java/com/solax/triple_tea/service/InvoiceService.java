package com.solax.triple_tea.service;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import com.solax.triple_tea.entity.Customer;
import com.solax.triple_tea.entity.Invoice;

public interface InvoiceService  {

    Invoice create(Invoice d);

    Invoice update(Invoice d);

    Invoice getOne(long id) ;

    List<Invoice> getAll();

    long getTotal();

    void delete(long id);

    Page<Invoice> findAllPaginate(Pageable pageable);

	Page<Invoice> findAllSpecification(Specification<Invoice> specs, Pageable pageable);

	List<Invoice> findAllByCustomer(Customer customer);
}
