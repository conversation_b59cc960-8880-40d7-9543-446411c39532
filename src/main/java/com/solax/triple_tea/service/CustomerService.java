package com.solax.triple_tea.service;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.solax.triple_tea.entity.Customer;

public interface CustomerService {

    List<Customer> findAll();

    List<Customer> findEnabled();

    Customer save(Customer customer);

    Optional<Customer> findById(Long id);

    void deleteById(Long id);

    Page<Customer> findAllWithFilters(Pageable pageable, String nameFilter, String icoFilter,
                                     String cityFilter, String countryFilter, String typeFilter, String subtypeFilter, List<Long> tagFilters, Long responsiblePersonFilter);
}
