package com.solax.triple_tea.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ModelAttribute;

import com.solax.triple_tea.entity.User;
import com.solax.triple_tea.service.UserService;


@ControllerAdvice
public class GlobalModelAttributes {

    private final UserService userService;

    @Value("${project.version}")
    private String projectVersion;

    public GlobalModelAttributes(UserService userService) {
        this.userService = userService;
    }

    @ModelAttribute
    public void addCurrentUser(Model model, @AuthenticationPrincipal UserDetails principal) {
        if (principal != null) {
            User currentUser = userService.findByEmail(principal.getUsername());
            model.addAttribute("currentUser", currentUser);
        }
    }

    @ModelAttribute("projectVersion")
    public String getVersion() {
        return projectVersion;
    }
}

