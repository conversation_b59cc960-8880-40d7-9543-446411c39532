package com.solax.triple_tea.config;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import com.solax.triple_tea.entity.Customer;
import com.solax.triple_tea.entity.Task;
import com.solax.triple_tea.entity.User;
import com.solax.triple_tea.repository.CustomerRepository;
import com.solax.triple_tea.repository.TaskRepository;
import com.solax.triple_tea.repository.UserRepository;


@Configuration
public class SecurityConfig {

    private final UserDetailsService userDetailsService;

    public SecurityConfig(UserDetailsService userDetailsService) {
        this.userDetailsService = userDetailsService;
    }

    @Bean
    public BCryptPasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public DaoAuthenticationProvider authenticationProvider() {
        DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
        authProvider.setUserDetailsService(userDetailsService);
        authProvider.setPasswordEncoder(passwordEncoder());
        return authProvider;
    }

    @Bean
    public CommandLineRunner init(UserRepository userRepository, TaskRepository taskRepository, CustomerRepository customerRepository, BCryptPasswordEncoder encoder) {
        return args -> {
            // Create admin user
            User admin = userRepository.findByEmail("<EMAIL>");
            if (admin == null) {
                admin = new User();
                admin.setEmail("<EMAIL>");
                admin.setFirstName("Admin");
                admin.setLastName("User");
                admin.setPassword(encoder.encode("admin"));
                admin.setEnabled(true);
                admin.setCanViewAllWorkHours(true); // Admin can see all work hours
                admin = userRepository.save(admin);
            } else if (!admin.isCanViewAllWorkHours()) {
                // Update existing admin to have view all permission
                admin.setCanViewAllWorkHours(true);
                admin = userRepository.save(admin);
            }

            // Create sample users for the graph
            User john = userRepository.findByEmail("<EMAIL>");
            if (john == null) {
                john = new User();
                john.setEmail("<EMAIL>");
                john.setFirstName("Ján");
                john.setLastName("Novák");
                john.setPassword(encoder.encode("password"));
                john.setEnabled(true);
                john = userRepository.save(john);
            }

            User maria = userRepository.findByEmail("<EMAIL>");
            if (maria == null) {
                maria = new User();
                maria.setEmail("<EMAIL>");
                maria.setFirstName("Mária");
                maria.setLastName("Svobodová");
                maria.setPassword(encoder.encode("password"));
                maria.setEnabled(true);
                maria = userRepository.save(maria);
            }

            User peter = userRepository.findByEmail("<EMAIL>");
            if (peter == null) {
                peter = new User();
                peter.setEmail("<EMAIL>");
                peter.setFirstName("Peter");
                peter.setLastName("Kováč");
                peter.setPassword(encoder.encode("password"));
                peter.setEnabled(true);
                peter = userRepository.save(peter);
            }

            User anna = userRepository.findByEmail("<EMAIL>");
            if (anna == null) {
                anna = new User();
                anna.setEmail("<EMAIL>");
                anna.setFirstName("Anna");
                anna.setLastName("Horváthová");
                anna.setPassword(encoder.encode("password"));
                anna.setEnabled(true);
                anna = userRepository.save(anna);
            }

            // Create sample customers
            createSampleCustomersIfNotExist(customerRepository);

            // Create sample tasks with actual duration hours for the graph
            createSampleTasksIfNotExist(taskRepository, customerRepository, admin, john, maria, peter, anna);
        };
    }

    private void createSampleCustomersIfNotExist(CustomerRepository customerRepository) {
        // Check if sample customers already exist
        long customerCount = customerRepository.count();
        System.out.println("Current customer count: " + customerCount);

        if (customerCount > 0) {
            System.out.println("Sample customers already exist, skipping creation");
            return; // Sample data already exists
        }

        System.out.println("Creating sample customers...");

        // Create sample customers
        Customer techCorp = new Customer();
        techCorp.setName("TechCorp s.r.o.");
        techCorp.setCompanyOrIndividual("company");
        techCorp.setIco("12345678");
        techCorp.setEnabled(true);
        techCorp.setNote("Technologická spoločnosť");
        customerRepository.save(techCorp);

        Customer designStudio = new Customer();
        designStudio.setName("Design Studio");
        designStudio.setCompanyOrIndividual("company");
        designStudio.setIco("87654321");
        designStudio.setEnabled(true);
        designStudio.setNote("Dizajnérske štúdio");
        customerRepository.save(designStudio);

        Customer consultingGroup = new Customer();
        consultingGroup.setName("Consulting Group a.s.");
        consultingGroup.setCompanyOrIndividual("company");
        consultingGroup.setIco("11223344");
        consultingGroup.setEnabled(true);
        consultingGroup.setNote("Poradenská spoločnosť");
        customerRepository.save(consultingGroup);

        Customer startupInc = new Customer();
        startupInc.setName("Startup Inc.");
        startupInc.setCompanyOrIndividual("company");
        startupInc.setIco("44332211");
        startupInc.setEnabled(true);
        startupInc.setNote("Mladá technologická spoločnosť");
        customerRepository.save(startupInc);

        System.out.println("Sample customers created successfully!");
    }

    private void createSampleTasksIfNotExist(TaskRepository taskRepository, CustomerRepository customerRepository, User admin, User john, User maria, User peter, User anna) {
        LocalDateTime now = LocalDateTime.now();

        // Check if sample tasks already exist
        long taskCount = taskRepository.count();
        System.out.println("Current task count: " + taskCount);

        if (taskCount > 0) {
            System.out.println("Sample tasks already exist, skipping creation");
            return; // Sample data already exists
        }

        System.out.println("Creating sample tasks...");

        // Get customers for task assignment
        List<Customer> customers = customerRepository.findAll();
        Customer techCorp = customers.stream().filter(c -> c.getName().equals("TechCorp s.r.o.")).findFirst().orElse(null);
        Customer designStudio = customers.stream().filter(c -> c.getName().equals("Design Studio")).findFirst().orElse(null);
        Customer consultingGroup = customers.stream().filter(c -> c.getName().equals("Consulting Group a.s.")).findFirst().orElse(null);
        Customer startupInc = customers.stream().filter(c -> c.getName().equals("Startup Inc.")).findFirst().orElse(null);

        // Tasks for John (45 hours total)
        createTask(taskRepository, "Implementácia API", "Vytvorenie REST API pre zákazníkov", john, admin, 15L, now.minusDays(10), techCorp);
        createTask(taskRepository, "Databázový dizajn", "Návrh databázovej štruktúry", john, admin, 20L, now.minusDays(8), techCorp);
        createTask(taskRepository, "Testovanie", "Unit testy pre API", john, admin, 10L, now.minusDays(5), startupInc);

        // Tasks for Maria (32 hours total)
        createTask(taskRepository, "UI dizajn", "Návrh používateľského rozhrania", maria, admin, 18L, now.minusDays(9), designStudio);
        createTask(taskRepository, "Frontend implementácia", "Implementácia React komponentov", maria, admin, 14L, now.minusDays(6), designStudio);

        // Tasks for Peter (28 hours total)
        createTask(taskRepository, "DevOps setup", "Konfigurácia CI/CD pipeline", peter, admin, 12L, now.minusDays(7), techCorp);
        createTask(taskRepository, "Dokumentácia", "Technická dokumentácia projektu", peter, admin, 16L, now.minusDays(4), consultingGroup);

        // Tasks for Anna (38 hours total)
        createTask(taskRepository, "Analýza požiadaviek", "Analýza business požiadaviek", anna, admin, 22L, now.minusDays(12), consultingGroup);
        createTask(taskRepository, "Projektové riadenie", "Koordinácia tímu a plánování", anna, admin, 16L, now.minusDays(3), startupInc);

        // Tasks for Admin (25 hours total) - some without customers
        createTask(taskRepository, "Code review", "Kontrola kvality kódu", admin, admin, 15L, now.minusDays(2), null);
        createTask(taskRepository, "Architektúra", "Návrh systémovej architektúry", admin, admin, 10L, now.minusDays(1), null);

        System.out.println("Sample tasks created successfully!");
    }

    private void createTask(TaskRepository taskRepository, String title, String description, User assignedUser, User createdBy, Long actualHours, LocalDateTime realizationDate, Customer customer) {
        try {
            Task task = new Task();

            // Fields from Task entity
            task.setTitle(title);
            task.setDescription(description);
            task.setCreatedBy(createdBy);
            task.setCustomer(customer);
            task.setCreatedAt(LocalDateTime.now().minusDays(15));
            task.setStartedAt(realizationDate.minusHours(actualHours));
            task.setEndedAt(realizationDate);
            task.setActualDurationHours(actualHours);
            task.setPriority(1);
            task.setIsCurrentlyPaused(false);
            task.setTotalPausedDurationSeconds(0L);

            // Fields from HistoryOverview parent class
            task.setUser(assignedUser);
            task.setRealizationDate(realizationDate);
            task.setNote("Sample task created during initialization");

            Task savedTask = taskRepository.save(task);
            String customerName = customer != null ? customer.getName() : "No customer";
            System.out.println("Created task: " + savedTask.getTitle() + " for user: " + assignedUser.getFullName() + " and customer: " + customerName);
        } catch (Exception e) {
            System.err.println("Error creating task '" + title + "': " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .authorizeHttpRequests(auth -> auth
                .requestMatchers("/login", "/css/**", "/js/**").permitAll()
                .anyRequest().authenticated()
            )
            .formLogin(form -> form
                .loginPage("/login")
                .defaultSuccessUrl("/home", true)
                .permitAll()
            )
            .logout(logout -> logout
                .logoutRequestMatcher(new AntPathRequestMatcher("/logout", "GET"))
                .logoutSuccessUrl("/login?logout")
                .invalidateHttpSession(true)
                .deleteCookies("JSESSIONID")
                .permitAll()
            );

        return http.build();
    }
}
