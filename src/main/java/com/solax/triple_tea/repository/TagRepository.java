package com.solax.triple_tea.repository;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import com.solax.triple_tea.entity.Tag;
import com.solax.triple_tea.entity.Task;

@Repository
public interface TagRepository extends JpaRepository<Tag, Long> , JpaSpecificationExecutor<Task> {

    Optional<Tag> findByName(String name);
}
