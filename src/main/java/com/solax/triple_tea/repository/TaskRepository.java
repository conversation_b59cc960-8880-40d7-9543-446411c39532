package com.solax.triple_tea.repository;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.solax.triple_tea.entity.Task;
import com.solax.triple_tea.entity.User;

@Repository
public interface TaskRepository  extends JpaRepository<Task, Long> , JpaSpecificationExecutor<Task> {

    @Query("SELECT task FROM Task task WHERE task.user.id = :userId AND task.realizationDate IS NULL "
        + "ORDER BY task.priority")
    List<Task> findByUserAndRealizationDateIsNull(Long userId);

    List<Task> findByArchivedAtIsNull();

    @Query("SELECT t.user.firstName, t.user.lastName, COALESCE(SUM(t.actualDurationHours), 0) " +
           "FROM Task t " +
           "WHERE t.user IS NOT NULL AND t.actualDurationHours IS NOT NULL " +
           "GROUP BY t.user.id, t.user.firstName, t.user.lastName " +
           "ORDER BY SUM(t.actualDurationHours) DESC")
    List<Object[]> findUserWorkHoursStatistics();

    @Query("SELECT t.user.firstName, t.user.lastName, COALESCE(SUM(t.actualDurationHours), 0) " +
           "FROM Task t " +
           "WHERE t.user IS NOT NULL AND t.actualDurationHours IS NOT NULL " +
           "AND t.realizationDate >= :weekStart AND t.realizationDate < :weekEnd " +
           "GROUP BY t.user.id, t.user.firstName, t.user.lastName " +
           "ORDER BY SUM(t.actualDurationHours) DESC")
    List<Object[]> findUserWorkHoursStatisticsByWeek(@Param("weekStart") LocalDateTime weekStart,
                                                     @Param("weekEnd") LocalDateTime weekEnd);

    @Query("SELECT t.user.firstName, t.user.lastName, COALESCE(SUM(t.actualDurationHours), 0) " +
           "FROM Task t " +
           "WHERE t.user IS NOT NULL AND t.actualDurationHours IS NOT NULL " +
           "AND t.realizationDate >= :weekStart AND t.realizationDate < :weekEnd " +
           "AND t.user.id = :userId " +
           "GROUP BY t.user.id, t.user.firstName, t.user.lastName " +
           "ORDER BY SUM(t.actualDurationHours) DESC")
    List<Object[]> findUserWorkHoursStatisticsByWeekForUser(@Param("weekStart") LocalDateTime weekStart,
                                                           @Param("weekEnd") LocalDateTime weekEnd,
                                                           @Param("userId") Long userId);

    @Query("SELECT t.user.firstName, t.user.lastName, " +
           "COALESCE(c.name, 'Bez zákazníka'), " +
           "COALESCE(c.id, 0), " +
           "COALESCE(SUM(t.actualDurationHours), 0) " +
           "FROM Task t " +
           "LEFT JOIN t.customer c " +
           "WHERE t.user IS NOT NULL AND t.actualDurationHours IS NOT NULL " +
           "GROUP BY t.user.id, t.user.firstName, t.user.lastName, c.id, c.name " +
           "ORDER BY t.user.firstName, t.user.lastName, c.name")
    List<Object[]> findUserCustomerWorkHoursStatistics();

    @Query("SELECT t.user.firstName, t.user.lastName, " +
           "COALESCE(c.name, 'Bez zákazníka'), " +
           "COALESCE(c.id, 0), " +
           "COALESCE(SUM(t.actualDurationHours), 0) " +
           "FROM Task t " +
           "LEFT JOIN t.customer c " +
           "WHERE t.user IS NOT NULL AND t.actualDurationHours IS NOT NULL " +
           "AND t.realizationDate >= :weekStart AND t.realizationDate < :weekEnd " +
           "GROUP BY t.user.id, t.user.firstName, t.user.lastName, c.id, c.name " +
           "ORDER BY t.user.firstName, t.user.lastName, c.name")
    List<Object[]> findUserCustomerWorkHoursStatisticsByWeek(@Param("weekStart") LocalDateTime weekStart,
                                                            @Param("weekEnd") LocalDateTime weekEnd);

    @Query("SELECT t.user.firstName, t.user.lastName, " +
           "COALESCE(c.name, 'Bez zákazníka'), " +
           "COALESCE(c.id, 0), " +
           "COALESCE(SUM(t.actualDurationHours), 0) " +
           "FROM Task t " +
           "LEFT JOIN t.customer c " +
           "WHERE t.user IS NOT NULL AND t.actualDurationHours IS NOT NULL " +
           "AND t.realizationDate >= :weekStart AND t.realizationDate < :weekEnd " +
           "AND t.user.id = :userId " +
           "GROUP BY t.user.id, t.user.firstName, t.user.lastName, c.id, c.name " +
           "ORDER BY t.user.firstName, t.user.lastName, c.name")
    List<Object[]> findUserCustomerWorkHoursStatisticsByWeekForUser(@Param("weekStart") LocalDateTime weekStart,
                                                                   @Param("weekEnd") LocalDateTime weekEnd,
                                                                   @Param("userId") Long userId);
}
