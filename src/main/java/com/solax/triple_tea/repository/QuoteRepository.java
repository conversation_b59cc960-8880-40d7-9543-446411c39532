package com.solax.triple_tea.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import com.solax.triple_tea.entity.Customer;
import com.solax.triple_tea.entity.Quote;

@Repository
public interface QuoteRepository extends JpaRepository<Quote, Long>, JpaSpecificationExecutor<Quote> {

	List<Quote> findByCustomer(Customer customer);
}
