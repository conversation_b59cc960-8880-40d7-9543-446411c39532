package com.solax.triple_tea.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.solax.triple_tea.entity.User;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    User findByEmail(String email);

    @Query("""
            SELECT u FROM User u WHERE u.firstName = :firstName AND u.lastName = :lastName
        """)
    User findByFullName(String firstName, String lastName);

    List<User> findByEnabled(boolean enabled);
}
