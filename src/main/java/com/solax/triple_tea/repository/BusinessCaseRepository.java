package com.solax.triple_tea.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import com.solax.triple_tea.entity.BusinessCase;
import com.solax.triple_tea.entity.Customer;

@Repository
public interface BusinessCaseRepository  extends JpaRepository<BusinessCase, Long> , JpaSpecificationExecutor<BusinessCase> {

    List<BusinessCase> findByCustomer(Customer customer);
}
