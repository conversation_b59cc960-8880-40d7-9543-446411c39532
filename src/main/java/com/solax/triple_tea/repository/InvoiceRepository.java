package com.solax.triple_tea.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.solax.triple_tea.entity.Customer;
import com.solax.triple_tea.entity.Invoice;

@Repository
public interface InvoiceRepository  extends JpaRepository<Invoice, Long> , JpaSpecificationExecutor<Invoice> {

	List<Invoice> findByCustomer(Customer customer);

	@Query("SELECT i.number FROM Invoice i WHERE i.number LIKE :pattern ORDER BY i.number DESC")
	List<String> findNumbersByPattern(@Param("pattern") String pattern);

	@Query("SELECT i.number FROM Invoice i WHERE i.number LIKE :pattern ORDER BY i.number DESC LIMIT 1")
	Optional<String> findLatestNumberByPattern(@Param("pattern") String pattern);
}
