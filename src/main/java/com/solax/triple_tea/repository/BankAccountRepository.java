package com.solax.triple_tea.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import com.solax.triple_tea.entity.BankAccount;

@Repository
public interface BankAccountRepository  extends JpaRepository<BankAccount, Long> , JpaSpecificationExecutor<BankAccount> {

}
