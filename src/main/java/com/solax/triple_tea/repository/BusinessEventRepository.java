package com.solax.triple_tea.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.solax.triple_tea.entity.BusinessEvent;
import com.solax.triple_tea.entity.Customer;

@Repository
public interface BusinessEventRepository extends JpaRepository<BusinessEvent, Long> {

    List<BusinessEvent> findByCustomer(Customer customer);
}
