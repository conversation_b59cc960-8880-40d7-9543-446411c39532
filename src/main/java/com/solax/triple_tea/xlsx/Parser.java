package com.solax.triple_tea.xlsx;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import com.solax.triple_tea.entity.Address;
import com.solax.triple_tea.entity.BankAccount;
import com.solax.triple_tea.entity.BusinessCase;
import com.solax.triple_tea.entity.BusinessEvent;
import com.solax.triple_tea.entity.Category;
import com.solax.triple_tea.entity.Contact;
import com.solax.triple_tea.entity.Contract;
import com.solax.triple_tea.entity.Customer;
import com.solax.triple_tea.entity.CustomerSubType;
import com.solax.triple_tea.entity.CustomerType;
import com.solax.triple_tea.entity.EventCategory;
import com.solax.triple_tea.entity.EventType;
import com.solax.triple_tea.entity.Invoice;
import com.solax.triple_tea.entity.Product;
import com.solax.triple_tea.entity.ProgressStatus;
import com.solax.triple_tea.entity.Tag;
import com.solax.triple_tea.entity.Task;
import com.solax.triple_tea.entity.User;
import com.solax.triple_tea.repository.AddressRepository;
import com.solax.triple_tea.repository.BankAccountRepository;
import com.solax.triple_tea.repository.BusinessCaseRepository;
import com.solax.triple_tea.repository.BusinessEventRepository;
import com.solax.triple_tea.repository.CategoryRepository;
import com.solax.triple_tea.repository.ContactRepository;
import com.solax.triple_tea.repository.ContractRepository;
import com.solax.triple_tea.repository.CustomerRepository;
import com.solax.triple_tea.repository.CustomerSubtypeRepository;
import com.solax.triple_tea.repository.CustomerTypeRepository;
import com.solax.triple_tea.repository.EventCategoryRepository;
import com.solax.triple_tea.repository.EventTypeRepository;
import com.solax.triple_tea.repository.InvoiceRepository;
import com.solax.triple_tea.repository.ProductRepository;
import com.solax.triple_tea.repository.TagRepository;
import com.solax.triple_tea.repository.TaskRepository;
import com.solax.triple_tea.repository.UserRepository;
import lombok.extern.log4j.Log4j2;


@Log4j2
@Service
public class Parser {

    private final BCryptPasswordEncoder encoder;

    private final UserRepository userRepository;
    private final CustomerRepository customerRepository;
    private final TagRepository tagRepository;
    private final CustomerSubtypeRepository customerSubtypeRepository;
    private final CustomerTypeRepository customerTypeRepository;
    private final AddressRepository addressRepository;
    private final ContactRepository contactRepository;
    private final BankAccountRepository bankAccountRepository;
    private final ProductRepository productRepository;
    private final InvoiceRepository invoiceRepository;
    private final BusinessCaseRepository businessCaseRepository;
    private final BusinessEventRepository businessEventRepository;
    private final EventTypeRepository eventTypeRepository;
    private final EventCategoryRepository eventCategoryRepository;
    private final TaskRepository taskRepository;
    private final CategoryRepository categoryRepository;
    private final ContractRepository contractRepository;

    private final Map<String, CustomerType> customerTypeCache = new HashMap<>();
    private final Map<String, CustomerSubType> customerSubTypeCache = new HashMap<>();
    private final Map<String, Tag> tagCache = new HashMap<>();
    private final Map<String, EventType> eventTypeCache = new HashMap<>();
    private final Map<String, EventCategory> eventCategoryCache = new HashMap<>();
    private final Map<String, Customer> customerCache = new HashMap<>();
    private final Map<String, User> userCache = new HashMap<>();

    public Parser(final BCryptPasswordEncoder encoder, final UserRepository userRepository, final CustomerRepository customerRepository,
                  final TagRepository tagRepository,
                  final CustomerSubtypeRepository customerSubtypeRepository,
                  final CustomerTypeRepository customerTypeRepository,
                  final AddressRepository addressRepository,
                  final ContactRepository contactRepository, final BankAccountRepository bankAccountRepository,
                  final ProductRepository productRepository, final InvoiceRepository invoiceRepository,
                  final BusinessCaseRepository businessCaseRepository, final BusinessEventRepository businessEventRepository,
                  final EventTypeRepository eventTypeRepository, final EventCategoryRepository eventCategoryRepository,
                  final TaskRepository taskRepository, final CategoryRepository categoryRepository,
                  final ContractRepository contractRepository) {
        this.encoder = encoder;
        this.userRepository = userRepository;
        this.customerRepository = customerRepository;
        this.tagRepository = tagRepository;
        this.customerSubtypeRepository = customerSubtypeRepository;
        this.customerTypeRepository = customerTypeRepository;
        this.addressRepository = addressRepository;
        this.contactRepository = contactRepository;
        this.bankAccountRepository = bankAccountRepository;
        this.productRepository = productRepository;
        this.invoiceRepository = invoiceRepository;
        this.businessCaseRepository = businessCaseRepository;
        this.businessEventRepository = businessEventRepository;
        this.eventTypeRepository = eventTypeRepository;
        this.eventCategoryRepository = eventCategoryRepository;
        this.taskRepository = taskRepository;
        this.categoryRepository = categoryRepository;
        this.contractRepository = contractRepository;
    }

    public void parseUsers(final String pathToFile) {

        try (InputStream inputStream = new FileInputStream(pathToFile);
            Workbook workbook = WorkbookFactory.create(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);
            List<User> users = new ArrayList<>();

            for (Row row : sheet) {

                if (row.getCell(1).getStringCellValue().isEmpty()) {
                    break;
                }

                User user = new User();
                user.setEnabled(row.getCell(0).getNumericCellValue() == 1);
                user.setLastName(row.getCell(1).getStringCellValue());
                user.setFirstName(row.getCell(2).getStringCellValue());
                user.setEmail(row.getCell(3).getStringCellValue());
                user.setPassword(encoder.encode("heslo"));
                users.add(user);
            }

            userRepository.saveAll(users);

        } catch (IOException e) {
            log.error(e.getMessage());
        }
    }

    public void parsePartners(final String pathToFile) {

        try (InputStream inputStream = new FileInputStream(pathToFile);
            Workbook workbook = WorkbookFactory.create(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0); // first sheet
            List<Customer> customers = new ArrayList<>();
            boolean isFirst = true;

            // Pre-load all customer types and subtypes to avoid repeated queries
            List<CustomerType> allCustomerTypes = customerTypeRepository.findAll();
            for (CustomerType type : allCustomerTypes) {
                customerTypeCache.put(type.getName(), type);
            }
            
            List<CustomerSubType> allCustomerSubTypes = customerSubtypeRepository.findAll();
            for (CustomerSubType subType : allCustomerSubTypes) {
                customerSubTypeCache.put(subType.getName(), subType);
            }
            
            // Pre-load all tags
            List<Tag> allTags = tagRepository.findAll();
            for (Tag tag : allTags) {
                tagCache.put(tag.getName(), tag);
            }

            for (Row row : sheet) {

                if (isFirst) {
                    isFirst = false;
                    continue;
                }

                if (row.getCell(1).getStringCellValue().isEmpty()) {
                    break;
                }

                Customer customer = new Customer();
                customer.setName(row.getCell(0).getStringCellValue());
                customer.setCompanyOrIndividual(row.getCell(1).getStringCellValue());

                Address address = new Address();
                address.setStreet(row.getCell(3).getStringCellValue());
                address.setZip(row.getCell(4).getStringCellValue());
                address.setCity(row.getCell(5).getStringCellValue());
                address.setCountryCode(row.getCell(6).getStringCellValue());
                address.setCountry(row.getCell(7).getStringCellValue());

                if (hasAtLeastOneNonBlankStringField(address)) {
                    address = addressRepository.save(address);
                    customer.setBillingAddress(address);
                }

                customer.setIco(row.getCell(8).getStringCellValue());
                customer.setDic(row.getCell(9).getStringCellValue());
                customer.setIcDph(row.getCell(10).getStringCellValue());

                // Handle CustomerType with cache
                String customerTypeName = row.getCell(11).getStringCellValue();
                if (!customerTypeName.isEmpty()) {
                    CustomerType customerType = customerTypeCache.get(customerTypeName);
                    if (customerType == null) {
                        customerType = new CustomerType();
                        customerType.setName(customerTypeName);
                        customerType = customerTypeRepository.save(customerType);
                        customerTypeCache.put(customerTypeName, customerType);
                    }
                    customer.setCustomerType(customerType);
                }

                // Handle CustomerSubType with cache
                String customerSubTypeName = row.getCell(12).getStringCellValue();
                if (!customerSubTypeName.isEmpty()) {
                    CustomerSubType customerSubType = customerSubTypeCache.get(customerSubTypeName);
                    if (customerSubType == null) {
                        customerSubType = new CustomerSubType();
                        customerSubType.setName(customerSubTypeName);
                        customerSubType = customerSubtypeRepository.save(customerSubType);
                        customerSubTypeCache.put(customerSubTypeName, customerSubType);
                    }
                    customer.setCustomerSubtype(customerSubType);
                }

                customer.setNote(row.getCell(13).getStringCellValue());

                if (!row.getCell(14).getStringCellValue().isEmpty()) {
                    String[] name = row.getCell(14).getStringCellValue().split(" ");
                    if (name.length > 1) {
                        User user = userRepository.findByFullName(name[1], name[0]);
                        customer.setResponsiblePerson(user);
                    }
                }

                // Handle Tags with cache
                List<String> tagNames = List.of(row.getCell(15).getStringCellValue().split(" "));
                Set<Tag> tags = new HashSet<>();
                tagNames.forEach(tagName -> {
                    if (tagName.isBlank()) {
                        return;
                    }

                    Tag tag = tagCache.get(tagName);
                    if (tag == null) {
                        Optional<Tag> optionalTag = tagRepository.findByName(tagName);
                        if (optionalTag.isPresent()) {
                            tag = optionalTag.get();
                        } else {
                            tag = new Tag();
                            tag.setName(tagName);
                            tag = tagRepository.save(tag);
                        }
                        tagCache.put(tagName, tag);
                    }
                    tags.add(tag);
                });
                customer.setTags(tags);

                BankAccount bankAccount = new BankAccount();
                bankAccount.setPrefix(row.getCell(16).getStringCellValue());
                bankAccount.setNumber(row.getCell(17).getStringCellValue());
                bankAccount.setBankCode(row.getCell(18).getStringCellValue());
                bankAccount.setIban(row.getCell(19).getStringCellValue());
                bankAccount.setSwift(row.getCell(20).getStringCellValue());

                if (hasAtLeastOneNonBlankStringField(bankAccount)) {
                    bankAccount = bankAccountRepository.save(bankAccount);
                    customer.setBankAccounts(Collections.singletonList(bankAccount));
                }

                Address address2 = new Address();
                address2.setStreet(row.getCell(26).getStringCellValue());
                address2.setZip(row.getCell(27).getStringCellValue());
                address2.setCity(row.getCell(28).getStringCellValue());
                address2.setCountryCode(row.getCell(29).getStringCellValue());
                address2.setCountry(row.getCell(30).getStringCellValue());

                if (hasAtLeastOneNonBlankStringField(address2)) {
                    address2 = addressRepository.save(address2);
                    customer.setCorrespondenceAddress(address2);
                }

                customer.setInvoiceDueDays((int) row.getCell(36).getNumericCellValue());

                LocalDateTime dateTime = row.getCell(37).getLocalDateTimeCellValue();
                customer.setCreatedAt(dateTime);

                Contact contact = new Contact();
                contact.setTitle(row.getCell(38).getStringCellValue());
                contact.setFirstName(row.getCell(39).getStringCellValue());
                contact.setLastName(row.getCell(40).getStringCellValue());
                contact.setTitleAfterName(row.getCell(41).getStringCellValue());
                contact.setPosition(row.getCell(42).getStringCellValue());
                contact.setEmail(row.getCell(43).getStringCellValue());
                contact.setPhone1(row.getCell(44).getStringCellValue());
                contact.setPhone2(row.getCell(45).getStringCellValue());
                contact.setOtherContact(row.getCell(46).getStringCellValue());
                contact.setNote(row.getCell(47).getStringCellValue());
                contact.setNewsletterSubscribed("Áno".equals(row.getCell(48).getStringCellValue()));

                if (hasAtLeastOneNonBlankStringField(contact)) {
                    customer.getContacts().add(contact);
                }

                Contact contact1 = new Contact();
                contact1.setTitle(row.getCell(49).getStringCellValue());
                contact1.setFirstName(row.getCell(50).getStringCellValue());
                contact1.setLastName(row.getCell(51).getStringCellValue());
                contact1.setTitleAfterName(row.getCell(52).getStringCellValue());
                contact1.setPosition(row.getCell(53).getStringCellValue());
                contact1.setEmail(row.getCell(54).getStringCellValue());
                contact1.setPhone1(row.getCell(55).getStringCellValue());
                contact1.setPhone2(row.getCell(56).getStringCellValue());
                contact1.setOtherContact(row.getCell(57).getStringCellValue());
                contact1.setNote(row.getCell(58).getStringCellValue());
                contact1.setNewsletterSubscribed("Áno".equals(row.getCell(59).getStringCellValue()));

                if (hasAtLeastOneNonBlankStringField(contact1)) {
                    customer.getContacts().add(contact1);
                }

                Contact contact2 = new Contact();
                contact2.setTitle(row.getCell(60).getStringCellValue());
                contact2.setFirstName(row.getCell(61).getStringCellValue());
                contact2.setLastName(row.getCell(62).getStringCellValue());
                contact2.setTitleAfterName(row.getCell(63).getStringCellValue());
                contact2.setPosition(row.getCell(64).getStringCellValue());
                contact2.setEmail(row.getCell(65).getStringCellValue());
                contact2.setPhone1(row.getCell(66).getStringCellValue());
                contact2.setPhone2(row.getCell(67).getStringCellValue());
                contact2.setOtherContact(row.getCell(68).getStringCellValue());
                contact2.setNote(row.getCell(69).getStringCellValue());
                contact2.setNewsletterSubscribed("Áno".equals(row.getCell(70).getStringCellValue()));

                if (hasAtLeastOneNonBlankStringField(contact2)) {
                    customer.getContacts().add(contact2);
                }

                Contact contact3 = new Contact();
                contact3.setTitle(row.getCell(71).getStringCellValue());
                contact3.setFirstName(row.getCell(73).getStringCellValue());
                contact3.setLastName(row.getCell(73).getStringCellValue());
                contact3.setTitleAfterName(row.getCell(74).getStringCellValue());
                contact3.setPosition(row.getCell(75).getStringCellValue());
                contact3.setEmail(row.getCell(76).getStringCellValue());
                contact3.setPhone1(row.getCell(77).getStringCellValue());
                contact3.setPhone2(row.getCell(78).getStringCellValue());
                contact3.setOtherContact(row.getCell(79).getStringCellValue());
                contact3.setNote(row.getCell(80).getStringCellValue());
                contact3.setNewsletterSubscribed("Áno".equals(row.getCell(81).getStringCellValue()));

                if (hasAtLeastOneNonBlankStringField(contact3)) {
                    customer.getContacts().add(contact3);
                }

                Contact contact4 = new Contact();
                contact4.setTitle(row.getCell(82).getStringCellValue());
                contact4.setFirstName(row.getCell(83).getStringCellValue());
                contact4.setLastName(row.getCell(84).getStringCellValue());
                contact4.setTitleAfterName(row.getCell(85).getStringCellValue());
                contact4.setPosition(row.getCell(86).getStringCellValue());
                contact4.setEmail(row.getCell(87).getStringCellValue());
                contact4.setPhone1(row.getCell(88).getStringCellValue());
                contact4.setPhone2(row.getCell(89).getStringCellValue());
                contact4.setOtherContact(row.getCell(90).getStringCellValue());
                contact4.setNote(row.getCell(91).getStringCellValue());
                contact4.setNewsletterSubscribed("Áno".equals(row.getCell(92).getStringCellValue()));

                if (hasAtLeastOneNonBlankStringField(contact4)) {
                    customer.getContacts().add(contact4);
                }

                Contact contact5 = new Contact();
                contact5.setTitle(row.getCell(93).getStringCellValue());
                contact5.setFirstName(row.getCell(94).getStringCellValue());
                contact5.setLastName(row.getCell(95).getStringCellValue());
                contact5.setTitleAfterName(row.getCell(96).getStringCellValue());
                contact5.setPosition(row.getCell(97).getStringCellValue());
                contact5.setEmail(row.getCell(98).getStringCellValue());
                contact5.setPhone1(row.getCell(99).getStringCellValue());
                contact5.setPhone2(row.getCell(100).getStringCellValue());
                contact5.setOtherContact(row.getCell(101).getStringCellValue());
                contact5.setNewsletterSubscribed("Áno".equals(row.getCell(102).getStringCellValue()));

                if (hasAtLeastOneNonBlankStringField(contact5)) {
                    customer.getContacts().add(contact5);
                }

                customer.setCustomerNumber(row.getCell(104).getStringCellValue());

                customers.add(customer);
            }

            // First save all customers to get their IDs
            List<Customer> savedCustomers = customerRepository.saveAll(customers);

            // Then save contacts with proper customer_id references
            for (Customer savedCustomer : savedCustomers) {
                List<Contact> contacts = savedCustomer.getContacts();
                for (Contact contact : contacts) {
                    // Set the customer reference to ensure customer_id is populated
                    contact.setCustomer(savedCustomer);
                    contactRepository.save(contact);
                }
            }

        } catch (IOException e) {
            log.error(e.getMessage());
        }
    }

    private boolean hasAtLeastOneNonBlankStringField(Object obj) {
        if (obj == null) {
            return false;
        }

        for (Field field : obj.getClass().getDeclaredFields()) {
            field.setAccessible(true);
            try {
                Object value = field.get(obj);
                if (value instanceof String && !((String) value).trim().isEmpty()) {
                    return true;
                }
            } catch (IllegalAccessException e) {
                log.error(e.getMessage());
            }
        }

        return false;
    }

    public void parseProducts(final String pathToFile) {
        try (InputStream inputStream = new FileInputStream(pathToFile);
             Workbook workbook = WorkbookFactory.create(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);
            List<Product> products = new ArrayList<>();
            boolean isFirst = true;

            for (Row row : sheet) {
                if (isFirst) {
                    isFirst = false;
                    continue;
                }

                if (row.getCell(0) == null || row.getCell(0).toString().isEmpty()) {
                    break;
                }

                Product product = new Product();
                product.setNumber(getCellValueAsString(row, 0));
                product.setName(getCellValueAsString(row, 1));
                product.setUnit(getCellValueAsString(row, 2));
                product.setType(getCellValueAsString(row, 3));
                product.setDisplayType(getCellValueAsString(row, 4));
                product.setEan(getCellValueAsString(row, 5));
                product.setSellingPriceWithoutVat(getCellValueAsBigDecimal(row, 6));
                product.setSellingPriceWithVat(getCellValueAsBigDecimal(row, 7));
                product.setBuyingPriceWithoutVat(getCellValueAsBigDecimal(row, 8));
                product.setBuyingPriceWithVat(getCellValueAsBigDecimal(row, 9));
                product.setVatRate(getCellValueAsBigDecimal(row, 10));
                product.setTradeMargin(getCellValueAsBigDecimal(row, 11));
                product.setCurrency(getCellValueAsString(row, 12));
                product.setAdditionalInvoiceText(getCellValueAsString(row, 13));
                product.setManufacturer(getCellValueAsString(row, 14));
                product.setCategory(getCellValueAsString(row, 15));
                product.setSubcategory(getCellValueAsString(row, 16));

                products.add(product);
            }

            productRepository.saveAll(products);

        } catch (IOException e) {
            log.error(e.getMessage());
        }
    }

    public void parseDocuments(final String pathToFile) {
        try (InputStream inputStream = new FileInputStream(pathToFile);
             Workbook workbook = WorkbookFactory.create(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);
            List<Invoice> invoices = new ArrayList<>();
            boolean isFirst = true;

            for (Row row : sheet) {
                if (isFirst) {
                    isFirst = false;
                    continue;
                }

                if (row.getCell(0) == null || row.getCell(0).toString().isEmpty()) {
                    break;
                }

                Invoice invoice = new Invoice();
                invoice.setNumber(getCellValueAsString(row, 0));
                invoice.setType(getCellValueAsString(row, 1));
                invoice.setIssueDate(getCellValueAsLocalDate(row, 2));
                invoice.setOrderDeliveryDate(getCellValueAsLocalDate(row, 3));
                invoice.setDeliveryDate(getCellValueAsLocalDate(row, 4));
                invoice.setDueDate(getCellValueAsLocalDate(row, 5));
                invoice.setPartnerName(getCellValueAsString(row, 6));
                invoice.setPartnerNumber(getCellValueAsString(row, 7));

                // Address
                Address address = new Address();
                address.setStreet(getCellValueAsString(row, 8));
                address.setZip(getCellValueAsString(row, 9));
                address.setCity(getCellValueAsString(row, 10));
                address.setCountryCode(getCellValueAsString(row, 11));
                address.setCountry(getCellValueAsString(row, 12));

                if (hasAtLeastOneNonBlankStringField(address)) {
                    address = addressRepository.save(address);
                    invoice.setAddress(address);
                }

                invoice.setIco(getCellValueAsString(row, 13));
                invoice.setDic(getCellValueAsString(row, 14));
                invoice.setIcDph(getCellValueAsString(row, 15));
                invoice.setTotal(getCellValueAsBigDecimal(row, 16));
                invoice.setTotalWithoutVat(getCellValueAsBigDecimal(row, 17));
                invoice.setVatBaseHigh(getCellValueAsBigDecimal(row, 18));
                invoice.setVatBaseNone(getCellValueAsBigDecimal(row, 19));
                invoice.setVatRateHigh(getCellValueAsBigDecimal(row, 20));
                invoice.setVatAmountHigh(getCellValueAsBigDecimal(row, 21));
                invoice.setCurrency(getCellValueAsString(row, 22));
                invoice.setPaymentMethod(getCellValueAsString(row, 23));

                invoices.add(invoice);
            }

            invoiceRepository.saveAll(invoices);

        } catch (IOException e) {
            log.error(e.getMessage());
        }
    }

    public void parseBusinessCases(final String pathToFile) {
        try (InputStream inputStream = new FileInputStream(pathToFile);
             Workbook workbook = WorkbookFactory.create(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);
            List<BusinessCase> businessCases = new ArrayList<>();
            boolean isFirst = true;

            for (Row row : sheet) {
                if (isFirst) {
                    isFirst = false;
                    continue;
                }

                if (row.getCell(0) == null || row.getCell(0).toString().isEmpty()) {
                    break;
                }

                BusinessCase businessCase = new BusinessCase();
                businessCase.setCreatedAt(getCellValueAsLocalDateTime(row, 0));
                businessCase.setContactSource(getCellValueAsString(row, 1));
                businessCase.setNumber(getCellValueAsString(row, 2));
                businessCase.setName(getCellValueAsString(row, 3));
                businessCase.setDescription(getCellValueAsString(row, 4));
                businessCase.setTotalPrice(getCellValueAsBigDecimal(row, 5));
                businessCase.setStatus(getCellValueAsString(row, 6));
                businessCase.setResponsible(getCellValueAsString(row, 7));
                businessCase.setDealType(getCellValueAsString(row, 8));
                businessCase.setDealSubtype(getCellValueAsString(row, 9));
                businessCase.setExpectedClosureDate(getCellValueAsLocalDate(row, 10));
                businessCase.setClosureDate(getCellValueAsLocalDate(row, 11));
                businessCase.setFailureReason(getCellValueAsString(row, 12));
                businessCase.setFailureNote(getCellValueAsString(row, 13));

                // Try to find customer by name
                String customerName = getCellValueAsString(row, 15);
                if (!customerName.isEmpty()) {
                    Customer customer = customerRepository.findAll().stream()
                        .filter(c -> c.getName().equals(customerName))
                        .findFirst()
                        .orElse(null);
                    businessCase.setCustomer(customer);
                }

                businessCases.add(businessCase);
            }

            businessCaseRepository.saveAll(businessCases);

        } catch (IOException e) {
            log.error(e.getMessage());
        }
    }

    public void parseEvents(final String pathToFile) {
        try (InputStream inputStream = new FileInputStream(pathToFile);
             Workbook workbook = WorkbookFactory.create(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);
            List<BusinessEvent> events = new ArrayList<>();
            boolean isFirst = true;

            // Pre-load all event types to avoid repeated queries
            List<EventType> allEventTypes = eventTypeRepository.findAll();
            for (EventType type : allEventTypes) {
                eventTypeCache.put(type.getName(), type);
            }

            // Pre-load all event categories
            List<EventCategory> allEventCategories = eventCategoryRepository.findAll();
            for (EventCategory category : allEventCategories) {
                eventCategoryCache.put(category.getName(), category);
            }

            // Pre-load all customers
            List<Customer> allCustomers = customerRepository.findAll();
            for (Customer customer : allCustomers) {
                customerCache.put(customer.getName(), customer);
            }
            
            // Pre-load all users
            List<User> allUsers = userRepository.findAll();
            for (User user : allUsers) {
                userCache.put(user.getFullName(), user);
            }

            for (Row row : sheet) {
                if (isFirst) {
                    isFirst = false;
                    continue;
                }

                BusinessEvent event = new BusinessEvent();

                // Event type with cache
                String eventTypeName = getCellValueAsString(row, 0);
                if (!eventTypeName.isEmpty()) {
                    EventType eventType = eventTypeCache.get(eventTypeName);
                    if (eventType == null) {
                        eventType = new EventType();
                        eventType.setName(eventTypeName);
                        eventType = eventTypeRepository.save(eventType);
                        eventTypeCache.put(eventTypeName, eventType);
                    }
                    event.setEventType(eventType);
                }

                // Event category with cache
                String eventCategoryName = getCellValueAsString(row, 1);
                if (!eventCategoryName.isEmpty()) {
                    EventCategory eventCategory = eventCategoryCache.get(eventCategoryName);
                    if (eventCategory == null) {
                        eventCategory = new EventCategory();
                        eventCategory.setName(eventCategoryName);
                        eventCategory = eventCategoryRepository.save(eventCategory);
                        eventCategoryCache.put(eventCategoryName, eventCategory);
                    }
                    event.setEventCategory(eventCategory);
                }

                event.setDirection(getCellValueAsString(row, 2));

                // Customer with cache
                String customerName = getCellValueAsString(row, 3);
                if (!customerName.isEmpty()) {
                    Customer customer = customerCache.get(customerName);
                    if (customer != null) {
                        event.setCustomer(customer);
                    }
                }
                
                // User with cache
                String userName = getCellValueAsString(row, 4);
                if (!userName.isEmpty()) {
                    User user = userCache.get(userName);
                    if (user == null) {
                        // Try to find by splitting the name
                        String[] nameParts = userName.split(" ");
                        if (nameParts.length > 1) {
                            String firstName = nameParts[0];
                            String lastName = nameParts[1];
                            user = userRepository.findByFullName(firstName, lastName);
                            if (user != null) {
                                userCache.put(userName, user);
                            }
                        }
                    }
                    event.setUser(user);
                }

                // Add other fields as needed
                event.setScheduledFrom(getCellValueAsLocalDateTime(row, 5));
                event.setScheduledTo(getCellValueAsLocalDateTime(row, 6));
                event.setRealizationDate(getCellValueAsLocalDateTime(row, 7));
                event.setNote(getCellValueAsString(row, 8));
                event.setStatus(ProgressStatus.FINISHED);

                events.add(event);
            }

            businessEventRepository.saveAll(events);

        } catch (IOException e) {
            log.error(e.getMessage());
        }
    }

    public void parseTasks(final String pathToFile) {
        try (InputStream inputStream = new FileInputStream(pathToFile);
             Workbook workbook = WorkbookFactory.create(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);
            List<Task> tasks = new ArrayList<>();
            boolean isFirst = true;

            for (Row row : sheet) {
                if (isFirst) {
                    isFirst = false;
                    continue;
                }

                if (row.getCell(0) == null || row.getCell(0).toString().isEmpty()) {
                    break;
                }

                Task task = new Task();
                task.setTitle(getCellValueAsString(row, 1));
                task.setDescription(getCellValueAsString(row, 2));

                // Find executor by name
                String executorName = getCellValueAsString(row, 3);
                if (!executorName.isEmpty()) {
                    User executor = userRepository.findAll().stream()
                        .filter(u -> u.getFullName().equals(executorName))
                        .findFirst()
                        .orElse(null);
                    task.setUser(executor);
                }

                task.setCreatedAt(getCellValueAsLocalDateTime(row, 15));

                // Handle category
                String categoryName = getCellValueAsString(row, 20);
                if (!categoryName.isEmpty()) {
                    Category category = categoryRepository.findByName(categoryName);
                    if (category == null) {
                        category = new Category();
                        category.setName(categoryName);
                        category = categoryRepository.save(category);
                    }
                    task.setCategory(category);
                }

                tasks.add(task);
            }

            taskRepository.saveAll(tasks);

        } catch (IOException e) {
            log.error(e.getMessage());
        }
    }

    public void parseContracts(final String pathToFile) {
        try (InputStream inputStream = new FileInputStream(pathToFile);
             Workbook workbook = WorkbookFactory.create(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);
            List<Contract> contracts = new ArrayList<>();
            boolean isFirst = true;

            for (Row row : sheet) {
                if (isFirst) {
                    isFirst = false;
                    continue;
                }

                if (row.getCell(0) == null || row.getCell(0).toString().isEmpty()) {
                    break;
                }

                Contract contract = new Contract();
                contract.setNumber(getCellValueAsString(row, 1));
                contract.setName(getCellValueAsString(row, 2));
                contract.setDescriptionAndRequirements(getCellValueAsString(row, 3));
                contract.setAgreedPrice(getCellValueAsBigDecimal(row, 4));
                contract.setTradeMargin(getCellValueAsBigDecimal(row, 5));
                contract.setTradeMarginPercent(getCellValueAsBigDecimal(row, 6).doubleValue());
                contract.setInvoiced(getCellValueAsBigDecimal(row, 7));
                contract.setUninvoicedPayments(getCellValueAsBigDecimal(row, 8));
                contract.setRemainingToInvoice(getCellValueAsBigDecimal(row, 9));
                contract.setEstimatedDuration(getCellValueAsString(row, 10));
                contract.setActualDuration(getCellValueAsString(row, 11));
                contract.setStatus(getCellValueAsString(row, 12));
                contract.setResponsible(getCellValueAsString(row, 13));
                contract.setType(getCellValueAsString(row, 14));
                contract.setSubtype(getCellValueAsString(row, 15));
                contract.setCompletionDeadline(getCellValueAsLocalDate(row, 16));
                contract.setCompletionDate(getCellValueAsLocalDate(row, 17));
                contract.setDeliveryDate(getCellValueAsLocalDate(row, 18));

                // Find customer by name
                String customerName = getCellValueAsString(row, 21);
                if (!customerName.isEmpty()) {
                    Customer customer = customerRepository.findAll().stream()
                        .filter(c -> c.getName().equals(customerName))
                        .findFirst()
                        .orElse(null);
                    contract.setCustomer(customer);
                }

                contracts.add(contract);
            }

            contractRepository.saveAll(contracts);

        } catch (IOException e) {
            log.error(e.getMessage());
        }
    }

    private String getCellValueAsString(Row row, int cellIndex) {
        try {
            Cell cell = row.getCell(cellIndex);
            if (cell == null) return "";
            return cell.toString().trim();
        } catch (Exception e) {
            return "";
        }
    }

    private BigDecimal getCellValueAsBigDecimal(Row row, int cellIndex) {
        try {
            Cell cell = row.getCell(cellIndex);
            if (cell == null) return BigDecimal.ZERO;
            return BigDecimal.valueOf(cell.getNumericCellValue());
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }

    private LocalDate getCellValueAsLocalDate(Row row, int cellIndex) {
        try {
            Cell cell = row.getCell(cellIndex);
            if (cell == null) return null;
            return cell.getLocalDateTimeCellValue().toLocalDate();
        } catch (Exception e) {
            return null;
        }
    }

    private LocalDateTime getCellValueAsLocalDateTime(Row row, int cellIndex) {
        try {
            Cell cell = row.getCell(cellIndex);
            if (cell == null) return null;
            return cell.getLocalDateTimeCellValue();
        } catch (Exception e) {
            return null;
        }
    }
}
