package com.solax.triple_tea.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Contact {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String title;
    private String titleAfterName;
    private String firstName;
    private String lastName;
    private String suffix;
    private String position;
    private String email;
    private String phone1;
    private String phone2;
    private String otherContact;
    private String note;
    private Boolean mainContact;
    private boolean newsletterSubscribed;

    @ManyToOne
    @JoinColumn(name = "customer_id")
    private Customer customer;
}
