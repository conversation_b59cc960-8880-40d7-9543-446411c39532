package com.solax.triple_tea.entity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import org.springframework.data.annotation.CreatedDate;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Quote extends HistoryOverview {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @CreatedDate
    @Column(updatable = false)
    private LocalDateTime createdAt;

    private String number;
    private LocalDate issueDate;
    private LocalDate validUntilDate;

    private BigDecimal total;
    private BigDecimal totalWithoutVat;
    private BigDecimal vatBaseHigh;
    private BigDecimal vatBaseNone;
    private BigDecimal vatRateHigh;
    private BigDecimal vatAmountHigh;

    private String currency;
    private String orderNumber;
    private String taxDocNumberForAdvance;
    private String internalNote;

    @ManyToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "customer_id")
    private Customer customer;

    @ManyToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "created_by_id")
    private User createdBy;
}
