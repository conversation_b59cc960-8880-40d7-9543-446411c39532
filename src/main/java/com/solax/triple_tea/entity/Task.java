package com.solax.triple_tea.entity;

import java.time.Duration;
import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;

import org.springframework.data.annotation.CreatedDate;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;


@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class Task extends HistoryOverview {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @CreatedDate
    @Column(updatable = false)
    private LocalDateTime createdAt;

    @ManyToOne
    @JoinColumn(name = "created_by_id")
    private User createdBy;

    @ManyToOne
    @JoinColumn(name = "category_id")
    private Category category;

    @ManyToOne
    @JoinColumn(name = "customer_id")
    private Customer customer;

    @Column
    private String title;

    @Column(columnDefinition = "TEXT")
    private String description;

    @Column
    private LocalDateTime startedAt;

    @Column
    private LocalDateTime endedAt;

    @Column
    private LocalDateTime pausedAt;

    @Column
    private Long totalPausedDurationSeconds = 0L;

    @Column
    private Boolean isCurrentlyPaused = false;

    @Column
    private LocalDateTime archivedAt;

    @Column
    private Long actualDurationHours;

    @Column
    private Integer priority;

    /**
     * Calculate total active duration excluding paused time
     * @return Duration in seconds, or 0 if task hasn't started
     */
    public Long getTotalActiveDurationSeconds() {
        if (startedAt == null) {
            return 0L;
        }

        LocalDateTime endTime = endedAt != null ? endedAt : LocalDateTime.now();

        // If currently paused, don't count time since pause started
        if (isCurrentlyPaused != null && isCurrentlyPaused && pausedAt != null) {
            endTime = pausedAt;
        }

        long totalSeconds = Duration.between(startedAt, endTime).getSeconds();
        long pausedSeconds = totalPausedDurationSeconds != null ? totalPausedDurationSeconds : 0L;

        return Math.max(0L, totalSeconds - pausedSeconds);
    }

    /**
     * Get total active duration in hours for display
     */
    public Double getTotalActiveDurationHours() {
        return getTotalActiveDurationSeconds() / 3600.0;
    }

    /**
     * Check if task is currently running (started but not ended and not paused)
     */
    public boolean isCurrentlyRunning() {
        return startedAt != null && endedAt == null &&
               (isCurrentlyPaused == null || !isCurrentlyPaused);
    }

    /**
     * Check if task is finished
     */
    public boolean isFinished() {
        return endedAt != null;
    }
}
