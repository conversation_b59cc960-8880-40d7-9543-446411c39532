package com.solax.triple_tea.entity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import org.springframework.data.annotation.CreatedDate;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BusinessCase {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @CreatedDate
    @Column(updatable = false)
    private LocalDateTime createdAt;

    private String contactSource;
    private String number;
    private String name;
    private String description;
    private BigDecimal totalPrice;
    private String status;
    private String responsible;
    private String dealType;
    private String dealSubtype;

    private LocalDate expectedClosureDate;
    private LocalDate closureDate;
    private String failureReason;
    private String failureNote;

    @ManyToOne
    @JoinColumn(name = "customer_id")
    private Customer customer;
}
