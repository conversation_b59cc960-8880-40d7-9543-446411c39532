package com.solax.triple_tea.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
public class User {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column
    private String firstName;

    @Column
    private String lastName;

    @Column(unique = true)
    private String email;

    @Column
    private boolean enabled = true;

    @Column
    private String password;

    @Column(name = "can_view_all_work_hours", nullable = false)
    private boolean canViewAllWorkHours = false;

    public String getFullName() {
        return firstName + " " + lastName;
    }
}
