package com.solax.triple_tea.entity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import org.springframework.data.annotation.CreatedDate;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Contract {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @CreatedDate
    @Column(updatable = false)
    private LocalDateTime createdAt;

    private String number;
    private String name;
    private String descriptionAndRequirements;
    private BigDecimal agreedPrice;
    private BigDecimal tradeMargin;
    private Double tradeMarginPercent;
    private BigDecimal invoiced;
    private BigDecimal uninvoicedPayments;
    private BigDecimal remainingToInvoice;
    private String estimatedDuration;  // format hh:mm
    private String actualDuration;     // format hh:mm
    private String status;
    private String responsible;
    private String type;
    private String subtype;
    private LocalDate completionDeadline;
    private LocalDate completionDate;
    private LocalDate deliveryDate;
    private Integer overdueDays;
    private String businessCase;

    @ManyToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "customer_id")
    private Customer customer;
}
