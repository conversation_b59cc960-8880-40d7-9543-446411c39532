package com.solax.triple_tea.entity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import org.springframework.data.annotation.CreatedDate;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Invoice extends HistoryOverview {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @CreatedDate
    @Column(updatable = false)
    private LocalDateTime createdAt;

    private String number;
    private String type;

    private LocalDate issueDate;
    private LocalDate orderDeliveryDate;
    private LocalDate deliveryDate;
    private LocalDate dueDate;
    private LocalDate paymentDate;

    private String partnerName;
    private String partnerNumber;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "address_id", referencedColumnName = "id")
    private Address address;

    private String ico;
    private String dic;
    private String icDph;

    private BigDecimal total;
    private BigDecimal totalWithoutVat;
    private BigDecimal vatBaseHigh;
    private BigDecimal vatBaseNone;
    private BigDecimal vatRateHigh;
    private BigDecimal vatAmountHigh;

    private String currency;
    private String paymentMethod;

    private String accountNumber;
    private String iban;
    private String swift;

    private String variableSymbol;
    private String specificSymbol;
    private String constantSymbol;

    private String orderNumber;
    private String company;
    private String issuedBy;

    private BigDecimal paidAdvance;
    private BigDecimal paidAdvanceWithoutVat;
    private BigDecimal totalExcludingAdvance;
    private BigDecimal totalExcludingAdvanceWithoutVat;
    private BigDecimal remainingToPay;

    private String taxDocNumberForAdvance;

    private String internalNote;

    private String category;
    private String subcategory;
    private String projectNumber;
    private String department;

    private String introText;

    @ManyToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "customer_id")
    private Customer customer;
}
