package com.solax.triple_tea.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import org.springframework.data.annotation.CreatedDate;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "products")
public class Product {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @CreatedDate
    @Column(updatable = false)
    private LocalDateTime createdAt;

    private String number;
    private String name;
    private String unit;
    private String type; // tovar/služba
    private String displayType; // typ zobrazenia
    private String ean;
    
    private BigDecimal sellingPriceWithoutVat;
    private BigDecimal sellingPriceWithVat;
    private BigDecimal buyingPriceWithoutVat;
    private BigDecimal buyingPriceWithVat;
    
    private BigDecimal vatRate;
    private BigDecimal tradeMargin;
    
    private String currency;
    
    @Column(length = 1000)
    private String additionalInvoiceText;
    
    private String manufacturer;
    private String category;
    private String subcategory;
}
