package com.solax.triple_tea.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.solax.triple_tea.repository.InvoiceRepository;
import com.solax.triple_tea.service.impl.NumberGenerationServiceImpl;

@ExtendWith(MockitoExtension.class)
class NumberGenerationServiceTest {

    @Mock
    private InvoiceRepository invoiceRepository;

    private NumberGenerationService numberGenerationService;

    @BeforeEach
    void setUp() {
        numberGenerationService = new NumberGenerationServiceImpl(invoiceRepository);
    }

    @Test
    void testGenerateFirstInvoiceNumberOfMonth() {
        // Given: No existing invoices for current month
        when(invoiceRepository.findLatestNumberByPattern(anyString())).thenReturn(Optional.empty());

        // When: Generate number
        String result = numberGenerationService.generateNextInvoiceNumber();

        // Then: Should be yyMM0001 format
        String expectedPrefix = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMM"));
        assertEquals(expectedPrefix + "0001", result);
    }

    @Test
    void testGenerateSecondInvoiceNumberOfMonth() {
        // Given: One existing invoice for current month
        String currentYearMonth = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMM"));
        String existingNumber = currentYearMonth + "0001";
        when(invoiceRepository.findLatestNumberByPattern(anyString())).thenReturn(Optional.of(existingNumber));

        // When: Generate number
        String result = numberGenerationService.generateNextInvoiceNumber();

        // Then: Should increment to 0002
        assertEquals(currentYearMonth + "0002", result);
    }

    @Test
    void testGenerateInvoiceNumberAfterMultipleInvoices() {
        // Given: Multiple existing invoices for current month
        String currentYearMonth = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMM"));
        String existingNumber = currentYearMonth + "0099";
        when(invoiceRepository.findLatestNumberByPattern(anyString())).thenReturn(Optional.of(existingNumber));

        // When: Generate number
        String result = numberGenerationService.generateNextInvoiceNumber();

        // Then: Should increment to 0100
        assertEquals(currentYearMonth + "0100", result);
    }

    @Test
    void testGenerateInvoiceNumberWithInvalidExistingNumber() {
        // Given: Invalid existing number format
        when(invoiceRepository.findLatestNumberByPattern(anyString())).thenReturn(Optional.of("invalid"));

        // When: Generate number
        String result = numberGenerationService.generateNextInvoiceNumber();

        // Then: Should fallback to 0001
        String expectedPrefix = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMM"));
        assertEquals(expectedPrefix + "0001", result);
    }
}
